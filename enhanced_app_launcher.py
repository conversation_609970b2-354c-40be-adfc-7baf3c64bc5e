#!/usr/bin/env python3
"""
Enhanced App Launcher for Agent-S
Provides robust, fast, and reliable application launching with multiple fallback methods
"""

import os
import sys
import time
import subprocess
import winreg
import psutil
import pyautogui
from pathlib import Path
from typing import Dict, List, Optional, Tuple
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class EnhancedAppLauncher:
    """Enhanced application launcher with intelligent detection and multiple launch methods"""
    
    def __init__(self):
        self.app_registry = {}
        self.common_paths = [
            r"C:\Program Files",
            r"C:\Program Files (x86)",
            r"C:\Windows\System32",
            r"C:\Users\<USER>\AppData\Local".format(os.getenv('USERNAME', '')),
            r"C:\Users\<USER>\AppData\Roaming".format(os.getenv('USERNAME', '')),
        ]
        self.launch_stats = {
            'total_launches': 0,
            'successful_launches': 0,
            'failed_launches': 0,
            'average_time': 0.0
        }
        
        # Initialize app registry
        self._build_app_registry()
    
    def _build_app_registry(self):
        """Build comprehensive app registry from multiple sources"""
        logger.info("🔍 Building application registry...")
        
        # Common applications with multiple launch methods
        self.app_registry = {
            'calculator': {
                'commands': ['calc', 'calculator'],
                'paths': [r'C:\Windows\System32\calc.exe'],
                'display_name': 'Calculator',
                'category': 'utility'
            },
            'notepad': {
                'commands': ['notepad', 'notepad.exe'],
                'paths': [r'C:\Windows\System32\notepad.exe'],
                'display_name': 'Notepad',
                'category': 'editor'
            },
            'excel': {
                'commands': ['excel', 'excel.exe', 'Microsoft Excel'],
                'paths': [],  # Will be detected
                'display_name': 'Microsoft Excel',
                'category': 'office'
            },
            'word': {
                'commands': ['winword', 'word', 'Microsoft Word'],
                'paths': [],
                'display_name': 'Microsoft Word',
                'category': 'office'
            },
            'powerpoint': {
                'commands': ['powerpnt', 'powerpoint', 'Microsoft PowerPoint'],
                'paths': [],
                'display_name': 'Microsoft PowerPoint',
                'category': 'office'
            },
            'outlook': {
                'commands': ['outlook', 'Microsoft Outlook'],
                'paths': [],
                'display_name': 'Microsoft Outlook',
                'category': 'office'
            },
            'chrome': {
                'commands': ['chrome', 'google chrome'],
                'paths': [
                    r'C:\Program Files\Google\Chrome\Application\chrome.exe',
                    r'C:\Program Files (x86)\Google\Chrome\Application\chrome.exe'
                ],
                'display_name': 'Google Chrome',
                'category': 'browser'
            },
            'firefox': {
                'commands': ['firefox', 'mozilla firefox'],
                'paths': [
                    r'C:\Program Files\Mozilla Firefox\firefox.exe',
                    r'C:\Program Files (x86)\Mozilla Firefox\firefox.exe'
                ],
                'display_name': 'Mozilla Firefox',
                'category': 'browser'
            },
            'edge': {
                'commands': ['msedge', 'edge', 'microsoft edge'],
                'paths': [r'C:\Program Files (x86)\Microsoft\Edge\Application\msedge.exe'],
                'display_name': 'Microsoft Edge',
                'category': 'browser'
            },
            'cmd': {
                'commands': ['cmd', 'command prompt'],
                'paths': [r'C:\Windows\System32\cmd.exe'],
                'display_name': 'Command Prompt',
                'category': 'system'
            },
            'powershell': {
                'commands': ['powershell', 'windows powershell'],
                'paths': [r'C:\Windows\System32\WindowsPowerShell\v1.0\powershell.exe'],
                'display_name': 'Windows PowerShell',
                'category': 'system'
            }
        }
        
        # Detect Office applications
        self._detect_office_apps()
        
        # Scan for additional applications
        self._scan_installed_apps()
        
        logger.info(f"✅ Registry built with {len(self.app_registry)} applications")
    
    def _detect_office_apps(self):
        """Detect Microsoft Office applications"""
        office_paths = [
            r'C:\Program Files\Microsoft Office',
            r'C:\Program Files (x86)\Microsoft Office',
            r'C:\Program Files\Microsoft Office 365',
            r'C:\Program Files (x86)\Microsoft Office 365'
        ]
        
        for base_path in office_paths:
            if os.path.exists(base_path):
                for root, dirs, files in os.walk(base_path):
                    for file in files:
                        if file.lower() in ['excel.exe', 'winword.exe', 'powerpnt.exe', 'outlook.exe']:
                            app_name = file.lower().replace('.exe', '')
                            if app_name == 'winword':
                                app_name = 'word'
                            elif app_name == 'powerpnt':
                                app_name = 'powerpoint'
                            
                            if app_name in self.app_registry:
                                full_path = os.path.join(root, file)
                                if full_path not in self.app_registry[app_name]['paths']:
                                    self.app_registry[app_name]['paths'].append(full_path)
    
    def _scan_installed_apps(self):
        """Scan Windows registry for installed applications"""
        try:
            # Check Windows registry for installed programs
            registry_paths = [
                r"SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall",
                r"SOFTWARE\WOW6432Node\Microsoft\Windows\CurrentVersion\Uninstall"
            ]
            
            for reg_path in registry_paths:
                try:
                    with winreg.OpenKey(winreg.HKEY_LOCAL_MACHINE, reg_path) as key:
                        for i in range(winreg.QueryInfoKey(key)[0]):
                            try:
                                subkey_name = winreg.EnumKey(key, i)
                                with winreg.OpenKey(key, subkey_name) as subkey:
                                    try:
                                        display_name = winreg.QueryValueEx(subkey, "DisplayName")[0]
                                        install_location = winreg.QueryValueEx(subkey, "InstallLocation")[0]
                                        
                                        # Add popular applications to registry
                                        self._add_detected_app(display_name, install_location)
                                    except FileNotFoundError:
                                        continue
                            except OSError:
                                continue
                except OSError:
                    continue
        except Exception as e:
            logger.warning(f"Registry scan failed: {e}")
    
    def _add_detected_app(self, display_name: str, install_location: str):
        """Add detected application to registry"""
        if not display_name or not install_location:
            return
        
        # Common application patterns
        app_patterns = {
            'visual studio code': 'vscode',
            'vs code': 'vscode',
            'adobe acrobat': 'acrobat',
            'vlc media player': 'vlc',
            'spotify': 'spotify',
            'discord': 'discord',
            'slack': 'slack',
            'zoom': 'zoom',
            'teams': 'teams'
        }
        
        display_lower = display_name.lower()
        for pattern, key in app_patterns.items():
            if pattern in display_lower and key not in self.app_registry:
                # Find executable in install location
                exe_files = []
                if os.path.exists(install_location):
                    for root, dirs, files in os.walk(install_location):
                        for file in files:
                            if file.endswith('.exe') and not file.startswith('unins'):
                                exe_files.append(os.path.join(root, file))
                
                if exe_files:
                    self.app_registry[key] = {
                        'commands': [key, display_name.lower()],
                        'paths': exe_files[:3],  # Limit to first 3 executables
                        'display_name': display_name,
                        'category': 'application'
                    }
                break
    
    def find_app(self, app_name: str) -> Optional[Dict]:
        """Find application in registry by name or alias"""
        app_name = app_name.lower().strip()
        
        # Direct match
        if app_name in self.app_registry:
            return self.app_registry[app_name]
        
        # Search by command aliases
        for key, app_info in self.app_registry.items():
            if app_name in [cmd.lower() for cmd in app_info['commands']]:
                return app_info
        
        # Fuzzy search by display name
        for key, app_info in self.app_registry.items():
            if app_name in app_info['display_name'].lower():
                return app_info
        
        return None
    
    def is_app_running(self, app_name: str) -> bool:
        """Check if application is already running"""
        app_info = self.find_app(app_name)
        if not app_info:
            return False
        
        # Check running processes
        for proc in psutil.process_iter(['pid', 'name', 'exe']):
            try:
                proc_name = proc.info['name'].lower()
                proc_exe = proc.info['exe']
                
                # Check by process name
                for cmd in app_info['commands']:
                    if cmd.lower() in proc_name or proc_name.startswith(cmd.lower()):
                        return True
                
                # Check by executable path
                if proc_exe:
                    for path in app_info['paths']:
                        if path and os.path.normpath(proc_exe.lower()) == os.path.normpath(path.lower()):
                            return True
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                continue
        
        return False

    def launch_app(self, app_name: str, force_new: bool = False) -> Tuple[bool, str, float]:
        """
        Launch application using multiple fallback methods

        Args:
            app_name: Name of application to launch
            force_new: Force new instance even if app is running

        Returns:
            Tuple of (success, method_used, execution_time)
        """
        start_time = time.time()
        self.launch_stats['total_launches'] += 1

        # Check if app is already running
        if not force_new and self.is_app_running(app_name):
            logger.info(f"✅ {app_name} is already running")
            execution_time = time.time() - start_time
            self.launch_stats['successful_launches'] += 1
            self._update_average_time(execution_time)
            return True, "already_running", execution_time

        # Find app in registry
        app_info = self.find_app(app_name)
        if not app_info:
            logger.warning(f"❌ Application '{app_name}' not found in registry")
            self.launch_stats['failed_launches'] += 1
            return False, "not_found", time.time() - start_time

        logger.info(f"🚀 Launching {app_info['display_name']}...")

        # Try multiple launch methods in order of reliability
        launch_methods = [
            self._launch_by_path,
            self._launch_by_command,
            self._launch_by_run_dialog,
            self._launch_by_start_menu,
            self._launch_by_powershell
        ]

        for method in launch_methods:
            try:
                success, method_name = method(app_info, app_name)
                if success:
                    execution_time = time.time() - start_time
                    logger.info(f"✅ Launched {app_info['display_name']} via {method_name} in {execution_time:.2f}s")
                    self.launch_stats['successful_launches'] += 1
                    self._update_average_time(execution_time)
                    return True, method_name, execution_time
            except Exception as e:
                logger.debug(f"Launch method {method.__name__} failed: {e}")
                continue

        # All methods failed
        execution_time = time.time() - start_time
        logger.error(f"❌ Failed to launch {app_name} after trying all methods")
        self.launch_stats['failed_launches'] += 1
        return False, "all_methods_failed", execution_time

    def _launch_by_path(self, app_info: Dict, app_name: str) -> Tuple[bool, str]:
        """Launch application by direct executable path"""
        for path in app_info['paths']:
            if path and os.path.exists(path):
                try:
                    subprocess.Popen([path], shell=False)
                    time.sleep(0.5)  # Brief wait to verify launch
                    return True, "direct_path"
                except Exception as e:
                    logger.debug(f"Direct path launch failed for {path}: {e}")
                    continue
        return False, "direct_path"

    def _launch_by_command(self, app_info: Dict, app_name: str) -> Tuple[bool, str]:
        """Launch application by command name"""
        for command in app_info['commands']:
            try:
                subprocess.Popen(command, shell=True)
                time.sleep(0.5)
                return True, "command"
            except Exception as e:
                logger.debug(f"Command launch failed for {command}: {e}")
                continue
        return False, "command"

    def _launch_by_run_dialog(self, app_info: Dict, app_name: str) -> Tuple[bool, str]:
        """Launch application via Windows Run dialog (Win+R)"""
        try:
            # Use the first command as it's usually the most reliable
            command = app_info['commands'][0] if app_info['commands'] else app_name

            pyautogui.hotkey('win', 'r')
            time.sleep(0.2)
            pyautogui.typewrite(command)
            time.sleep(0.1)
            pyautogui.press('enter')
            time.sleep(0.5)
            return True, "run_dialog"
        except Exception as e:
            logger.debug(f"Run dialog launch failed: {e}")
            return False, "run_dialog"

    def _launch_by_start_menu(self, app_info: Dict, app_name: str) -> Tuple[bool, str]:
        """Launch application via Start menu search"""
        try:
            # Use display name for better search results
            search_term = app_info['display_name']

            pyautogui.press('win')
            time.sleep(0.3)
            pyautogui.typewrite(search_term)
            time.sleep(0.8)  # Wait for search results
            pyautogui.press('enter')
            time.sleep(0.5)
            return True, "start_menu"
        except Exception as e:
            logger.debug(f"Start menu launch failed: {e}")
            return False, "start_menu"

    def _launch_by_powershell(self, app_info: Dict, app_name: str) -> Tuple[bool, str]:
        """Launch application via PowerShell"""
        try:
            # Try with the first available path or command
            target = None
            if app_info['paths']:
                for path in app_info['paths']:
                    if path and os.path.exists(path):
                        target = f'"{path}"'
                        break

            if not target and app_info['commands']:
                target = app_info['commands'][0]

            if target:
                ps_command = f'Start-Process {target}'
                subprocess.Popen(['powershell', '-Command', ps_command], shell=False)
                time.sleep(0.5)
                return True, "powershell"
        except Exception as e:
            logger.debug(f"PowerShell launch failed: {e}")

        return False, "powershell"

    def _update_average_time(self, execution_time: float):
        """Update average execution time statistics"""
        total_successful = self.launch_stats['successful_launches']
        current_avg = self.launch_stats['average_time']

        # Calculate new average
        new_avg = ((current_avg * (total_successful - 1)) + execution_time) / total_successful
        self.launch_stats['average_time'] = new_avg

    def get_stats(self) -> Dict:
        """Get launcher statistics"""
        total = self.launch_stats['total_launches']
        success_rate = (self.launch_stats['successful_launches'] / total * 100) if total > 0 else 0

        return {
            'total_launches': total,
            'successful_launches': self.launch_stats['successful_launches'],
            'failed_launches': self.launch_stats['failed_launches'],
            'success_rate': f"{success_rate:.1f}%",
            'average_time': f"{self.launch_stats['average_time']:.2f}s",
            'registered_apps': len(self.app_registry)
        }

    def list_apps(self, category: Optional[str] = None) -> List[Dict]:
        """List available applications, optionally filtered by category"""
        apps = []
        for key, app_info in self.app_registry.items():
            if category is None or app_info['category'] == category:
                apps.append({
                    'key': key,
                    'name': app_info['display_name'],
                    'category': app_info['category'],
                    'commands': app_info['commands'],
                    'available': len(app_info['paths']) > 0 or len(app_info['commands']) > 0
                })

        return sorted(apps, key=lambda x: x['name'])

    def quick_launch_business_apps(self) -> Dict[str, bool]:
        """Quick launch common business applications"""
        business_apps = ['excel', 'word', 'outlook', 'chrome', 'calculator']
        results = {}

        logger.info("🚀 Quick launching business applications...")
        for app in business_apps:
            success, method, exec_time = self.launch_app(app)
            results[app] = success
            if success:
                logger.info(f"  ✅ {app}: {exec_time:.2f}s via {method}")
            else:
                logger.warning(f"  ❌ {app}: Failed")
            time.sleep(1)  # Brief pause between launches

        return results


# Example usage and testing
if __name__ == "__main__":
    launcher = EnhancedAppLauncher()

    print("🎯 Enhanced App Launcher - Agent-S")
    print("=" * 40)

    # Show statistics
    stats = launcher.get_stats()
    print(f"📊 Registered Apps: {stats['registered_apps']}")

    # List available apps
    print("\n📱 Available Applications:")
    apps = launcher.list_apps()
    for app in apps[:10]:  # Show first 10
        status = "✅" if app['available'] else "❌"
        print(f"  {status} {app['name']} ({app['key']})")

    # Interactive mode
    print("\n💡 Enter application name to launch (or 'quit' to exit):")
    while True:
        try:
            app_name = input("\n🎯 Launch: ").strip()
            if app_name.lower() in ['quit', 'exit', 'q']:
                break

            if app_name:
                success, method, exec_time = launcher.launch_app(app_name)
                if success:
                    print(f"✅ Launched in {exec_time:.2f}s via {method}")
                else:
                    print(f"❌ Failed to launch {app_name}")
        except KeyboardInterrupt:
            break

    # Final statistics
    print(f"\n📊 Final Statistics:")
    final_stats = launcher.get_stats()
    for key, value in final_stats.items():
        print(f"  {key}: {value}")

    print("\n👋 Goodbye!")
