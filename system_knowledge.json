{"system_info": {}, "installed_apps": {"nzxt cam 4.74.0": {"name": "NZXT CAM 4.74.0", "location": "", "registry_key": "ac0666ae-ee66-5310-ac01-9d6348133b2d"}, "anaconda3 2024.10-1 (python 3.12.7 64-bit)": {"name": "Anaconda3 2024.10-1 (Python 3.12.7 64-bit)", "location": "", "registry_key": "Anaconda3 2024.10-1 (Python 3.12.7 64-bit)"}, "android studio": {"name": "Android Studio", "location": "", "registry_key": "Android Studio"}, "avast free antivirus": {"name": "Avast Free Antivirus", "location": "C:\\Program Files\\Avast Software\\Avast", "registry_key": "Avast Antivirus"}, "ccleaner": {"name": "<PERSON><PERSON><PERSON>", "location": "C:\\Program Files\\CCleaner", "type": "folder_scan"}, "docker desktop": {"name": "<PERSON><PERSON> Des<PERSON>", "location": "C:\\Users\\<USER>\\AppData\\Roaming\\Docker Desktop", "type": "folder_scan"}, "fairlight audio accelerator utility": {"name": "Fairlight Audio Accelerator Utility", "location": "C:\\Program Files\\Blackmagic Design\\DaVinci Resolve\\audio\\Fairlight Audio Accelerator\\", "registry_key": "FairlightAudioAccelerator_is1"}, "git": {"name": "Git", "location": "C:\\Program Files\\Git", "type": "folder_scan"}, "pgagent_pg17 4.2.3": {"name": "pgAgent_PG17 4.2.3", "location": "C:\\Program Files\\PostgreSQL\\17", "registry_key": "pgAgent_PG17 4.2.3-1"}, "postgresql 17 ": {"name": "PostgreSQL 17 ", "location": "C:\\Program Files\\PostgreSQL\\17", "registry_key": "PostgreSQL 17"}, "devour": {"name": "DEVOUR", "location": "C:\\Program Files (x86)\\Steam\\steamapps\\common\\Devour", "registry_key": "Steam App 1274570"}, "mydockfinder": {"name": "MyDockFinder", "location": "C:\\Program Files (x86)\\Steam\\steamapps\\common\\MyDockFinder", "registry_key": "Steam App 1787090"}, "call of duty: black ops iii": {"name": "Call of Duty: Black Ops III", "location": "C:\\Program Files (x86)\\Steam\\steamapps\\common\\Call of Duty Black Ops III", "registry_key": "Steam App 311210"}, "dead by daylight": {"name": "Dead by Daylight", "location": "C:\\Program Files (x86)\\Steam\\steamapps\\common\\Dead by Daylight", "registry_key": "Steam App 381210"}, "wallpaper engine": {"name": "Wallpaper Engine", "location": "C:\\Program Files (x86)\\Steam\\steamapps\\common\\wallpaper_engine", "registry_key": "Steam App 431960"}, "spacewar": {"name": "Spacewar", "location": "", "registry_key": "Steam App 480"}, "microsoft visual c++ 2013 x64 additional runtime - 12.0.40664": {"name": "Microsoft Visual C++ 2013 x64 Additional Runtime - 12.0.40664", "location": "", "registry_key": "{010792BA-551A-3AC0-A7EF-0FAB4156C382}"}, "microsoft .net runtime - 8.0.18 (x64)": {"name": "Microsoft .NET Runtime - 8.0.18 (x64)", "location": "", "registry_key": "{024F9CF2-AE21-4E1B-82E1-6E88F3972EB6}"}, "intel(r) serial io": {"name": "Intel(R) Serial IO", "location": "C:\\Program Files\\Intel\\Intel(R) Serial IO", "registry_key": "{9FD91C5C-44AE-4D9D-85BE-AE52816B0294}"}, "python 3.12.2 executables (64-bit)": {"name": "Python 3.12.2 Executables (64-bit)", "location": "", "registry_key": "{097D2A37-E94B-4FAD-8C89-D63443BD4D4A}"}, "python 3.12.9 development libraries (64-bit)": {"name": "Python 3.12.9 Development Libraries (64-bit)", "location": "", "registry_key": "{17CB40BC-AC95-47EF-9CFD-7D9C3F9EEB41}"}, "python 3.12.9 core interpreter (64-bit)": {"name": "Python 3.12.9 Core Interpreter (64-bit)", "location": "", "registry_key": "{1B1A3A1E-3CCF-4A0E-A544-0B94E8E51156}"}, "intel(r) management engine components": {"name": "Intel(R) Management Engine Components", "location": "", "registry_key": "{F3D2543D-2538-49B0-BFA6-EBD2DC8143DD}"}, "microsoft visual c++ 2010  x64 redistributable - 10.0.40219": {"name": "Microsoft Visual C++ 2010  x64 Redistributable - 10.0.40219", "location": "", "registry_key": "{1D8E6291-B0D5-35EC-8441-6616F567A0F7}"}, "nvidia nsight compute 2023.1.0": {"name": "NVIDIA Nsight Compute 2023.1.0", "location": "", "registry_key": "{1DF98968-764F-4AC4-AAF3-E7E50B2831BA}"}, "python 3.12.9 documentation (64-bit)": {"name": "Python 3.12.9 Documentation (64-bit)", "location": "", "registry_key": "{285AE535-C59C-401B-9407-7E1B7136C2E4}"}, "microsoft windows desktop runtime - 8.0.18 (x64)": {"name": "Microsoft Windows Desktop Runtime - 8.0.18 (x64)", "location": "", "registry_key": "{4e59a99f-313c-4d91-9e44-d5f4ea6dc79f}"}, "microsoft visual c++ 2022 x64 minimum runtime - 14.42.34438": {"name": "Microsoft Visual C++ 2022 X64 Minimum Runtime - 14.42.34438", "location": "", "registry_key": "{2E15F519-4FDA-4834-B4EE-7EFCE7D8D4EE}"}, "python 3.12.9 standard library (64-bit)": {"name": "Python 3.12.9 Standard Library (64-bit)", "location": "", "registry_key": "{312BF73C-2417-4636-A1C8-2DE2EAE1AD68}"}, "python 3.12.9 tcl/tk support (64-bit)": {"name": "Python 3.12.9 Tcl/Tk Support (64-bit)", "location": "", "registry_key": "{35132CCC-6F41-40CF-8045-FDD16269C0D4}"}, "davinci resolve control panels": {"name": "DaVinci Resolve Control Panels", "location": "", "registry_key": "{3739CA49-792F-4F1F-9B76-42DFBBBED27E}"}, "python 3.12.9 pip bootstrap (64-bit)": {"name": "Python 3.12.9 pip <PERSON><PERSON> (64-bit)", "location": "", "registry_key": "{3896B2CF-1393-4CDF-A6B6-8FAFF1E5FB03}"}, "python 3.12.2 core interpreter (64-bit)": {"name": "Python 3.12.2 Core Interpreter (64-bit)", "location": "", "registry_key": "{4534F2ED-1616-434D-98A6-0DA358DCD466}"}, "microsoft .net host fx resolver - 8.0.18 (x64)": {"name": "Microsoft .NET Host FX Resolver - 8.0.18 (x64)", "location": "", "registry_key": "{5116B651-F1AB-4667-8D2B-D5EE2B4D33AB}"}, "microsoft visual c++ 2013 x64 minimum runtime - 12.0.40664": {"name": "Microsoft Visual C++ 2013 x64 Minimum Runtime - 12.0.40664", "location": "", "registry_key": "{53CF6934-A98D-3D84-9146-FC4EDF3D5641}"}, "davinci resolve": {"name": "DaVinci <PERSON>solve", "location": "", "registry_key": "{592397DE-B8DC-4F92-91D5-926287D15017}"}, "python 3.12.9 add to path (64-bit)": {"name": "Python 3.12.9 Add to Path (64-bit)", "location": "", "registry_key": "{59FFA9E4-7873-481E-B795-184D9BD82CCD}"}, "microsoft .net host - 8.0.18 (x64)": {"name": "Microsoft .NET Host - 8.0.18 (x64)", "location": "", "registry_key": "{60BD8FAE-F541-477B-BAC5-7CDE355D2A22}"}, "node.js": {"name": "Node.js", "location": "", "registry_key": "{672AF449-6278-4AC5-8FE0-A951FCD7CFB0}"}, "redis on windows": {"name": "Redis on Windows", "location": "", "registry_key": "{6E927557-4447-4348-AE9C-4B2EA64BDA17}"}, "microsoft visual studio installer": {"name": "Microsoft Visual Studio Installer", "location": "\"C:\\Program Files (x86)\\Microsoft Visual Studio\\Installer\"", "registry_key": "{6F320B93-EE3C-4826-85E0-ADF79F8D4C61}"}, "python 3.12.9 test suite (64-bit)": {"name": "Python 3.12.9 Test Suite (64-bit)", "location": "", "registry_key": "{7B927FDC-8F38-4DF7-9EF2-F34ED59CE33D}"}, "intel(r) chipset device software": {"name": "Intel(R) Chipset Device Software", "location": "", "registry_key": "{d86cfdf3-0928-4b89-9f81-d21552c39b2d}"}, "microsoft mpi (10.1.12498.16)": {"name": "Microsoft MPI (10.1.12498.16)", "location": "C:\\Program Files\\Microsoft MPI\\", "registry_key": "{8499ACD3-C1E3-45AB-BF96-DA491727EBE1}"}, "application verifier x64 external package": {"name": "Application Verifier x64 External Package", "location": "", "registry_key": "{8A4CD158-E6B3-6D91-D7DE-10098BC980E2}"}, "python 3.12.9 executables (64-bit)": {"name": "Python 3.12.9 Executables (64-bit)", "location": "", "registry_key": "{8F708501-AF68-42E7-8A6E-D239CA6DA1A8}"}, "windows subsystem for linux": {"name": "Windows Subsystem for Linux", "location": "", "registry_key": "{9123DBDC-D242-4A68-9E97-841E2A788E7C}"}, "python 3.12.2 test suite (64-bit)": {"name": "Python 3.12.2 Test Suite (64-bit)", "location": "", "registry_key": "{94087C99-E4F5-4637-A789-3B6059DF787B}"}, "intel(r) me wmi provider": {"name": "Intel(R) ME WMI Provider", "location": "", "registry_key": "{95495073-8315-4E24-B475-E8B59638F0B4}"}, "void": {"name": "Void", "location": "C:\\Users\\<USER>\\AppData\\Roaming\\Void", "type": "folder_scan"}, "nvidia nsight systems 2023.1.2": {"name": "NVIDIA Nsight Systems 2023.1.2", "location": "", "registry_key": "{A397B122-A879-40D8-941A-68122D92B3B6}"}, "vs script debugging common": {"name": "VS Script Debugging Common", "location": "", "registry_key": "{A4272808-82F5-410F-A5F9-1BF6F63F6B9A}"}, "blackmagic raw common components": {"name": "Blackmagic RAW Common Components", "location": "", "registry_key": "{B276A28D-9003-4448-B993-2F85E3D3919B}"}, "microsoft visual c++ 2019 x64 debug runtime - 14.29.30157": {"name": "Microsoft Visual C++ 2019 X64 Debug Runtime - 14.29.30157", "location": "", "registry_key": "{B2D2DB83-DEF0-4638-A634-025F645DFBDB}"}, "cublas runtime": {"name": "CUBLAS Runtime", "location": "C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.1", "registry_key": "{B2FE1952-0186-46C3-BAEC-A80AA35AC5B8}_cublas_12.1"}, "cublas development": {"name": "CUBLAS Development", "location": "C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.1", "registry_key": "{B2FE1952-0186-46C3-BAEC-A80AA35AC5B8}_cublas_dev_12.1"}, "nvidia cuda development 12.1": {"name": "NVIDIA CUDA Development 12.1", "location": "C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.1", "registry_key": "{B2FE1952-0186-46C3-BAEC-A80AA35AC5B8}_CUDADevelopment_12.1"}, "nvidia cuda documentation 12.1": {"name": "NVIDIA CUDA Documentation 12.1", "location": "C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.1", "registry_key": "{B2FE1952-0186-46C3-BAEC-A80AA35AC5B8}_CUDADocument_12.1"}, "cudart runtime": {"name": "CUDART Runtime", "location": "C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.1", "registry_key": "{B2FE1952-0186-46C3-BAEC-A80AA35AC5B8}_cudart_12.1"}, "nvidia cuda runtime 12.1": {"name": "NVIDIA CUDA Runtime 12.1", "location": "C:\\Program Files\\NVIDIA Corporation\\Installer2\\CUDARuntimes_12.1.{A860DD2B-9543-428E-8203-DCAD6D90E0B1}", "registry_key": "{B2FE1952-0186-46C3-BAEC-A80AA35AC5B8}_CUDARuntimes_12.1"}, "cuda toolkit": {"name": "CUDA Toolkit", "location": "C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.1", "registry_key": "{B2FE1952-0186-46C3-BAEC-A80AA35AC5B8}_CUDAToolkit_12.1"}, "cuda profiler api": {"name": "CUDA Profiler API", "location": "C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.1", "registry_key": "{B2FE1952-0186-46C3-BAEC-A80AA35AC5B8}_cuda_profiler_api_12.1"}, "cudnn runtime": {"name": "CUDNN Runtime", "location": "C:\\Program Files\\NVIDIA\\CUDNN\\v9.10", "registry_key": "{B2FE1952-0186-46C3-BAEC-A80AA35AC5B8}_cudnn_cuda12.9_9.10"}, "cudnn development": {"name": "CUDNN Development", "location": "C:\\Program Files\\NVIDIA\\CUDNN\\v9.10", "registry_key": "{B2FE1952-0186-46C3-BAEC-A80AA35AC5B8}_cudnn_dev_cuda12.9_9.10"}, "cudnn samples": {"name": "CUDNN Samples", "location": "C:\\Program Files\\NVIDIA\\CUDNN\\v9.10", "registry_key": "{B2FE1952-0186-46C3-BAEC-A80AA35AC5B8}_cudnn_samples_9.10"}, "${{arpdisplayname}}": {"name": "${{arpDisplayName}}", "location": "C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.1", "registry_key": "{B2FE1952-0186-46C3-BAEC-A80AA35AC5B8}_cufft_12.1"}, "cufft development": {"name": "CUFFT Development", "location": "C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.1", "registry_key": "{B2FE1952-0186-46C3-BAEC-A80AA35AC5B8}_cufft_dev_12.1"}, "cuobjdump": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "location": "C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.1", "registry_key": "{B2FE1952-0186-46C3-BAEC-A80AA35AC5B8}_cuobjdump_12.1"}, "cupti": {"name": "CUPTI", "location": "C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.1", "registry_key": "{B2FE1952-0186-46C3-BAEC-A80AA35AC5B8}_cupti_12.1"}, "curand runtime": {"name": "CURAND Runtime", "location": "C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.1", "registry_key": "{B2FE1952-0186-46C3-BAEC-A80AA35AC5B8}_curand_12.1"}, "curand development": {"name": "CURAND Development", "location": "C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.1", "registry_key": "{B2FE1952-0186-46C3-BAEC-A80AA35AC5B8}_curand_dev_12.1"}, "cusolver runtime": {"name": "CUSOLVER Runtime", "location": "C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.1", "registry_key": "{B2FE1952-0186-46C3-BAEC-A80AA35AC5B8}_cusolver_12.1"}, "cusolver development": {"name": "CUSOLVER Development", "location": "C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.1", "registry_key": "{B2FE1952-0186-46C3-BAEC-A80AA35AC5B8}_cusolver_dev_12.1"}, "cusparse runtime": {"name": "CUSPARSE Runtime", "location": "C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.1", "registry_key": "{B2FE1952-0186-46C3-BAEC-A80AA35AC5B8}_cusparse_12.1"}, "cusparse development": {"name": "CUSPARSE Development", "location": "C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.1", "registry_key": "{B2FE1952-0186-46C3-BAEC-A80AA35AC5B8}_cusparse_dev_12.1"}, "cuxxfilt": {"name": "cuxxfilt", "location": "C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.1", "registry_key": "{B2FE1952-0186-46C3-BAEC-A80AA35AC5B8}_cuxxfilt_12.1"}, "demo suite": {"name": "Demo Suite", "location": "C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.1", "registry_key": "{B2FE1952-0186-46C3-BAEC-A80AA35AC5B8}_demo_suite_12.1"}, "nvidia graphics driver 572.16": {"name": "NVIDIA Graphics Driver 572.16", "location": "C:\\Program Files\\NVIDIA Corporation\\Installer2\\Display.Driver.{8C7E467D-A9F6-49F2-AB39-69D93ECC2306}", "registry_key": "{B2FE1952-0186-46C3-BAEC-A80AA35AC5B8}_Display.Driver"}, "nvidia app 11.0.4.159": {"name": "NVIDIA App 11.0.4.159", "location": "C:\\Program Files\\NVIDIA Corporation\\Installer2\\Display.NvApp.{47A4EBD3-7096-41FE-AB2B-D45746942EBB}", "registry_key": "{B2FE1952-0186-46C3-BAEC-A80AA35AC5B8}_Display.NvApp"}, "nvidia messagebus 3 for nvapp": {"name": "NVIDIA MessageBus 3 for NvApp", "location": "C:\\Program Files\\NVIDIA Corporation\\Installer2\\Display.NvApp.MessageBus.{1C98F2DB-2C4D-4549-A4F8-6E8A00C91249}", "registry_key": "{B2FE1952-0186-46C3-BAEC-A80AA35AC5B8}_Display.NvApp.MessageBus"}, "nvidia backend": {"name": "NVIDIA Backend", "location": "C:\\Program Files\\NVIDIA Corporation\\Installer2\\Display.NvApp.NvBackend.{B5BFDB1E-D6EB-4C1F-A59D-7F8A0095DF84}", "registry_key": "{B2FE1952-0186-46C3-BAEC-A80AA35AC5B8}_Display.NvApp.NvBackend"}, "nvcpl": {"name": "NvCpl", "location": "C:\\Program Files\\NVIDIA Corporation\\Installer2\\Display.NvApp.NvCPL.{21AA3BED-D374-45CF-B715-9DF663BEF6C5}", "registry_key": "{B2FE1952-0186-46C3-BAEC-A80AA35AC5B8}_Display.NvApp.NvCPL"}, "nvidia physx system software 9.23.1019": {"name": "NVIDIA PhysX System Software 9.23.1019", "location": "C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX", "registry_key": "{B2FE1952-0186-46C3-BAEC-A80AA35AC5B8}_Display.PhysX"}, "cuda documentation": {"name": "CUDA Documentation", "location": "C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.1", "registry_key": "{B2FE1952-0186-46C3-BAEC-A80AA35AC5B8}_documentation_12.1"}, "nvidia frameview sdk 1.5.10920.35420203": {"name": "NVIDIA FrameView SDK 1.5.10920.35420203", "location": "C:\\Program Files\\NVIDIA Corporation\\FrameViewSDK", "registry_key": "{B2FE1952-0186-46C3-BAEC-A80AA35AC5B8}_FrameViewSdk"}, "nvidia hd audio driver 1.4.3.2": {"name": "NVIDIA HD Audio Driver 1.4.3.2", "location": "C:\\Program Files\\NVIDIA Corporation\\Installer2\\HDAudio.Driver.{794B3ED8-B44A-4E56-863C-C36B2C91DD81}", "registry_key": "{B2FE1952-0186-46C3-BAEC-A80AA35AC5B8}_HDAudio.Driver"}, "nvidia install application": {"name": "NVIDIA Install Application", "location": "C:\\Program Files\\NVIDIA Corporation\\Installer2\\InstallerCore", "registry_key": "{B2FE1952-0186-46C3-BAEC-A80AA35AC5B8}_installer"}, "npp runtime": {"name": "NPP Runtime", "location": "C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.1", "registry_key": "{B2FE1952-0186-46C3-BAEC-A80AA35AC5B8}_npp_12.1"}, "npp development": {"name": "NPP Development", "location": "C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.1", "registry_key": "{B2FE1952-0186-46C3-BAEC-A80AA35AC5B8}_npp_dev_12.1"}, "nvcc": {"name": "nvcc", "location": "C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.1", "registry_key": "{B2FE1952-0186-46C3-BAEC-A80AA35AC5B8}_nvcc_12.1"}, "nvidia container": {"name": "NVIDIA Container", "location": "C:\\Program Files\\NVIDIA Corporation\\NvContainer", "registry_key": "{B2FE1952-0186-46C3-BAEC-A80AA35AC5B8}_NvContainer"}, "nvidia aiuser container": {"name": "NVIDIA AIUser Container", "location": "C:\\Program Files\\NVIDIA Corporation\\Installer2\\NvContainer.AIUser.{599D9D65-FFBF-4D68-8337-684304BA9ABB}", "registry_key": "{B2FE1952-0186-46C3-BAEC-A80AA35AC5B8}_NvContainer.AIUser"}, "nvidia localsystem container": {"name": "NVIDIA LocalSystem Container", "location": "C:\\Program Files\\NVIDIA Corporation\\Installer2\\NvContainer.LocalSystem.{AFCDA27C-748C-4219-A8CB-497A708D3CC8}", "registry_key": "{B2FE1952-0186-46C3-BAEC-A80AA35AC5B8}_NvContainer.LocalSystem"}, "nvidia session container": {"name": "NVIDIA Session Container", "location": "C:\\Program Files\\NVIDIA Corporation\\Installer2\\NvContainer.Session.{3599A7C0-AB06-4661-9F63-7E161C316B62}", "registry_key": "{B2FE1952-0186-46C3-BAEC-A80AA35AC5B8}_NvContainer.Session"}, "nvidia user container": {"name": "NVIDIA User Container", "location": "C:\\Program Files\\NVIDIA Corporation\\Installer2\\NvContainer.User.{87E9270D-1551-4F8B-9817-32B996A00FB2}", "registry_key": "{B2FE1952-0186-46C3-BAEC-A80AA35AC5B8}_NvContainer.User"}, "disassembler": {"name": "Disassembler", "location": "C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.1", "registry_key": "{B2FE1952-0186-46C3-BAEC-A80AA35AC5B8}_nvdisasm_12.1"}, "nvidia nvdlisr": {"name": "NVIDIA NvDLISR", "location": "C:\\Program Files\\NVIDIA Corporation\\Installer2\\NvDLISR.{F908B12F-181F-45D8-8E07-A3B094522D5E}", "registry_key": "{B2FE1952-0186-46C3-BAEC-A80AA35AC5B8}_NvDLISR"}, "nvjitlink runtime": {"name": "NVJITLINK Runtime", "location": "C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.1", "registry_key": "{B2FE1952-0186-46C3-BAEC-A80AA35AC5B8}_nvjitlink_12.1"}, "nvjpeg runtime": {"name": "NVJPEG Runtime", "location": "C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.1", "registry_key": "{B2FE1952-0186-46C3-BAEC-A80AA35AC5B8}_nvjpeg_12.1"}, "nvjpeg development": {"name": "NVJPEG Development", "location": "C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.1", "registry_key": "{B2FE1952-0186-46C3-BAEC-A80AA35AC5B8}_nvjpeg_dev_12.1"}, "nvml development": {"name": "NVML Development", "location": "C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.1", "registry_key": "{B2FE1952-0186-46C3-BAEC-A80AA35AC5B8}_nvml_dev_12.1"}, "nvidia watchdog plugin for nvcontainer": {"name": "NVIDIA Watchdog Plugin for NvContainer", "location": "C:\\Program Files\\NVIDIA Corporation\\Installer2\\NvPlugin.Watchdog.{84DFF929-F868-4DFA-854A-620B5CE8D212}", "registry_key": "{B2FE1952-0186-46C3-BAEC-A80AA35AC5B8}_NvPlugin.Watchdog"}, "cuda profiler tools": {"name": "CUDA Profiler Tools", "location": "C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.1", "registry_key": "{B2FE1952-0186-46C3-BAEC-A80AA35AC5B8}_nvprof_12.1"}, "nvprune": {"name": "nvprune", "location": "C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.1", "registry_key": "{B2FE1952-0186-46C3-BAEC-A80AA35AC5B8}_nvprune_12.1"}, "nvrtc runtime": {"name": "NVRTC Runtime", "location": "C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.1", "registry_key": "{B2FE1952-0186-46C3-BAEC-A80AA35AC5B8}_nvrtc_12.1"}, "nvrtc development": {"name": "NVRTC Development", "location": "C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.1", "registry_key": "{B2FE1952-0186-46C3-BAEC-A80AA35AC5B8}_nvrtc_dev_12.1"}, "nvidia telemetry client": {"name": "NVIDIA Telemetry Client", "location": "C:\\Program Files\\NVIDIA Corporation\\NvTelemetry", "registry_key": "{B2FE1952-0186-46C3-BAEC-A80AA35AC5B8}_NvTelemetry"}, "nvtx development": {"name": "NVTX Development", "location": "C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.1", "registry_key": "{B2FE1952-0186-46C3-BAEC-A80AA35AC5B8}_nvtx_12.1"}, "nvvm samples": {"name": "NVVM Samples", "location": "C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.1", "registry_key": "{B2FE1952-0186-46C3-BAEC-A80AA35AC5B8}_nvvm_samples_12.1"}, "occupancy calculator": {"name": "Occupancy Calculator", "location": "C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.1", "registry_key": "{B2FE1952-0186-46C3-BAEC-A80AA35AC5B8}_occupancy_calculator_12.1"}, "opencl runtime": {"name": "OPENCL Runtime", "location": "C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.1", "registry_key": "{B2FE1952-0186-46C3-BAEC-A80AA35AC5B8}_opencl_12.1"}, "compute sanitizer": {"name": "Compute Sanitizer", "location": "C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.1", "registry_key": "{B2FE1952-0186-46C3-BAEC-A80AA35AC5B8}_sanitizer_12.1"}, "nvidia shadowplay 11.0.4.0": {"name": "NVIDIA ShadowPlay 11.0.4.0", "location": "C:\\Program Files\\NVIDIA Corporation\\NVIDIA App\\ShadowPlay", "registry_key": "{B2FE1952-0186-46C3-BAEC-A80AA35AC5B8}_ShadowPlay"}, "cuda cccl": {"name": "CUDA CCCL", "location": "C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.1", "registry_key": "{B2FE1952-0186-46C3-BAEC-A80AA35AC5B8}_thrust_12.1"}, "nvidia virtual audio 4.65.0.3": {"name": "NVIDIA Virtual Audio 4.65.0.3", "location": "C:\\Program Files\\NVIDIA Corporation\\Installer2\\VirtualAudio.Driver.{649728F2-2A11-4CE5-994B-EC6B4D1145C7}", "registry_key": "{B2FE1952-0186-46C3-BAEC-A80AA35AC5B8}_VirtualAudio.Driver"}, "visual profiler": {"name": "Visual Profiler", "location": "C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.1", "registry_key": "{B2FE1952-0186-46C3-BAEC-A80AA35AC5B8}_visual_profiler_12.1"}, "nvidia cuda visual studio integration 12.1": {"name": "NVIDIA CUDA Visual Studio Integration 12.1", "location": "C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.1", "registry_key": "{B2FE1952-0186-46C3-BAEC-A80AA35AC5B8}_visual_studio_integration_12.1"}, "python 3.12.2 tcl/tk support (64-bit)": {"name": "Python 3.12.2 Tcl/Tk Support (64-bit)", "location": "", "registry_key": "{B50C92E9-2780-433A-AA61-E9F06D0AFF8A}"}, "intel(r) icls": {"name": "Intel(R) Icls", "location": "", "registry_key": "{BBA1D01B-6740-425B-8B5C-DC980291DAC5}"}, "python 3.12.2 documentation (64-bit)": {"name": "Python 3.12.2 Documentation (64-bit)", "location": "", "registry_key": "{BD32BDE9-835D-4013-8F9A-45FF11456F02}"}, "python 3.12.2 pip bootstrap (64-bit)": {"name": "Python 3.12.2 pip <PERSON> (64-bit)", "location": "", "registry_key": "{BDE73EDC-76AE-475D-8885-9B583631B0FC}"}, "go programming language amd64 go1.24.4": {"name": "Go Programming Language amd64 go1.24.4", "location": "", "registry_key": "{BF44F985-A720-4EB9-8B4B-6C2F76A30CDB}"}, "dynamic application loader host interface service": {"name": "Dynamic Application Loader Host Interface Service", "location": "", "registry_key": "{C1EF5AEC-6EC4-4102-A13A-20A8E4EB3071}"}, "microsoft update health tools": {"name": "Microsoft Update Health Tools", "location": "C:\\Program Files\\Microsoft Update Health Tools", "type": "folder_scan"}, "nvidia nsight visual studio edition 2023.1.0.23041": {"name": "NVIDIA Nsight Visual Studio Edition 2023.1.0.23041", "location": "", "registry_key": "{********-2E75-42F4-B21E-638E92F3C5AA}"}, "intel(r) management engine driver": {"name": "Intel(R) Management Engine Driver", "location": "", "registry_key": "{CBC5E4AC-0679-4DA6-BD54-DF9737C5BE2A}"}, "universal crt tools x64": {"name": "Universal CRT Tools x64", "location": "", "registry_key": "{CD06199B-41C1-AE6D-7567-984CC68792C3}"}, "windows app certification kit native components": {"name": "Windows App Certification Kit Native Components", "location": "", "registry_key": "{D2886D0B-F38D-EB07-2108-B6218761F8F9}"}, "python 3.12.2 add to path (64-bit)": {"name": "Python 3.12.2 Add to Path (64-bit)", "location": "", "registry_key": "{D552469C-E810-468F-A139-1EA43D0E2BE0}"}, "python 3.12.2 standard library (64-bit)": {"name": "Python 3.12.2 Standard Library (64-bit)", "location": "", "registry_key": "{E172CAF3-ABC7-4B62-BA8C-3A2472DE44F6}"}, "microsoft visual c++ 2022 x64 additional runtime - 14.42.34438": {"name": "Microsoft Visual C++ 2022 X64 Additional Runtime - 14.42.34438", "location": "", "registry_key": "{E528AD94-12D7-42C4-91A3-908BE28E9BD2}"}, "windows sdk directx x64 remote": {"name": "Windows SDK DirectX x64 Remote", "location": "", "registry_key": "{EBD149F6-9F46-49E4-ED99-25D2A0ECDBBD}"}, "python 3.12.2 development libraries (64-bit)": {"name": "Python 3.12.2 Development Libraries (64-bit)", "location": "", "registry_key": "{F131E2DD-B8C5-42F3-85B7-3D4BAC9582CD}"}, "windows subsystem for linux update": {"name": "Windows Subsystem for Linux Update", "location": "", "registry_key": "{F8474A47-8B5D-4466-ACE3-78EAB3BF21A8}"}, "epic games launcher prerequisites (x64)": {"name": "Epic Games Launcher Prerequisites (x64)", "location": "", "registry_key": "{F9C5C994-F6B9-4D75-B3E7-AD01B84073E9}"}, "anydesk": {"name": "AnyDesk", "location": "C:\\Users\\<USER>\\AppData\\Roaming\\AnyDesk", "type": "folder_scan"}, "visual studio build tools 2019": {"name": "Visual Studio Build Tools 2019", "location": "C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\BuildTools", "registry_key": "b334b1d4"}, "brave": {"name": "Brave", "location": "C:\\Program Files\\BraveSoftware\\Brave-Browser\\Application", "registry_key": "BraveSoftware Brave-Browser"}, "google chrome": {"name": "Google Chrome", "location": "C:\\Program Files\\Google\\Chrome\\Application", "registry_key": "Google Chrome"}, "microsoft edge": {"name": "Microsoft Edge", "location": "C:\\Program Files (x86)\\Microsoft\\Edge\\Application", "registry_key": "Microsoft Edge"}, "microsoft edge webview2 runtime": {"name": "Microsoft Edge WebView2 Runtime", "location": "C:\\Program Files (x86)\\Microsoft\\EdgeWebView\\Application", "registry_key": "Microsoft EdgeWebView"}, "steam": {"name": "Steam", "location": "C:\\Users\\<USER>\\AppData\\Local\\Steam", "type": "folder_scan"}, "microsoft visual c++ 2013 redistributable (x64) - 12.0.40664": {"name": "Microsoft Visual C++ 2013 Redistributable (x64) - 12.0.40664", "location": "", "registry_key": "{042d26ef-3dbe-4c25-95d3-4c1b11b235a7}"}, "universal crt redistributable": {"name": "Universal CRT Redistributable", "location": "", "registry_key": "{A57CD0A6-4297-FD30-34A4-34758B6F5F69}"}, "windows sdk desktop tools arm64": {"name": "Windows SDK Desktop Tools arm64", "location": "", "registry_key": "{06E580FA-F3B2-08E9-4DC0-0AB55D985CBB}"}, "windows iot extension sdk": {"name": "Windows IoT Extension SDK", "location": "", "registry_key": "{084094EF-6AC9-480A-7CC1-04199047BBDD}"}, "vs_minshellmsires": {"name": "vs_minshellmsires", "location": "", "registry_key": "{0916C6E1-6A0A-4887-9E00-D96FD44AFACE}"}, "realtek usb audio": {"name": "Realtek USB Audio", "location": "C:\\Program Files (x86)\\Realtek\\Audio", "registry_key": "{0A46A65D-89AC-464C-8026-3CD44960BD04}"}, "winrt intellisense uap - en-us": {"name": "WinRT Intellisense UAP - en-us", "location": "", "registry_key": "{0AF3B821-474B-1885-473A-6E3FB4F1CF71}"}, "windows sdk for windows store apps directx x86 remote": {"name": "Windows SDK for Windows Store Apps DirectX x86 Remote", "location": "", "registry_key": "{0E2FEA3B-C853-DE2A-8A04-BB7D5BF010E0}"}, "vs_filehandler_amd64": {"name": "vs_filehandler_amd64", "location": "", "registry_key": "{102E83BD-B6A0-4C74-AD22-7D594A3435D3}"}, "winrt intellisense ppi - en-us": {"name": "WinRT Intellisense PPI - en-us", "location": "", "registry_key": "{15E29AFF-CB19-A20B-9A81-B0765A63115F}"}, "windows sdk desktop libs x64": {"name": "Windows SDK Desktop Libs x64", "location": "", "registry_key": "{170B023D-7C1B-2EF4-D3E9-B974A26752AC}"}, "windows sdk desktop tools x86": {"name": "Windows SDK Desktop Tools x86", "location": "", "registry_key": "{1C966E96-8553-EF1E-A06F-A8174B3CAA60}"}, "windows sdk for windows store apps libs": {"name": "Windows SDK for Windows Store Apps Libs", "location": "", "registry_key": "{1FBBD022-F751-FE7B-54DF-9FED23892B2F}"}, "winrt intellisense iot - other languages": {"name": "WinRT Intellisense IoT - Other Languages", "location": "", "registry_key": "{216D5F47-257D-6284-5849-B51037875EFA}"}, "windows app certification kit supportedapilist x86": {"name": "Windows App Certification Kit SupportedApiList x86", "location": "", "registry_key": "{26D02D07-8007-2FD2-6DFE-14B29D09B5FD}"}, "windows sdk for windows store apps contracts": {"name": "Windows SDK for Windows Store Apps Contracts", "location": "", "registry_key": "{2A8533B3-8D16-67E4-E729-5BB04EDD2FE4}"}, "windows sdk desktop libs arm": {"name": "Windows SDK Desktop Libs arm", "location": "", "registry_key": "{2AC29D7B-F29F-34FA-4434-C5DF1F086264}"}, "winappdeploy": {"name": "WinAppDeploy", "location": "", "registry_key": "{2ADF1977-BF31-E127-B651-AC28A8658317}"}, "windows sdk for windows store apps metadata": {"name": "Windows SDK for Windows Store Apps Metadata", "location": "", "registry_key": "{2CFB2180-7C20-5470-4B8A-747512A6AB70}"}, "windows sdk facade windows winmd versioned": {"name": "Windows SDK Facade Windows WinMD Versioned", "location": "", "registry_key": "{2D296649-CFBE-CF23-EA8E-E24554187B3F}"}, "windows sdk directx x86 remote": {"name": "Windows SDK DirectX x86 Remote", "location": "", "registry_key": "{313B416A-97E7-F3EF-EDFC-A903A8CA4BC2}"}, "winrt intellisense iot - en-us": {"name": "WinRT Intellisense IoT - en-us", "location": "", "registry_key": "{3335615C-ABEB-960E-2226-4274CD28E046}"}, "vs_communitymsires": {"name": "vs_communitymsires", "location": "", "registry_key": "{3751D1CF-9A44-43D2-B4BB-80FA6E7925A8}"}, "vs_communitymsi": {"name": "vs_communitymsi", "location": "", "registry_key": "{375AFBC1-2264-470C-9ADE-2C0BF23328A2}"}, "windows sdk desktop headers x86": {"name": "Windows SDK Desktop Headers x86", "location": "", "registry_key": "{3D5981B5-ABF0-1495-7FC3-102D1C75B9C8}"}, "launcher prerequisites (x64)": {"name": "Launcher Prerequisites (x64)", "location": "", "registry_key": "{43a03b9c-4770-409c-a999-587b60700b63}"}, "windows sdk modern non-versioned developer tools": {"name": "Windows SDK Modern Non-Versioned Developer Tools", "location": "", "registry_key": "{43AA42C2-D292-CF91-6264-63B7A99CDE99}"}, "windows sdk redistributables": {"name": "Windows SDK Redistributables", "location": "", "registry_key": "{43B3CDF5-CD8F-9A5E-4598-765F8CB27170}"}, "winrt intellisense mobile - en-us": {"name": "WinRT Intellisense Mobile - en-us", "location": "", "registry_key": "{443FF51E-16C3-F23B-18FC-0D1D66024B0B}"}, "windows software development kit - windows 10.0.19041.685": {"name": "Windows Software Development Kit - Windows 10.0.19041.685", "location": "", "registry_key": "{4591faf1-a2db-4a3d-bfda-aa5a4ebb1587}"}, "windows iot extension sdk contracts": {"name": "Windows IoT Extension SDK Contracts", "location": "", "registry_key": "{497B2D49-F5C2-CA3B-05FF-22ABF39F2873}"}, "windows sdk for windows store apps tools": {"name": "Windows SDK for Windows Store Apps Tools", "location": "", "registry_key": "{4AC6C7FB-D848-9D68-DCB0-1376083FEA3A}"}, "windows sdk desktop headers arm": {"name": "Windows SDK Desktop Headers arm", "location": "", "registry_key": "{4BD2B107-B0D3-850C-7135-ACA153D30C78}"}, "universal crt extension sdk": {"name": "Universal CRT Extension SDK", "location": "", "registry_key": "{4D69FB64-4443-F2DD-DE1C-F14FD98AAC59}"}, "epic online services": {"name": "Epic Online Services", "location": "", "registry_key": "{5122B8BC-D6DF-48FF-8D4E-15A63EEC5073}"}, "microsoft visual c++ 2022 x86 minimum runtime - 14.42.34438": {"name": "Microsoft Visual C++ 2022 X86 Minimum Runtime - 14.42.34438", "location": "", "registry_key": "{5D0C4511-3CA1-4FF8-A4BA-C0E1957ABEEA}"}, "windows team extension sdk contracts": {"name": "Windows Team Extension SDK Contracts", "location": "", "registry_key": "{5F616EBF-DF09-A2DA-AB66-3A5341FA611C}"}, "windows app certification kit x64": {"name": "Windows App Certification Kit x64", "location": "", "registry_key": "{6487BFDF-6FA4-7CC5-0341-AA5D1AB69856}"}, "python launcher": {"name": "Python Launcher", "location": "", "registry_key": "{7102CAE5-270C-4E81-AC25-27699156D8AE}"}, "microsoft visual studio setup configuration": {"name": "Microsoft Visual Studio Setup Configuration", "location": "", "registry_key": "{6AC5612A-D067-44B9-9C8E-2C1B3473B429}"}, "universal crt headers libraries and sources": {"name": "Universal CRT Headers Libraries and Sources", "location": "", "registry_key": "{6B56745A-F6A4-C51C-933A-AD96C00683EA}"}, "vs_filehandler_x86": {"name": "vs_filehandler_x86", "location": "", "registry_key": "{6CBDE7BE-E956-4E0E-81FB-2CB79190C924}"}, "windows mobile extension sdk": {"name": "Windows Mobile Extension SDK", "location": "", "registry_key": "{718C25EB-084C-6341-1C3E-589DA641C28F}"}, "sdk arm redistributables": {"name": "SDK ARM Redistributables", "location": "", "registry_key": "{72DB07D6-E166-5A3F-B6E6-4664383781B8}"}, "windows mobile extension sdk contracts": {"name": "Windows Mobile Extension SDK Contracts", "location": "", "registry_key": "{7A9E937D-9757-80CB-A6E3-F4AB6081AEA6}"}, "msi development tools": {"name": "MSI Development Tools", "location": "", "registry_key": "{7AAC93B0-F3D7-6B24-6B37-9E74980C1C81}"}, "windows sdk": {"name": "Windows SDK", "location": "", "registry_key": "{7B891B74-6BE8-1581-357C-72DD8A82F0F7}"}, "windows sdk desktop libs x86": {"name": "Windows SDK Desktop Libs x86", "location": "", "registry_key": "{7DD1F495-F1BF-6A30-620F-AC064DD302D8}"}, "microsoft visual c++ 2013 x86 minimum runtime - 12.0.40664": {"name": "Microsoft Visual C++ 2013 x86 Minimum Runtime - 12.0.40664", "location": "", "registry_key": "{8122DAB1-ED4D-3676-BB0A-CA368196543E}"}, "winrt intellisense uap - other languages": {"name": "WinRT Intellisense UAP - Other Languages", "location": "", "registry_key": "{8832F8ED-1035-9ABE-FD73-4E5ABAA84A5C}"}, "vs_minshellinteropmsi": {"name": "vs_minshe<PERSON><PERSON><PERSON><PERSON>", "location": "", "registry_key": "{883D29E5-9A41-4C45-A192-C10B8078BF0C}"}, "windows sdk for windows store apps headers": {"name": "Windows SDK for Windows Store Apps Headers", "location": "", "registry_key": "{8E9DD3FE-3338-8012-81C5-F3AA9B617BAE}"}, "windows sdk arm desktop tools": {"name": "Windows SDK ARM Desktop Tools", "location": "", "registry_key": "{940042ED-CB90-8E03-BE68-DF8A76E661FD}"}, "windows sdk desktop libs arm64": {"name": "Windows SDK Desktop Libs arm64", "location": "", "registry_key": "{9555AB64-6A00-776F-CA44-568E0E7B9632}"}, "microsoft visual c++ 2013 redistributable (x86) - 12.0.40664": {"name": "Microsoft Visual C++ 2013 Redistributable (x86) - 12.0.40664", "location": "", "registry_key": "{9dff3540-fc85-4ed5-ac84-9e3c7fd8bece}"}, "windows desktop extension sdk contracts": {"name": "Windows Desktop Extension SDK Contracts", "location": "", "registry_key": "{A34A6580-86EF-A26A-33A5-80E1919B7F75}"}, "windows sdk eula": {"name": "Windows SDK EULA", "location": "", "registry_key": "{A50A075D-973C-1867-4228-738205D555C8}"}, "microsoft visual c++ 2022 x86 additional runtime - 14.42.34438": {"name": "Microsoft Visual C++ 2022 X86 Additional Runtime - 14.42.34438", "location": "", "registry_key": "{A5592FEF-F948-4BA6-A066-8BBFC2DC7EE1}"}, "windows sdk for windows store apps": {"name": "Windows SDK for Windows Store Apps", "location": "", "registry_key": "{A5E4C2C0-D963-40D6-8E5F-60A4DD995331}"}, "universal general midi dls extension sdk": {"name": "Universal General MIDI DLS Extension SDK", "location": "", "registry_key": "{A7E95C47-B5F4-110C-D27A-DECB03412B96}"}, "vs_filetracker_singleton": {"name": "vs_FileTracker_Singleton", "location": "", "registry_key": "{AB0010C0-CA62-40C7-BDED-DB2514BDCF19}"}, "winrt intellisense desktop - other languages": {"name": "WinRT Intellisense Desktop - Other Languages", "location": "", "registry_key": "{B42BF427-AFDB-C00F-DB60-6F51395D74A1}"}, "microsoft visual c++ 2015-2022 redistributable (x64) - 14.42.34438": {"name": "Microsoft Visual C++ 2015-2022 Redistributable (x64) - 14.42.34438", "location": "", "registry_key": "{b49c10dd-4d54-45f8-ad13-fa25704456a4}"}, "windows sdk signing tools": {"name": "Windows SDK Signing Tools", "location": "", "registry_key": "{B62A26BB-90A0-82FB-2DDC-3157ADF07833}"}, "microsoft visual c++ 2015-2022 redistributable (x86) - 14.42.34438": {"name": "Microsoft Visual C++ 2015-2022 Redistributable (x86) - 14.42.34438", "location": "", "registry_key": "{ba10fda9-f731-441f-a999-000bbb7ceec2}"}, "winrt intellisense desktop - en-us": {"name": "WinRT Intellisense Desktop - en-us", "location": "", "registry_key": "{BCF7CA0F-E53C-2A4F-B128-A751EC9A1016}"}, "universal crt tools x86": {"name": "Universal CRT Tools x86", "location": "", "registry_key": "{BD75F257-50A4-E0CD-9942-C3550CA3E66A}"}, "vs_minshellmsi": {"name": "vs_minshellmsi", "location": "", "registry_key": "{C1337DAC-D78B-4435-B795-29E8B7D5E75C}"}, "microsoft visual c++ 2019 x86 debug runtime - 14.29.30157": {"name": "Microsoft Visual C++ 2019 X86 Debug Runtime - 14.29.30157", "location": "", "registry_key": "{C45C7D61-1241-4033-BF55-3F7A99E06DCA}"}, "epic games launcher": {"name": "Epic Games Launcher", "location": "C:\\Program Files (x86)\\Epic Games\\", "registry_key": "{C5C3EE71-4047-4144-946E-18D500510CB5}"}, "windows sdk desktop headers x64": {"name": "Windows SDK Desktop Headers x64", "location": "", "registry_key": "{C81D239D-863A-D4B4-3562-BC8D3D7C271E}"}, "windows sdk desktop headers arm64": {"name": "Windows SDK Desktop Headers arm64", "location": "", "registry_key": "{C88797F9-0AD8-E022-5BBB-596BC78D4C76}"}, "windows team extension sdk": {"name": "Windows Team Extension SDK", "location": "", "registry_key": "{CE7E4A6A-45A2-2968-4B34-D0D4CFCC0E1D}"}, "microsoft .net framework 4 multi-targeting pack": {"name": "Microsoft .NET Framework 4 Multi-Targeting Pack", "location": "", "registry_key": "{CFEF48A8-BFB8-3EAC-8BA5-DE4F8AA267CE}"}, "update for  (kb2504637)": {"name": "Update for  (KB2504637)", "location": "", "registry_key": "{CFEF48A8-BFB8-3EAC-8BA5-DE4F8AA267CE}.KB2504637"}, "windows desktop extension sdk": {"name": "Windows Desktop Extension SDK", "location": "", "registry_key": "{D3B54AAA-2B64-5DE2-EA64-9900152E5282}"}, "microsoft visual c++ 2013 x86 additional runtime - 12.0.40664": {"name": "Microsoft Visual C++ 2013 x86 Additional Runtime - 12.0.40664", "location": "", "registry_key": "{D401961D-3A20-3AC7-943B-6139D5BD490A}"}, "windows sdk addon": {"name": "Windows SDK AddOn", "location": "", "registry_key": "{E18618EC-D9DB-4BCE-B382-85ADA2CBB340}"}, "vcpp_crt.redist.clickonce": {"name": "vcpp_crt.redist.clickonce", "location": "", "registry_key": "{E2121340-F05B-48E1-BE1D-175FA97B2FC0}"}, "microsoft visual studio setup wmi provider": {"name": "Microsoft Visual Studio Setup WMI Provider", "location": "", "registry_key": "{E281F6E2-136B-4AF0-895B-253279711697}"}, "kits configuration installer": {"name": "Kits Configuration Installer", "location": "", "registry_key": "{E75A9998-E979-760B-6AEB-49763F279EDD}"}, "microsoft visual c++ 2010  x86 redistributable - 10.0.40219": {"name": "Microsoft Visual C++ 2010  x86 Redistributable - 10.0.40219", "location": "", "registry_key": "{F0C3E5D1-1ADE-321E-8167-68EF0DE699A5}"}, "windows sdk desktop tools x64": {"name": "Windows SDK Desktop Tools x64", "location": "", "registry_key": "{F9BDEC71-9E56-CFBF-0AE8-E7AF032D07C7}"}, "windows sdk modern versioned developer tools": {"name": "Windows SDK Modern Versioned Developer Tools", "location": "", "registry_key": "{FC5A59F8-6BEE-FBB4-C720-47C565A92798}"}, "sdk arm additions": {"name": "SDK ARM Additions", "location": "", "registry_key": "{FCF9D89E-6F79-64FB-B08D-B0E69FF54DEE}"}, "winrt intellisense ppi - other languages": {"name": "WinRT Intellisense PPI - Other Languages", "location": "", "registry_key": "{FF2B49B7-0254-3D6A-4BE0-EF4C59DBCC2B}"}, "windows sdk for windows store managed apps libs": {"name": "Windows SDK for Windows Store Managed Apps Libs", "location": "", "registry_key": "{FF7D4409-CF59-34AE-BDC7-8A6146A9BA36}"}, "android": {"name": "Android", "location": "C:\\Users\\<USER>\\AppData\\Local\\Android", "type": "folder_scan"}, "application verifier": {"name": "Application Verifier", "location": "C:\\Program Files (x86)\\Application Verifier", "type": "folder_scan"}, "avast software": {"name": "Avast Software", "location": "C:\\Users\\<USER>\\AppData\\Roaming\\Avast Software", "type": "folder_scan"}, "blackmagic design": {"name": "Blackmagic Design", "location": "C:\\Users\\<USER>\\AppData\\Roaming\\Blackmagic Design", "type": "folder_scan"}, "bravesoftware": {"name": "BraveSoftware", "location": "C:\\Users\\<USER>\\AppData\\Local\\BraveSoftware", "type": "folder_scan"}, "common files": {"name": "Common Files", "location": "C:\\Program Files (x86)\\Common Files", "type": "folder_scan"}, "docker": {"name": "<PERSON>er", "location": "C:\\Users\\<USER>\\AppData\\Roaming\\Docker", "type": "folder_scan"}, "dotnet": {"name": "dotnet", "location": "C:\\Program Files\\dotnet", "type": "folder_scan"}, "epic games": {"name": "Epic Games", "location": "C:\\Users\\<USER>\\AppData\\Local\\Epic Games", "type": "folder_scan"}, "go": {"name": "go", "location": "C:\\Users\\<USER>\\AppData\\Roaming\\go", "type": "folder_scan"}, "google": {"name": "Google", "location": "C:\\Users\\<USER>\\AppData\\Roaming\\Google", "type": "folder_scan"}, "hyper-v": {"name": "Hyper-V", "location": "C:\\Program Files\\Hyper-V", "type": "folder_scan"}, "intel": {"name": "Intel", "location": "C:\\Program Files (x86)\\Intel", "type": "folder_scan"}, "internet explorer": {"name": "Internet Explorer", "location": "C:\\Program Files (x86)\\Internet Explorer", "type": "folder_scan"}, "microsoft mpi": {"name": "Microsoft MPI", "location": "C:\\Program Files\\Microsoft MPI", "type": "folder_scan"}, "modifiablewindowsapps": {"name": "ModifiableWindowsApps", "location": "C:\\Program Files\\ModifiableWindowsApps", "type": "folder_scan"}, "nodejs": {"name": "nodejs", "location": "C:\\Program Files\\nodejs", "type": "folder_scan"}, "nvidia": {"name": "NVIDIA", "location": "C:\\Users\\<USER>\\AppData\\Roaming\\NVIDIA", "type": "folder_scan"}, "nvidia corporation": {"name": "NVIDIA Corporation", "location": "C:\\Users\\<USER>\\AppData\\Local\\NVIDIA Corporation", "type": "folder_scan"}, "nvidia gpu computing toolkit": {"name": "NVIDIA GPU Computing Toolkit", "location": "C:\\Program Files\\NVIDIA GPU Computing Toolkit", "type": "folder_scan"}, "nzxt cam": {"name": "NZXT CAM", "location": "C:\\Users\\<USER>\\AppData\\Roaming\\NZXT CAM", "type": "folder_scan"}, "postgresql": {"name": "PostgreSQL", "location": "C:\\Program Files\\PostgreSQL", "type": "folder_scan"}, "python312": {"name": "Python312", "location": "C:\\Program Files\\Python312", "type": "folder_scan"}, "redis": {"name": "Redis", "location": "C:\\Program Files\\Redis", "type": "folder_scan"}, "uninstall information": {"name": "Uninstall Information", "location": "C:\\Program Files (x86)\\Uninstall Information", "type": "folder_scan"}, "windows defender": {"name": "Windows Defender", "location": "C:\\Program Files (x86)\\Windows Defender", "type": "folder_scan"}, "windows defender advanced threat protection": {"name": "Windows Defender Advanced Threat Protection", "location": "C:\\Program Files\\Windows Defender Advanced Threat Protection", "type": "folder_scan"}, "windows mail": {"name": "Windows Mail", "location": "C:\\Program Files (x86)\\Windows Mail", "type": "folder_scan"}, "windows media player": {"name": "Windows Media Player", "location": "C:\\Program Files (x86)\\Windows Media Player", "type": "folder_scan"}, "windows nt": {"name": "Windows NT", "location": "C:\\Program Files (x86)\\Windows NT", "type": "folder_scan"}, "windows photo viewer": {"name": "Windows Photo Viewer", "location": "C:\\Program Files (x86)\\Windows Photo Viewer", "type": "folder_scan"}, "windows sidebar": {"name": "Windows Sidebar", "location": "C:\\Program Files (x86)\\Windows Sidebar", "type": "folder_scan"}, "windowsapps": {"name": "WindowsApps", "location": "C:\\Program Files\\WindowsApps", "type": "folder_scan"}, "windowspowershell": {"name": "WindowsPowerShell", "location": "C:\\Program Files (x86)\\WindowsPowerShell", "type": "folder_scan"}, "wsl": {"name": "WSL", "location": "C:\\Program Files\\WSL", "type": "folder_scan"}, "easyanticheat_eos": {"name": "EasyAntiCheat_EOS", "location": "C:\\Program Files (x86)\\EasyAntiCheat_EOS", "type": "folder_scan"}, "installshield installation information": {"name": "InstallShield Installation Information", "location": "C:\\Program Files (x86)\\InstallShield Installation Information", "type": "folder_scan"}, "microsoft": {"name": "Microsoft", "location": "C:\\Users\\<USER>\\AppData\\Roaming\\Microsoft", "type": "folder_scan"}, "microsoft sdks": {"name": "Microsoft SDKs", "location": "C:\\Program Files (x86)\\Microsoft SDKs", "type": "folder_scan"}, "microsoft visual studio": {"name": "Microsoft Visual Studio", "location": "C:\\Program Files (x86)\\Microsoft Visual Studio", "type": "folder_scan"}, "microsoft.net": {"name": "Microsoft.NET", "location": "C:\\Program Files (x86)\\Microsoft.NET", "type": "folder_scan"}, "msbuild": {"name": "MSBuild", "location": "C:\\Program Files (x86)\\MSBuild", "type": "folder_scan"}, "msi": {"name": "MSI", "location": "C:\\Program Files (x86)\\MSI", "type": "folder_scan"}, "razer": {"name": "<PERSON><PERSON>", "location": "C:\\Program Files (x86)\\Razer", "type": "folder_scan"}, "realtek": {"name": "Realtek", "location": "C:\\Program Files (x86)\\Realtek", "type": "folder_scan"}, "reference assemblies": {"name": "Reference Assemblies", "location": "C:\\Program Files (x86)\\Reference Assemblies", "type": "folder_scan"}, "windows kits": {"name": "Windows Kits", "location": "C:\\Program Files (x86)\\Windows Kits", "type": "folder_scan"}, ".lingma": {"name": ".lingma", "location": "C:\\Users\\<USER>\\AppData\\Local\\.lingma", "type": "folder_scan"}, "application data": {"name": "Application Data", "location": "C:\\Users\\<USER>\\AppData\\Local\\Application Data", "type": "folder_scan"}, "backup": {"name": "Backup", "location": "C:\\Users\\<USER>\\AppData\\Local\\Backup", "type": "folder_scan"}, "cache": {"name": "cache", "location": "C:\\Users\\<USER>\\AppData\\Local\\cache", "type": "folder_scan"}, "cef": {"name": "CEF", "location": "C:\\Users\\<USER>\\AppData\\Local\\CEF", "type": "folder_scan"}, "checkpoint-nodejs": {"name": "checkpoint-nodejs", "location": "C:\\Users\\<USER>\\AppData\\Local\\checkpoint-nodejs", "type": "folder_scan"}, "comms": {"name": "<PERSON><PERSON><PERSON>", "location": "C:\\Users\\<USER>\\AppData\\Local\\Comms", "type": "folder_scan"}, "conda": {"name": "conda", "location": "C:\\Users\\<USER>\\AppData\\Local\\conda", "type": "folder_scan"}, "connecteddevicesplatform": {"name": "ConnectedDevicesPlatform", "location": "C:\\Users\\<USER>\\AppData\\Local\\ConnectedDevicesPlatform", "type": "folder_scan"}, "crashdumps": {"name": "CrashDumps", "location": "C:\\Users\\<USER>\\AppData\\Local\\CrashDumps", "type": "folder_scan"}, "crashreportclient": {"name": "CrashReportClient", "location": "C:\\Users\\<USER>\\AppData\\Local\\CrashReportClient", "type": "folder_scan"}, "crewai": {"name": "CrewAI", "location": "C:\\Users\\<USER>\\AppData\\Local\\CrewAI", "type": "folder_scan"}, "cursor-updater": {"name": "cursor-updater", "location": "C:\\Users\\<USER>\\AppData\\Local\\cursor-updater", "type": "folder_scan"}, "d3dscache": {"name": "D3DSCache", "location": "C:\\Users\\<USER>\\AppData\\Local\\D3DSCache", "type": "folder_scan"}, "davinci resolve welcome": {"name": "DaVinci Resolve Welcome", "location": "C:\\Users\\<USER>\\AppData\\Local\\DaVinci Resolve Welcome", "type": "folder_scan"}, "deadbydaylight": {"name": "DeadByDaylight", "location": "C:\\Users\\<USER>\\AppData\\Local\\DeadByDaylight", "type": "folder_scan"}, "diagnostics": {"name": "Diagnostics", "location": "C:\\Users\\<USER>\\AppData\\Local\\Diagnostics", "type": "folder_scan"}, "discord": {"name": "discord", "location": "C:\\Users\\<USER>\\AppData\\Roaming\\discord", "type": "folder_scan"}, "discovery": {"name": "Discovery", "location": "C:\\Users\\<USER>\\AppData\\Local\\Discovery", "type": "folder_scan"}, "epicgameslauncher": {"name": "EpicGamesLauncher", "location": "C:\\Users\\<USER>\\AppData\\Local\\EpicGamesLauncher", "type": "folder_scan"}, "fortnitegame": {"name": "FortniteGame", "location": "C:\\Users\\<USER>\\AppData\\Local\\FortniteGame", "type": "folder_scan"}, "githubdesktop": {"name": "GitHubDesktop", "location": "C:\\Users\\<USER>\\AppData\\Local\\GitHubDesktop", "type": "folder_scan"}, "gk": {"name": "gk", "location": "C:\\Users\\<USER>\\AppData\\Local\\gk", "type": "folder_scan"}, "go-build": {"name": "go-build", "location": "C:\\Users\\<USER>\\AppData\\Local\\go-build", "type": "folder_scan"}, "history": {"name": "History", "location": "C:\\Users\\<USER>\\AppData\\Local\\History", "type": "folder_scan"}, "inethistory": {"name": "INetHistory", "location": "C:\\Users\\<USER>\\AppData\\Local\\INetHistory", "type": "folder_scan"}, "kotlin": {"name": "kotlin", "location": "C:\\Users\\<USER>\\AppData\\Local\\kotlin", "type": "folder_scan"}, "main.kts.compiled.cache": {"name": "main.kts.compiled.cache", "location": "C:\\Users\\<USER>\\AppData\\Local\\main.kts.compiled.cache", "type": "folder_scan"}, "ms-playwright-go": {"name": "ms-playwright-go", "location": "C:\\Users\\<USER>\\AppData\\Local\\ms-playwright-go", "type": "folder_scan"}, "neo": {"name": "NEO", "location": "C:\\Users\\<USER>\\AppData\\Local\\NEO", "type": "folder_scan"}, "ngrok": {"name": "ngrok", "location": "C:\\Users\\<USER>\\AppData\\Roaming\\ngrok", "type": "folder_scan"}, "node-gyp": {"name": "node-gyp", "location": "C:\\Users\\<USER>\\AppData\\Local\\node-gyp", "type": "folder_scan"}, "npm-cache": {"name": "npm-cache", "location": "C:\\Users\\<USER>\\AppData\\Roaming\\npm-cache", "type": "folder_scan"}, "nzxt cam-updater": {"name": "nzxt cam-updater", "location": "C:\\Users\\<USER>\\AppData\\Local\\nzxt cam-updater", "type": "folder_scan"}, "ollama": {"name": "Ollama", "location": "C:\\Users\\<USER>\\AppData\\Local\\Ollama", "type": "folder_scan"}, "onedrive": {"name": "OneDrive", "location": "C:\\Users\\<USER>\\AppData\\Local\\OneDrive", "type": "folder_scan"}, "package cache": {"name": "Package Cache", "location": "C:\\Users\\<USER>\\AppData\\Local\\Package Cache", "type": "folder_scan"}, "packages": {"name": "Packages", "location": "C:\\Users\\<USER>\\AppData\\Local\\Packages", "type": "folder_scan"}, "peerdistrepub": {"name": "Peer<PERSON>ist<PERSON><PERSON>ub", "location": "C:\\Users\\<USER>\\AppData\\Local\\PeerDistRepub", "type": "folder_scan"}, "pip": {"name": "pip", "location": "C:\\Users\\<USER>\\AppData\\Local\\pip", "type": "folder_scan"}, "placeholdertilelogofolder": {"name": "PlaceholderTileLogoFolder", "location": "C:\\Users\\<USER>\\AppData\\Local\\PlaceholderTileLogoFolder", "type": "folder_scan"}, "pnpm": {"name": "pnpm", "location": "C:\\Users\\<USER>\\AppData\\Local\\pnpm", "type": "folder_scan"}, "pnpm-cache": {"name": "pnpm-cache", "location": "C:\\Users\\<USER>\\AppData\\Local\\pnpm-cache", "type": "folder_scan"}, "pnpm-state": {"name": "pnpm-state", "location": "C:\\Users\\<USER>\\AppData\\Local\\pnpm-state", "type": "folder_scan"}, "prisma-nodejs": {"name": "prisma-nodejs", "location": "C:\\Users\\<USER>\\AppData\\Local\\prisma-nodejs", "type": "folder_scan"}, "programs": {"name": "Programs", "location": "C:\\Users\\<USER>\\AppData\\Local\\Programs", "type": "folder_scan"}, "publishers": {"name": "Publishers", "location": "C:\\Users\\<USER>\\AppData\\Local\\Publishers", "type": "folder_scan"}, "puccinialin": {"name": "puccinialin", "location": "C:\\Users\\<USER>\\AppData\\Local\\puccinialin", "type": "folder_scan"}, "pypa": {"name": "pypa", "location": "C:\\Users\\<USER>\\AppData\\Local\\pypa", "type": "folder_scan"}, "roblox": {"name": "<PERSON><PERSON><PERSON>", "location": "C:\\Users\\<USER>\\AppData\\Local\\Roblox", "type": "folder_scan"}, "sema4ai": {"name": "sema4ai", "location": "C:\\Users\\<USER>\\AppData\\Local\\sema4ai", "type": "folder_scan"}, "shift": {"name": "Shift", "location": "C:\\Users\\<USER>\\AppData\\Local\\Shift", "type": "folder_scan"}, "speech": {"name": "speech", "location": "C:\\Users\\<USER>\\AppData\\Local\\speech", "type": "folder_scan"}, "squirreltemp": {"name": "SquirrelTemp", "location": "C:\\Users\\<USER>\\AppData\\Local\\SquirrelTemp", "type": "folder_scan"}, "temp": {"name": "Temp", "location": "C:\\Users\\<USER>\\AppData\\Local\\Temp", "type": "folder_scan"}, "temporary internet files": {"name": "Temporary Internet Files", "location": "C:\\Users\\<USER>\\AppData\\Local\\Temporary Internet Files", "type": "folder_scan"}, "unrealengine": {"name": "UnrealEngine", "location": "C:\\Users\\<USER>\\AppData\\Local\\UnrealEngine", "type": "folder_scan"}, "unrealenginelauncher": {"name": "UnrealEngineLauncher", "location": "C:\\Users\\<USER>\\AppData\\Local\\UnrealEngineLauncher", "type": "folder_scan"}, "update-informer-rs": {"name": "update-informer-rs", "location": "C:\\Users\\<USER>\\AppData\\Local\\update-informer-rs", "type": "folder_scan"}, "virtualstore": {"name": "VirtualStore", "location": "C:\\Users\\<USER>\\AppData\\Local\\VirtualStore", "type": "folder_scan"}, "vscode-sqltools": {"name": "vscode-sqltools", "location": "C:\\Users\\<USER>\\AppData\\Roaming\\vscode-sqltools", "type": "folder_scan"}, "warp": {"name": "warp", "location": "C:\\Users\\<USER>\\AppData\\Roaming\\warp", "type": "folder_scan"}, "yarn": {"name": "Yarn", "location": "C:\\Users\\<USER>\\AppData\\Local\\Yarn", "type": "folder_scan"}, ".anaconda": {"name": ".anaconda", "location": "C:\\Users\\<USER>\\AppData\\Roaming\\.anaconda", "type": "folder_scan"}, "adobe": {"name": "Adobe", "location": "C:\\Users\\<USER>\\AppData\\Roaming\\Adobe", "type": "folder_scan"}, "code": {"name": "Code", "location": "C:\\Users\\<USER>\\AppData\\Roaming\\Code", "type": "folder_scan"}, "coderabbit": {"name": "CodeRabbit", "location": "C:\\Users\\<USER>\\AppData\\Roaming\\CodeRabbit", "type": "folder_scan"}, "cody-nodejs": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "location": "C:\\Users\\<USER>\\AppData\\Roaming\\Cody-nodejs", "type": "folder_scan"}, "composer": {"name": "Composer", "location": "C:\\Users\\<USER>\\AppData\\Roaming\\Composer", "type": "folder_scan"}, "create-next-app-nodejs": {"name": "create-next-app-nodejs", "location": "C:\\Users\\<USER>\\AppData\\Roaming\\create-next-app-nodejs", "type": "folder_scan"}, "cursor": {"name": "<PERSON><PERSON><PERSON>", "location": "C:\\Users\\<USER>\\AppData\\Roaming\\Cursor", "type": "folder_scan"}, "easyanticheat": {"name": "EasyAntiCheat", "location": "C:\\Users\\<USER>\\AppData\\Roaming\\EasyAntiCheat", "type": "folder_scan"}, "github desktop": {"name": "GitHub Desktop", "location": "C:\\Users\\<USER>\\AppData\\Roaming\\GitHub Desktop", "type": "folder_scan"}, "nextjs-nodejs": {"name": "nextjs-nodejs", "location": "C:\\Users\\<USER>\\AppData\\Roaming\\nextjs-nodejs", "type": "folder_scan"}, "npm": {"name": "npm", "location": "C:\\Users\\<USER>\\AppData\\Roaming\\npm", "type": "folder_scan"}, "python": {"name": "Python", "location": "C:\\Users\\<USER>\\AppData\\Roaming\\Python", "type": "folder_scan"}, "smithery": {"name": "smithery", "location": "C:\\Users\\<USER>\\AppData\\Roaming\\smithery", "type": "folder_scan"}, "trae": {"name": "<PERSON><PERSON>", "location": "C:\\Users\\<USER>\\AppData\\Roaming\\Trae", "type": "folder_scan"}, "turborepo": {"name": "turborepo", "location": "C:\\Users\\<USER>\\AppData\\Roaming\\turborepo", "type": "folder_scan"}, "visual studio setup": {"name": "Visual Studio Setup", "location": "C:\\Users\\<USER>\\AppData\\Roaming\\Visual Studio Setup", "type": "folder_scan"}, "voiceaccess": {"name": "VoiceAccess", "location": "C:\\Users\\<USER>\\AppData\\Roaming\\VoiceAccess", "type": "folder_scan"}, "windsurf": {"name": "Windsurf", "location": "C:\\Users\\<USER>\\AppData\\Roaming\\Windsurf", "type": "folder_scan"}}, "file_locations": {"desktop": "C:\\Users\\<USER>", "documents": "C:\\Users\\<USER>", "downloads": "C:\\Users\\<USER>", "pictures": "C:\\Users\\<USER>", "videos": "C:\\Users\\<USER>", "music": "C:\\Users\\<USER>", "appdata": "C:\\Users\\<USER>", "temp": "C:\\Users\\<USER>\\AppData\\Local\\Temp", "program_files": "C:\\Program Files", "program_files_x86": "C:\\Program Files (x86)", "windows": "C:\\Windows", "system32": "C:\\Windows\\System32", "business_folders": ["C:\\Users\\<USER>\\My Music", "C:\\Users\\<USER>\\My Pictures", "C:\\Users\\<USER>\\My Videos"]}, "ui_elements": {"screen_resolution": "2560x1080", "taskbar": {"position": "bottom", "height": 40, "start_button": [50, 1030], "system_tray": [2460, 1030]}, "window_controls": {"close": [2540, 20], "minimize": [2500, 20], "maximize": [2520, 20]}, "common_areas": {"top_left": [50, 50], "top_right": [2510, 50], "center": [1280, 540], "bottom_left": [50, 1030], "bottom_right": [2510, 1030]}}, "shortcuts": {"windows_shortcuts": {"win": "Open Start Menu", "win+r": "Run Dialog", "win+e": "File Explorer", "win+d": "Show Desktop", "win+l": "Lock Computer", "win+i": "Settings", "win+x": "Power User Menu", "alt+tab": "Switch Applications", "alt+f4": "Close Application", "ctrl+shift+esc": "Task Manager"}, "office_shortcuts": {"ctrl+n": "New Document", "ctrl+o": "Open", "ctrl+s": "Save", "ctrl+p": "Print", "ctrl+z": "Undo", "ctrl+y": "Redo", "ctrl+c": "Copy", "ctrl+v": "Paste", "ctrl+x": "Cut", "ctrl+a": "Select All"}, "browser_shortcuts": {"ctrl+t": "New Tab", "ctrl+w": "Close Tab", "ctrl+shift+t": "Reopen Closed Tab", "ctrl+l": "Address Bar", "ctrl+d": "Bookmark", "f5": "Refresh", "ctrl+f": "Find on Page"}, "system_commands": {"calc": "Launch calc", "notepad": "Launch notepad", "mspaint": "Launch mspaint", "explorer": "Launch explorer", "cmd": "Launch cmd", "powershell": "Launch powershell", "taskmgr": "Launch taskmgr", "msconfig": "Launch msconfig", "regedit": "Launch regedit", "services.msc": "Launch services.msc", "devmgmt.msc": "Launch devmgmt.msc"}}, "common_paths": {}, "user_preferences": {}, "running_processes": {"Registry": {"pid": 472, "executable": "Registry", "name": "Registry"}, "smss.exe": {"pid": 1088, "executable": "C:\\Windows\\System32\\smss.exe", "name": "smss.exe"}, "chrome.exe": {"pid": 52920, "executable": "C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe", "name": "chrome.exe"}, "node.exe": {"pid": 47700, "executable": "C:\\Program Files\\nodejs\\node.exe", "name": "node.exe"}, "svchost.exe": {"pid": 54464, "executable": "C:\\Windows\\System32\\svchost.exe", "name": "svchost.exe"}, "NVDisplay.Container.exe": {"pid": 2972, "executable": "C:\\Windows\\System32\\DriverStore\\FileRepository\\nv_dispi.inf_amd64_1e8724cced6e93d4\\Display.NvContainer\\NVDisplay.Container.exe", "name": "NVDisplay.Container.exe"}, "csrss.exe": {"pid": 1624, "executable": "C:\\Windows\\System32\\csrss.exe", "name": "csrss.exe"}, "wininit.exe": {"pid": 1616, "executable": "C:\\Windows\\System32\\wininit.exe", "name": "wininit.exe"}, "nvcontainer.exe": {"pid": 11900, "executable": "C:\\Program Files\\NVIDIA Corporation\\NvContainer\\nvcontainer.exe", "name": "nvcontainer.exe"}, "winlogon.exe": {"pid": 1712, "executable": "C:\\Windows\\System32\\winlogon.exe", "name": "winlogon.exe"}, "services.exe": {"pid": 1740, "executable": "C:\\Windows\\System32\\services.exe", "name": "services.exe"}, "LsaIso.exe": {"pid": 1780, "executable": "C:\\Windows\\System32\\LsaIso.exe", "name": "LsaIso.exe"}, "lsass.exe": {"pid": 1788, "executable": "C:\\Windows\\System32\\lsass.exe", "name": "lsass.exe"}, "postgres.exe": {"pid": 10516, "executable": "C:\\Program Files\\PostgreSQL\\17\\bin\\postgres.exe", "name": "postgres.exe"}, "fontdrvhost.exe": {"pid": 2016, "executable": "C:\\Windows\\System32\\fontdrvhost.exe", "name": "fontdrvhost.exe"}, "conhost.exe": {"pid": 51880, "executable": "C:\\Windows\\System32\\conhost.exe", "name": "conhost.exe"}, "dwm.exe": {"pid": 2108, "executable": "C:\\Windows\\System32\\dwm.exe", "name": "dwm.exe"}, "wsc_proxy.exe": {"pid": 2216, "executable": "C:\\Program Files\\Avast Software\\Avast\\wsc_proxy.exe", "name": "wsc_proxy.exe"}, "IntelCpHDCPSvc.exe": {"pid": 2308, "executable": "C:\\Windows\\System32\\DriverStore\\FileRepository\\iigd_dch.inf_amd64_58f71ae65c958b28\\IntelCpHDCPSvc.exe", "name": "IntelCpHDCPSvc.exe"}, "com.docker.build.exe": {"pid": 2592, "executable": "C:\\Program Files\\Docker\\Docker\\resources\\com.docker.build.exe", "name": "com.docker.build.exe"}, "vmms.exe": {"pid": 2860, "executable": "C:\\Windows\\System32\\vmms.exe", "name": "vmms.exe"}, "MemCompression": {"pid": 3252, "executable": "MemCompression", "name": "MemCompression"}, "Cursor.exe": {"pid": 55292, "executable": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\Cursor.exe", "name": "Cursor.exe"}, "taskhostw.exe": {"pid": 4160, "executable": "C:\\Windows\\System32\\taskhostw.exe", "name": "taskhostw.exe"}, "Trae.exe": {"pid": 45740, "executable": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Trae\\Trae.exe", "name": "Trae.exe"}, "AvastSvc.exe": {"pid": 4412, "executable": "C:\\Program Files\\Avast Software\\Avast\\AvastSvc.exe", "name": "AvastSvc.exe"}, "aswToolsSvc.exe": {"pid": 4844, "executable": "C:\\Program Files\\Avast Software\\Avast\\aswToolsSvc.exe", "name": "aswToolsSvc.exe"}, "SearchIndexer.exe": {"pid": 4868, "executable": "C:\\Windows\\System32\\SearchIndexer.exe", "name": "SearchIndexer.exe"}, "spoolsv.exe": {"pid": 4964, "executable": "C:\\Windows\\System32\\spoolsv.exe", "name": "spoolsv.exe"}, "aswEngSrv.exe": {"pid": 5384, "executable": "C:\\Program Files\\Avast Software\\Avast\\aswEngSrv.exe", "name": "aswEngSrv.exe"}, "afwServ.exe": {"pid": 5724, "executable": "C:\\Program Files\\Avast Software\\Avast\\afwServ.exe", "name": "afwServ.exe"}, "SearchProtocolHost.exe": {"pid": 5948, "executable": "C:\\Windows\\System32\\SearchProtocolHost.exe", "name": "SearchProtocolHost.exe"}, "AnyDesk.exe": {"pid": 6316, "executable": "C:\\Program Files (x86)\\AnyDesk\\AnyDesk.exe", "name": "AnyDesk.exe"}, "service.exe": {"pid": 6352, "executable": "C:\\Program Files\\NZXT CAM\\resources\\app.asar.unpacked\\node_modules\\@nzxt\\cam-core\\dist\\service.exe", "name": "service.exe"}, "WMIRegistrationService.exe": {"pid": 6360, "executable": "C:\\Windows\\System32\\DriverStore\\FileRepository\\mewmiprov.inf_amd64_58a0ea2de06916f7\\WMIRegistrationService.exe", "name": "WMIRegistrationService.exe"}, "OneApp.IGCC.WinService.exe": {"pid": 6452, "executable": "C:\\Windows\\System32\\DriverStore\\FileRepository\\igcc_dch.inf_amd64_ee28fa9e734aabda\\OneApp.IGCC.WinService.exe", "name": "OneApp.IGCC.WinService.exe"}, "redis-server.exe": {"pid": 6460, "executable": "C:\\Program Files\\Redis\\redis-server.exe", "name": "redis-server.exe"}, "jhi_service.exe": {"pid": 6472, "executable": "C:\\Windows\\System32\\DriverStore\\FileRepository\\dal.inf_amd64_8a3f88e34f6b8385\\jhi_service.exe", "name": "jhi_service.exe"}, "RtkAudUService64.exe": {"pid": 17188, "executable": "C:\\Windows\\System32\\DriverStore\\FileRepository\\realtekservice.inf_amd64_3c5a1ee29345a7e2\\RtkAudUService64.exe", "name": "RtkAudUService64.exe"}, "pg_ctl.exe": {"pid": 6508, "executable": "C:\\Program Files\\PostgreSQL\\17\\bin\\pg_ctl.exe", "name": "pg_ctl.exe"}, "wslservice.exe": {"pid": 6516, "executable": "C:\\Program Files\\WSL\\wslservice.exe", "name": "wslservice.exe"}, "gamingservices.exe": {"pid": 7420, "executable": "C:\\Program Files\\WindowsApps\\Microsoft.GamingServices_29.103.2001.0_x64__8wekyb3d8bbwe\\gamingservices.exe", "name": "gamingservices.exe"}, "gamingservicesnet.exe": {"pid": 7428, "executable": "C:\\Program Files\\WindowsApps\\Microsoft.GamingServices_29.103.2001.0_x64__8wekyb3d8bbwe\\gamingservicesnet.exe", "name": "gamingservicesnet.exe"}, "IntelGraphicsSoftware.Service.exe": {"pid": 7440, "executable": "C:\\Program Files\\WindowsApps\\AppUp.IntelArcSoftware_25.22.1502.0_x64__8j3eq9eme6ctt\\VFS\\ProgramFilesX64\\Intel\\Intel Graphics Software\\IntelGraphicsSoftware.Service.exe", "name": "IntelGraphicsSoftware.Service.exe"}, "pgagent.exe": {"pid": 7828, "executable": "C:\\PROGRA~1\\POSTGR~1\\17\\bin\\pgagent.exe", "name": "pgagent.exe"}, "RuntimeBroker.exe": {"pid": 24852, "executable": "C:\\Windows\\System32\\RuntimeBroker.exe", "name": "RuntimeBroker.exe"}, "SearchHost.exe": {"pid": 8824, "executable": "C:\\Windows\\SystemApps\\MicrosoftWindows.Client.CBS_cw5n1h2txyewy\\SearchHost.exe", "name": "SearchHost.exe"}, "aswidsagent.exe": {"pid": 8876, "executable": "C:\\Program Files\\Avast Software\\Avast\\aswidsagent.exe", "name": "aswidsagent.exe"}, "dasHost.exe": {"pid": 8928, "executable": "C:\\Windows\\System32\\dasHost.exe", "name": "dasHost.exe"}, "PresentMonService.exe": {"pid": 9220, "executable": "C:\\Program Files\\WindowsApps\\AppUp.IntelArcSoftware_25.22.1502.0_x64__8j3eq9eme6ctt\\VFS\\ProgramFilesX64\\Intel\\Intel Graphics Software\\PresentMonService.exe", "name": "PresentMonService.exe"}, "docker-language-server-windows-amd64.exe": {"pid": 48296, "executable": "C:\\Users\\<USER>\\.cursor\\extensions\\docker.docker-0.10.0-win32-x64\\bin\\docker-language-server-windows-amd64.exe", "name": "docker-language-server-windows-amd64.exe"}, "AggregatorHost.exe": {"pid": 10136, "executable": "C:\\Windows\\System32\\AggregatorHost.exe", "name": "AggregatorHost.exe"}, "unsecapp.exe": {"pid": 10416, "executable": "C:\\Windows\\System32\\wbem\\unsecapp.exe", "name": "unsecapp.exe"}, "powershell.exe": {"pid": 51544, "executable": "C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\powershell.exe", "name": "powershell.exe"}, "vmcompute.exe": {"pid": 11180, "executable": "C:\\Windows\\System32\\vmcompute.exe", "name": "vmcompute.exe"}, "WidgetService.exe": {"pid": 11208, "executable": "C:\\Program Files\\WindowsApps\\Microsoft.WidgetsPlatformRuntime_1.6.9.0_x64__8wekyb3d8bbwe\\WidgetService\\WidgetService.exe", "name": "WidgetService.exe"}, "sihost.exe": {"pid": 11848, "executable": "C:\\Windows\\System32\\sihost.exe", "name": "sihost.exe"}, "EpicGamesLauncher.exe": {"pid": 11868, "executable": "C:\\Program Files (x86)\\Epic Games\\Launcher\\Portal\\Binaries\\Win64\\EpicGamesLauncher.exe", "name": "EpicGamesLauncher.exe"}, "brave.exe": {"pid": 44052, "executable": "C:\\Program Files\\BraveSoftware\\Brave-Browser\\Application\\brave.exe", "name": "brave.exe"}, "wsl.exe": {"pid": 38872, "executable": "C:\\Program Files\\WSL\\wsl.exe", "name": "wsl.exe"}, "shift.exe": {"pid": 48972, "executable": "C:\\Users\\<USER>\\AppData\\Local\\Shift\\chromium\\shift.exe", "name": "shift.exe"}, "explorer.exe": {"pid": 12608, "executable": "C:\\Windows\\explorer.exe", "name": "explorer.exe"}, "EpicWebHelper.exe": {"pid": 19060, "executable": "C:\\Program Files (x86)\\Epic Games\\Launcher\\Engine\\Binaries\\Win64\\EpicWebHelper.exe", "name": "EpicWebHelper.exe"}, "ShellHost.exe": {"pid": 12640, "executable": "C:\\Windows\\System32\\ShellHost.exe", "name": "ShellHost.exe"}, "cmd.exe": {"pid": 46612, "executable": "C:\\Windows\\System32\\cmd.exe", "name": "cmd.exe"}, "SearchFilterHost.exe": {"pid": 53364, "executable": "C:\\Windows\\System32\\SearchFilterHost.exe", "name": "SearchFilterHost.exe"}, "nvsphelper64.exe": {"pid": 13396, "executable": "C:\\Program Files\\NVIDIA Corporation\\NVIDIA app\\ShadowPlay\\nvsphelper64.exe", "name": "nvsphelper64.exe"}, "CrossDeviceResume.exe": {"pid": 13512, "executable": "C:\\Windows\\SystemApps\\MicrosoftWindows.Client.CBS_cw5n1h2txyewy\\CrossDeviceResume.exe", "name": "CrossDeviceResume.exe"}, "Widgets.exe": {"pid": 14016, "executable": "C:\\Program Files\\WindowsApps\\MicrosoftWindows.Client.WebExperience_525.15301.20.0_x64__cw5n1h2txyewy\\Dashboard\\Widgets.exe", "name": "Widgets.exe"}, "NVIDIA Overlay.exe": {"pid": 15188, "executable": "C:\\Program Files\\NVIDIA Corporation\\NVIDIA app\\CEF\\NVIDIA Overlay.exe", "name": "NVIDIA Overlay.exe"}, "rust-analyzer.exe": {"pid": 39244, "executable": "C:\\Users\\<USER>\\.cursor\\extensions\\rust-lang.rust-analyzer-0.3.2533-win32-x64\\server\\rust-analyzer.exe", "name": "rust-analyzer.exe"}, "StartMenuExperienceHost.exe": {"pid": 14920, "executable": "C:\\Windows\\SystemApps\\Microsoft.Windows.StartMenuExperienceHost_cw5n1h2txyewy\\StartMenuExperienceHost.exe", "name": "StartMenuExperienceHost.exe"}, "UserOOBEBroker.exe": {"pid": 15108, "executable": "C:\\Windows\\System32\\oobe\\UserOOBEBroker.exe", "name": "UserOOBEBroker.exe"}, "msedgewebview2.exe": {"pid": 16844, "executable": "C:\\Program Files (x86)\\Microsoft\\EdgeWebView\\Application\\138.0.3351.77\\msedgewebview2.exe", "name": "msedgewebview2.exe"}, "ctfmon.exe": {"pid": 16376, "executable": "C:\\Windows\\System32\\ctfmon.exe", "name": "ctfmon.exe"}, "SystemSettings.exe": {"pid": 16664, "executable": "C:\\Windows\\ImmersiveControlPanel\\SystemSettings.exe", "name": "SystemSettings.exe"}, "ui32.exe": {"pid": 20696, "executable": "C:\\Program Files (x86)\\Steam\\steamapps\\common\\wallpaper_engine\\bin\\ui32.exe", "name": "ui32.exe"}, "SecurityHealthService.exe": {"pid": 16876, "executable": "C:\\Windows\\System32\\SecurityHealthService.exe", "name": "SecurityHealthService.exe"}, "SecurityHealthSystray.exe": {"pid": 17396, "executable": "C:\\Windows\\System32\\SecurityHealthSystray.exe", "name": "SecurityHealthSystray.exe"}, "AvastUI.exe": {"pid": 35592, "executable": "C:\\Program Files\\Avast Software\\Avast\\AvastUI.exe", "name": "AvastUI.exe"}, "OneDrive.exe": {"pid": 17524, "executable": "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\OneDrive\\OneDrive.exe", "name": "OneDrive.exe"}, "vmwp.exe": {"pid": 17644, "executable": "C:\\Windows\\System32\\vmwp.exe", "name": "vmwp.exe"}, "dllhost.exe": {"pid": 24228, "executable": "C:\\Windows\\System32\\dllhost.exe", "name": "dllhost.exe"}, "NZXT CAM.exe": {"pid": 18576, "executable": "C:\\Program Files\\NZXT CAM\\NZXT CAM.exe", "name": "NZXT CAM.exe"}, "TextInputHost.exe": {"pid": 19108, "executable": "C:\\Windows\\SystemApps\\MicrosoftWindows.Client.CBS_cw5n1h2txyewy\\TextInputHost.exe", "name": "TextInputHost.exe"}, "cam_helper.exe": {"pid": 19736, "executable": "C:\\Program Files\\NZXT CAM\\resources\\app.asar.unpacked\\node_modules\\@nzxt\\cam-core\\dist\\cam_helper.exe", "name": "cam_helper.exe"}, "wallpaper32.exe": {"pid": 20228, "executable": "C:\\Program Files (x86)\\Steam\\steamapps\\common\\wallpaper_engine\\wallpaper32.exe", "name": "wallpaper32.exe"}, "PAD.BridgeToUIAutomation2.exe": {"pid": 20888, "executable": "C:\\Program Files\\WindowsApps\\Microsoft.PowerAutomateDesktop_11.2506.143.0_x64__8wekyb3d8bbwe\\dotnet\\PAD.BridgeToUIAutomation2.exe", "name": "PAD.BridgeToUIAutomation2.exe"}, "PAD.AutomationServer.exe": {"pid": 20896, "executable": "C:\\Program Files\\WindowsApps\\Microsoft.PowerAutomateDesktop_11.2506.143.0_x64__8wekyb3d8bbwe\\dotnet\\PAD.AutomationServer.exe", "name": "PAD.AutomationServer.exe"}, "TranslucentTB.exe": {"pid": 21896, "executable": "C:\\Program Files\\WindowsApps\\28017CharlesMilette.TranslucentTB_2025.1.0.0_x64__v826wp6bftszj\\TranslucentTB.exe", "name": "TranslucentTB.exe"}, "Slack.exe": {"pid": 37000, "executable": "C:\\Program Files\\WindowsApps\\91750D7E.Slack_4.44.63.0_x64__8she8kybcnzg4\\app\\Slack.exe", "name": "Slack.exe"}, "PAD.Console.Host.exe": {"pid": 22984, "executable": "C:\\Program Files\\WindowsApps\\Microsoft.PowerAutomateDesktop_11.2506.143.0_x64__8wekyb3d8bbwe\\dotnet\\PAD.Console.Host.exe", "name": "PAD.Console.Host.exe"}, "XboxPcAppFT.exe": {"pid": 23324, "executable": "C:\\Program Files\\WindowsApps\\Microsoft.GamingApp_2506.1001.20.0_x64__8wekyb3d8bbwe\\XboxPcAppFT.exe", "name": "XboxPcAppFT.exe"}, "XboxPcTray.exe": {"pid": 23428, "executable": "C:\\Program Files\\WindowsApps\\Microsoft.GamingApp_2506.1001.20.0_x64__8wekyb3d8bbwe\\XboxPcTray.exe", "name": "XboxPcTray.exe"}, "CrossDeviceService.exe": {"pid": 23580, "executable": "C:\\Program Files\\WindowsApps\\MicrosoftWindows.CrossDevice_1.25061.25.0_x64__cw5n1h2txyewy\\CrossDeviceService.exe", "name": "CrossDeviceService.exe"}, "XboxPcApp.exe": {"pid": 23664, "executable": "C:\\Program Files\\WindowsApps\\Microsoft.GamingApp_2506.1001.20.0_x64__8wekyb3d8bbwe\\XboxPcApp.exe", "name": "XboxPcApp.exe"}, "ollama app.exe": {"pid": 23912, "executable": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Ollama\\ollama app.exe", "name": "ollama app.exe"}, "wslhost.exe": {"pid": 41936, "executable": "C:\\Program Files\\WSL\\wslhost.exe", "name": "wslhost.exe"}, "ApplicationFrameHost.exe": {"pid": 24160, "executable": "C:\\Windows\\System32\\ApplicationFrameHost.exe", "name": "ApplicationFrameHost.exe"}, "ShellExperienceHost.exe": {"pid": 24724, "executable": "C:\\Windows\\SystemApps\\ShellExperienceHost_cw5n1h2txyewy\\ShellExperienceHost.exe", "name": "ShellExperienceHost.exe"}, "Lingma.exe": {"pid": 27816, "executable": "C:\\Users\\<USER>\\.lingma\\bin\\2.5.15\\x86_64_windows\\Lingma.exe", "name": "Lingma.exe"}, "Docker Desktop.exe": {"pid": 41280, "executable": "C:\\Program Files\\Docker\\Docker\\frontend\\Docker Desktop.exe", "name": "Docker Desktop.exe"}, "vmmemWSL": {"pid": 28528, "executable": "vmmemWSL", "name": "vmmemWSL"}, "PhoneExperienceHost.exe": {"pid": 29436, "executable": "C:\\Program Files\\WindowsApps\\Microsoft.YourPhone_1.25061.44.0_x64__8wekyb3d8bbwe\\PhoneExperienceHost.exe", "name": "PhoneExperienceHost.exe"}, "python.exe": {"pid": 53188, "executable": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\python.exe", "name": "python.exe"}, "PAD.BrowserNativeMessageHost.exe": {"pid": 31496, "executable": "C:\\Program Files\\WindowsApps\\Microsoft.PowerAutomateDesktop_11.2506.143.0_x64__8wekyb3d8bbwe\\dotnet\\PAD.BrowserNativeMessageHost.exe", "name": "PAD.BrowserNativeMessageHost.exe"}, "wslrelay.exe": {"pid": 36496, "executable": "C:\\Program Files\\WSL\\wslrelay.exe", "name": "wslrelay.exe"}, "com.docker.backend.exe": {"pid": 37052, "executable": "C:\\Program Files\\Docker\\Docker\\resources\\com.docker.backend.exe", "name": "com.docker.backend.exe"}, "manager.exe": {"pid": 38296, "executable": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Trae\\resources\\app\\bin\\manager.exe", "name": "manager.exe"}, "msrdc.exe": {"pid": 41224, "executable": "C:\\Program Files\\WSL\\msrdc.exe", "name": "msrdc.exe"}, "WmiPrvSE.exe": {"pid": 42816, "executable": "C:\\Windows\\System32\\wbem\\WmiPrvSE.exe", "name": "WmiPrvSE.exe"}, "ckg_server_windows_x64.exe": {"pid": 44392, "executable": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Trae\\resources\\app\\modules\\ckg\\binary\\ckg_server_windows_x64.exe", "name": "ckg_server_windows_x64.exe"}, "ai-agent.exe": {"pid": 46632, "executable": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Trae\\resources\\app\\modules\\ai-agent\\ai-agent.exe", "name": "ai-agent.exe"}, "esbuild.exe": {"pid": 47004, "executable": "C:\\Users\\<USER>\\Downloads\\ep app\\project\\node_modules\\@esbuild\\win32-x64\\esbuild.exe", "name": "esbuild.exe"}, "ollama.exe": {"pid": 49100, "executable": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Ollama\\ollama.exe", "name": "ollama.exe"}}, "metadata": {"created": "2025-07-10 19:34:43", "computer_name": "DESKTOP-15R4F5N", "username": "<PERSON>", "os_version": "nt"}}