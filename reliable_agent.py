#!/usr/bin/env python3
"""
Reliable Agent-S - Simple, Direct, and Actually Works
No over-analysis, just gets things done
"""

import os
import time
import logging
import pyautogui

# AI imports
try:
    import openai
    HAS_OPENAI = True
except ImportError:
    HAS_OPENAI = False

# Configure
pyautogui.PAUSE = 0.5
pyautogui.FAILSAFE = True
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class ReliableAgent:
    """Simple, reliable agent that actually works"""
    
    def __init__(self):
        logger.info("🚀 Starting Reliable Agent...")
        
        # Simple AI setup
        self.ai_client = None
        if HAS_OPENAI and os.getenv('OPENAI_API_KEY'):
            try:
                self.ai_client = openai.OpenAI(api_key=os.getenv('OPENAI_API_KEY'))
                logger.info("✅ AI enabled")
            except Exception as e:
                logger.warning(f"AI failed: {e}")
        
        # Stats
        self.stats = {"completed": 0, "failed": 0}
        
        logger.info("✅ Reliable Agent ready!")
    
    def execute_task(self, user_request):
        """Execute task reliably without over-analysis"""
        start_time = time.time()
        request_lower = user_request.lower()
        
        logger.info(f"🎯 EXECUTING: {user_request}")
        
        try:
            # Direct task routing - no complex analysis
            if "browser" in request_lower and "search" in request_lower:
                return self._browser_and_search(user_request, start_time)
            elif "browser" in request_lower:
                return self._open_browser(start_time)
            elif "search" in request_lower:
                return self._search_only(user_request, start_time)
            elif "screenshot" in request_lower:
                return self._take_screenshot(start_time)
            elif "calculator" in request_lower:
                return self._open_calculator(start_time)
            else:
                return self._ask_user_what_to_do(user_request, start_time)
                
        except Exception as e:
            logger.error(f"❌ Task failed: {e}")
            return self._handle_failure(str(e), start_time)
    
    def _browser_and_search(self, user_request, start_time):
        """Open browser and search - the most common request"""
        logger.info("🌐 Opening browser and searching...")
        
        # Step 1: Open browser directly
        success = self._open_browser_direct()
        if not success:
            return self._ask_for_help("Failed to open browser", user_request, start_time)
        
        # Step 2: Wait for browser to load
        time.sleep(3)
        
        # Step 3: Extract search query
        search_query = self._extract_search_query(user_request)
        
        # Step 4: Search directly
        success = self._search_direct(search_query)
        if not success:
            return self._ask_for_help("Failed to search", user_request, start_time)
        
        # Success!
        execution_time = time.time() - start_time
        self.stats["completed"] += 1
        
        return {
            "success": True,
            "execution_time": execution_time,
            "action": "browser_and_search",
            "query": search_query
        }
    
    def _open_browser_direct(self):
        """Open browser with simple, reliable method"""
        try:
            # Method 1: Try Chrome directly
            logger.info("🔍 Opening Chrome...")
            pyautogui.press('win')
            time.sleep(1)
            pyautogui.write('chrome')
            time.sleep(1)
            pyautogui.press('enter')
            time.sleep(2)
            return True
            
        except Exception as e:
            logger.warning(f"Chrome failed: {e}")
            
            try:
                # Method 2: Try Edge
                logger.info("🔍 Trying Edge...")
                pyautogui.press('win')
                time.sleep(1)
                pyautogui.write('edge')
                time.sleep(1)
                pyautogui.press('enter')
                time.sleep(2)
                return True
                
            except Exception as e:
                logger.warning(f"Edge failed: {e}")
                return False
    
    def _search_direct(self, query):
        """Search directly in browser"""
        try:
            logger.info(f"🔍 Searching for: {query}")
            
            # Click address bar
            pyautogui.hotkey('ctrl', 'l')
            time.sleep(1)
            
            # Type search query
            pyautogui.write(query)
            time.sleep(1)
            
            # Press enter
            pyautogui.press('enter')
            time.sleep(2)
            
            return True
            
        except Exception as e:
            logger.error(f"Search failed: {e}")
            return False
    
    def _extract_search_query(self, user_request):
        """Extract search query from user request"""
        request_lower = user_request.lower()
        
        # Common search terms
        if "bitcoin" in request_lower or "crypto" in request_lower:
            return "bitcoin price"
        elif "weather" in request_lower:
            return "weather today"
        elif "news" in request_lower:
            return "latest news"
        else:
            # Extract words after "search"
            words = user_request.split()
            try:
                search_index = next(i for i, word in enumerate(words) if "search" in word.lower())
                if search_index + 1 < len(words):
                    return " ".join(words[search_index + 1:])
            except:
                pass
            
            return "bitcoin price"  # Default
    
    def _open_browser(self, start_time):
        """Just open browser"""
        success = self._open_browser_direct()
        execution_time = time.time() - start_time
        
        if success:
            self.stats["completed"] += 1
            return {"success": True, "execution_time": execution_time, "action": "browser_opened"}
        else:
            return self._ask_for_help("Failed to open browser", "open browser", start_time)
    
    def _search_only(self, user_request, start_time):
        """Search in existing browser"""
        query = self._extract_search_query(user_request)
        success = self._search_direct(query)
        execution_time = time.time() - start_time
        
        if success:
            self.stats["completed"] += 1
            return {"success": True, "execution_time": execution_time, "action": "search", "query": query}
        else:
            return self._ask_for_help("Failed to search", user_request, start_time)
    
    def _take_screenshot(self, start_time):
        """Take screenshot"""
        try:
            screenshot = pyautogui.screenshot()
            filename = f"screenshot_{int(time.time())}.png"
            screenshot.save(filename)
            
            execution_time = time.time() - start_time
            self.stats["completed"] += 1
            
            return {
                "success": True,
                "execution_time": execution_time,
                "action": "screenshot",
                "filename": filename
            }
            
        except Exception as e:
            return self._handle_failure(str(e), start_time)
    
    def _open_calculator(self, start_time):
        """Open calculator"""
        try:
            pyautogui.press('win')
            time.sleep(1)
            pyautogui.write('calculator')
            time.sleep(1)
            pyautogui.press('enter')
            time.sleep(2)
            
            execution_time = time.time() - start_time
            self.stats["completed"] += 1
            
            return {"success": True, "execution_time": execution_time, "action": "calculator"}
            
        except Exception as e:
            return self._handle_failure(str(e), start_time)
    
    def _ask_user_what_to_do(self, user_request, start_time):
        """Ask user for clarification"""
        print(f"\n❓ I'm not sure how to: '{user_request}'")
        print("I can help with:")
        print("• 'open browser and search [something]'")
        print("• 'open browser'")
        print("• 'search [something]'")
        print("• 'take screenshot'")
        print("• 'open calculator'")
        
        while True:
            try:
                clarification = input("\n🤔 What would you like me to do? ").strip()
                if clarification:
                    return self.execute_task(clarification)
                    
            except KeyboardInterrupt:
                return self._handle_failure("User cancelled", start_time)
    
    def _ask_for_help(self, issue, original_request, start_time):
        """Ask for help when stuck"""
        execution_time = time.time() - start_time
        
        print(f"\n🆘 I NEED HELP!")
        print(f"Task: {original_request}")
        print(f"Issue: {issue}")
        print(f"Time: {execution_time:.1f}s")
        
        print("\nWhat should I do?")
        print("1. 'retry' - Try again")
        print("2. 'manual' - You tell me step by step")
        print("3. 'skip' - Give up on this task")
        
        while True:
            try:
                help_choice = input("\n🆘 Your choice: ").strip().lower()
                
                if help_choice == 'retry':
                    print("🔄 Retrying...")
                    return self.execute_task(original_request)
                
                elif help_choice == 'manual':
                    return self._manual_mode(original_request, start_time)
                
                elif help_choice == 'skip':
                    print("⏭️ Skipping task")
                    return self._handle_failure("User skipped", start_time)
                
                else:
                    print("❌ Please choose: retry, manual, or skip")
                    
            except KeyboardInterrupt:
                return self._handle_failure("User cancelled", start_time)
    
    def _manual_mode(self, original_request, start_time):
        """Manual step-by-step mode"""
        print(f"\n📝 MANUAL MODE")
        print(f"Original task: {original_request}")
        print("Tell me what to do step by step:")
        print("• 'click X Y' - Click at coordinates")
        print("• 'type text' - Type something")
        print("• 'press key' - Press a key")
        print("• 'done' - Task complete")
        
        while True:
            try:
                instruction = input("\n📝 Next step: ").strip()
                
                if instruction.lower() == 'done':
                    execution_time = time.time() - start_time
                    self.stats["completed"] += 1
                    print("✅ Task completed manually!")
                    return {
                        "success": True,
                        "execution_time": execution_time,
                        "action": "manual_completion"
                    }
                
                elif instruction.startswith('click '):
                    parts = instruction.split()
                    if len(parts) >= 3:
                        x, y = int(parts[1]), int(parts[2])
                        pyautogui.click(x, y)
                        print(f"✅ Clicked {x}, {y}")
                    else:
                        print("❌ Use: click X Y")
                
                elif instruction.startswith('type '):
                    text = instruction[5:]
                    pyautogui.write(text)
                    print(f"✅ Typed: {text}")
                
                elif instruction.startswith('press '):
                    key = instruction[6:]
                    pyautogui.press(key)
                    print(f"✅ Pressed: {key}")
                
                else:
                    print("❌ Unknown command. Use: click X Y, type text, press key, or done")
                    
            except KeyboardInterrupt:
                return self._handle_failure("Manual mode cancelled", start_time)
            except Exception as e:
                print(f"❌ Error: {e}")
    
    def _handle_failure(self, error, start_time):
        """Handle task failure"""
        execution_time = time.time() - start_time
        self.stats["failed"] += 1
        
        return {
            "success": False,
            "execution_time": execution_time,
            "error": error
        }

def main():
    """Main function"""
    print("🚀 RELIABLE AGENT-S")
    print("=" * 30)
    print("Simple, direct automation that actually works!")
    print()
    
    try:
        agent = ReliableAgent()
        
        print("💬 Ready! Tell me what to do:")
        print("Examples:")
        print("• 'open browser and search bitcoin'")
        print("• 'take a screenshot'")
        print("• 'open calculator'")
        print("\nType 'quit' to exit, 'stats' for statistics")
        print("-" * 40)
        
        while True:
            try:
                user_input = input("\n🤖 You: ").strip()
                
                if user_input.lower() in ['quit', 'exit']:
                    print("👋 Goodbye!")
                    break
                
                if user_input.lower() == 'stats':
                    print(f"\n📊 STATS:")
                    print(f"   Completed: {agent.stats['completed']}")
                    print(f"   Failed: {agent.stats['failed']}")
                    total = agent.stats['completed'] + agent.stats['failed']
                    if total > 0:
                        success_rate = agent.stats['completed'] / total * 100
                        print(f"   Success Rate: {success_rate:.1f}%")
                    continue
                
                if not user_input:
                    continue
                
                # Execute task
                result = agent.execute_task(user_input)
                
                # Show result
                if result['success']:
                    print(f"✅ SUCCESS! ({result['execution_time']:.1f}s)")
                    if 'query' in result:
                        print(f"   Searched for: {result['query']}")
                    if 'filename' in result:
                        print(f"   Saved: {result['filename']}")
                else:
                    print(f"❌ FAILED: {result.get('error', 'Unknown error')}")
                    print(f"   Time: {result['execution_time']:.1f}s")
                
            except KeyboardInterrupt:
                print("\n👋 Goodbye!")
                break
            except Exception as e:
                print(f"❌ Error: {e}")
    
    except Exception as e:
        print(f"❌ Failed to start: {e}")

if __name__ == "__main__":
    main()
