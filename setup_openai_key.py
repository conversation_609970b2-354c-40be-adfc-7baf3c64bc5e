#!/usr/bin/env python3
"""
Setup script to configure OpenAI API key for the intelligent agent
"""

import os
import sys
import subprocess

def check_openai_key():
    """Check if OpenAI API key is already set"""
    key = os.getenv('OPENAI_API_KEY')
    if key:
        print(f"✅ OpenAI API key found: {key[:10]}...")
        return True
    else:
        print("❌ OpenAI API key not found")
        return False

def set_openai_key_windows(api_key):
    """Set OpenAI API key on Windows"""
    try:
        # Set for current session
        os.environ['OPENAI_API_KEY'] = api_key
        
        # Set permanently using setx
        result = subprocess.run(['setx', 'OPENAI_API_KEY', api_key], 
                              capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ OpenAI API key set permanently!")
            print("🔄 Please restart your command prompt for the change to take effect")
            return True
        else:
            print(f"❌ Failed to set permanently: {result.stderr}")
            print("✅ Set for current session only")
            return True
    except Exception as e:
        print(f"❌ Error setting API key: {e}")
        return False

def test_openai_connection(api_key):
    """Test if the OpenAI API key works"""
    try:
        import requests
        
        headers = {
            'Authorization': f'Bearer {api_key}',
            'Content-Type': 'application/json'
        }
        
        # Simple test request
        response = requests.get(
            'https://api.openai.com/v1/models',
            headers=headers,
            timeout=10
        )
        
        if response.status_code == 200:
            models = response.json()
            gpt4_models = [m for m in models['data'] if 'gpt-4' in m['id']]
            print(f"✅ API key works! Found {len(gpt4_models)} GPT-4 models available")
            return True
        else:
            print(f"❌ API test failed: {response.status_code}")
            print(response.text)
            return False
            
    except Exception as e:
        print(f"❌ Connection test failed: {e}")
        return False

def main():
    """Main setup process"""
    print("🔧 OpenAI API Key Setup for Intelligent Agent")
    print("=" * 50)
    
    # Check if key is already set
    if check_openai_key():
        test_existing = input("\n🧪 Test existing API key? (y/n): ").lower().strip()
        if test_existing == 'y':
            existing_key = os.getenv('OPENAI_API_KEY')
            if test_openai_connection(existing_key):
                print("\n🎉 Your API key is working perfectly!")
                print("You can now run: python smart_screen_controller.py")
                return
            else:
                print("\n❌ Existing API key doesn't work. Let's set a new one.")
    
    print("\n📝 To get your OpenAI API key:")
    print("1. Go to: https://platform.openai.com/api-keys")
    print("2. Sign in to your OpenAI account")
    print("3. Click 'Create new secret key'")
    print("4. Copy the key (starts with 'sk-')")
    
    # Get API key from user
    api_key = input("\n🔑 Enter your OpenAI API key: ").strip()
    
    if not api_key:
        print("❌ No API key entered")
        return
    
    if not api_key.startswith('sk-'):
        print("⚠️ Warning: API key should start with 'sk-'")
        confirm = input("Continue anyway? (y/n): ").lower().strip()
        if confirm != 'y':
            return
    
    # Test the API key
    print("\n🧪 Testing API key...")
    if not test_openai_connection(api_key):
        print("❌ API key test failed. Please check your key and try again.")
        return
    
    # Set the API key
    print("\n💾 Setting API key...")
    if set_openai_key_windows(api_key):
        print("\n🎉 Setup complete!")
        print("\n🚀 You can now run:")
        print("  python smart_screen_controller.py")
        print("  python real_screen_agent.py")
        
        # Test the intelligent agent
        test_agent = input("\n🧪 Test the intelligent agent now? (y/n): ").lower().strip()
        if test_agent == 'y':
            try:
                print("\n🚀 Starting Smart Screen Controller...")
                import smart_screen_controller
                controller = smart_screen_controller.SmartScreenController()
                print("✅ Agent initialized successfully!")
                print("Type 'quit' to exit the test")
                controller.run_interactive_mode()
            except Exception as e:
                print(f"❌ Test failed: {e}")
    else:
        print("❌ Failed to set API key")

if __name__ == "__main__":
    main()
