#!/usr/bin/env python3
"""
Agent-S with Ollama Local Models
Best configuration for computer use with local models
"""

import os
import pyautogui
import io
import time
from PIL import Image
from gui_agents.s2.agents.agent_s import AgentS2
from gui_agents.s2.agents.grounding import OSWorldACI

# Configuration
OLLAMA_BASE_URL = "http://localhost:11434/v1/"
OLLAMA_API_KEY = "not-needed"  # Ollama doesn't require API key

# Model Selection (Best to Worst for Computer Use)
MODELS = {
    "qwen2.5vl:32b": "Best - Vision + Language, 32B parameters",
    "llava:34b": "Excellent - LLaVA 34B, great for computer use", 
    "llava:13b": "Good - LLaVA 13B, balanced performance",
    "minicpm-v:8b": "Fast - MiniCPM-V 8B, efficient vision",
    "llava:7b": "Fastest - LLaVA 7B, most efficient"
}

def select_model():
    """Let user select which model to use"""
    print("\n🤖 AVAILABLE OLLAMA MODELS FOR COMPUTER USE:")
    print("=" * 60)
    
    for i, (model, desc) in enumerate(MODELS.items(), 1):
        print(f"{i}. {model} - {desc}")
    
    while True:
        try:
            choice = input("\nSelect model (1-5) or press Enter for best (1): ").strip()
            if not choice:
                choice = "1"
            
            choice = int(choice)
            if 1 <= choice <= len(MODELS):
                selected_model = list(MODELS.keys())[choice - 1]
                print(f"\n✅ Selected: {selected_model}")
                return selected_model
            else:
                print("❌ Invalid choice. Please select 1-5.")
        except ValueError:
            print("❌ Invalid input. Please enter a number.")

def scale_screen_dimensions(width: int, height: int, max_dim_size: int = 2400):
    """Scale screen dimensions for optimal processing"""
    scale_factor = min(max_dim_size / width, max_dim_size / height, 1)
    safe_width = int(width * scale_factor)
    safe_height = int(height * scale_factor)
    return safe_width, safe_height

def main():
    print("🚀 AGENT-S + OLLAMA LOCAL SETUP")
    print("=" * 50)
    
    # Select model
    selected_model = select_model()
    
    # Get screen dimensions
    screen_width, screen_height = pyautogui.size()
    scaled_width, scaled_height = scale_screen_dimensions(screen_width, screen_height)
    
    print(f"\n🖥️  Screen: {screen_width}x{screen_height}")
    print(f"📏 Scaled: {scaled_width}x{scaled_height}")
    
    # Engine parameters for main model
    engine_params = {
        "engine_type": "openai",  # Use OpenAI engine with custom base_url
        "model": selected_model,
        "base_url": OLLAMA_BASE_URL,
        "api_key": OLLAMA_API_KEY,
    }
    
    # Engine parameters for grounding model (same as main)
    engine_params_for_grounding = {
        "engine_type": "openai",
        "model": selected_model,
        "base_url": OLLAMA_BASE_URL,
        "api_key": OLLAMA_API_KEY,
        "grounding_width": scaled_width,
        "grounding_height": scaled_height,
    }
    
    print(f"\n🔧 Initializing Agent-S with {selected_model}...")
    
    try:
        # Initialize grounding agent
        grounding_agent = OSWorldACI(
            platform="windows",
            engine_params_for_generation=engine_params,
            engine_params_for_grounding=engine_params_for_grounding,
            width=screen_width,
            height=screen_height,
        )
        
        # Initialize Agent-S2
        agent = AgentS2(
            engine_params,
            grounding_agent,
            platform="windows",
            action_space="pyautogui",
            observation_type="mixed",
            search_engine=None,  # Disable web search for local setup
            embedding_engine_type="openai"  # You can change this if needed
        )
        
        print("✅ Agent-S initialized successfully!")
        print("\n🎯 READY FOR COMPUTER USE TASKS!")
        print("=" * 50)
        
        # Interactive loop
        while True:
            print("\n💭 What would you like me to do?")
            print("Examples:")
            print("  - 'open calculator'")
            print("  - 'take a screenshot'") 
            print("  - 'open notepad and write hello'")
            print("  - 'close all windows'")
            print("  - 'quit' to exit")
            
            instruction = input("\n🤔 Your command: ").strip()
            
            if instruction.lower() in ['quit', 'exit', 'q']:
                print("\n👋 Goodbye!")
                break
                
            if not instruction:
                continue
                
            print(f"\n🔄 Processing: {instruction}")
            
            # Reset agent for new task
            agent.reset()
            
            # Take screenshot
            screenshot = pyautogui.screenshot()
            screenshot = screenshot.resize((scaled_width, scaled_height), Image.LANCZOS)
            
            # Convert to bytes
            buffered = io.BytesIO()
            screenshot.save(buffered, format="PNG")
            screenshot_bytes = buffered.getvalue()
            
            # Create observation
            obs = {"screenshot": screenshot_bytes}
            
            # Get prediction
            try:
                print("🤖 Agent is thinking...")
                info, actions = agent.predict(instruction=instruction, observation=obs)
                
                if actions and len(actions) > 0:
                    action = actions[0]
                    print(f"🎯 Executing: {action}")
                    
                    # Execute the action
                    exec(action)
                    print("✅ Action completed!")
                else:
                    print("❌ No action generated")
                    
            except Exception as e:
                print(f"❌ Error: {e}")
                
    except Exception as e:
        print(f"❌ Failed to initialize Agent-S: {e}")
        print("\n🔍 TROUBLESHOOTING:")
        print("1. Make sure Ollama server is running: ollama serve")
        print("2. Check model is available: ollama list")
        print("3. Test Ollama API: curl http://localhost:11434/api/tags")

if __name__ == "__main__":
    main() 