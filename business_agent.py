#!/usr/bin/env python3
"""
Business-Optimized Agent-S
Fast, reliable automation for common business tasks
"""

import os
import pyautogui
import time
import subprocess
from typing import Dict, List

# Speed optimizations
pyautogui.PAUSE = 0.1  # Faster execution
pyautogui.FAILSAFE = True  # Safety

class BusinessAgent:
    def __init__(self):
        self.common_apps = {
            'excel': ['excel', 'Excel', 'EXCEL.EXE'],
            'word': ['winword', 'Word', 'WINWORD.EXE'],
            'outlook': ['outlook', 'Outlook', 'OUTLOOK.EXE'],
            'chrome': ['chrome', 'Chrome', 'chrome.exe'],
            'edge': ['msedge', 'Edge', 'msedge.exe'],
            'notepad': ['notepad', 'Notepad', 'notepad.exe'],
            'calculator': ['calc', 'Calculator', 'calc.exe'],
            'teams': ['Teams', 'Microsoft Teams', 'Teams.exe']
        }
    
    def fast_open_app(self, app_name: str) -> bool:
        """Ultra-fast app opening using multiple methods"""
        app_name = app_name.lower()
        
        if app_name not in self.common_apps:
            print(f"❌ Unknown app: {app_name}")
            return False
            
        commands = self.common_apps[app_name]
        
        # Method 1: Direct command (fastest)
        try:
            subprocess.Popen(commands[0], shell=True)
            time.sleep(2)
            print(f"✅ Opened {app_name} via direct command")
            return True
        except:
            pass
        
        # Method 2: Windows key + search (most reliable)
        try:
            pyautogui.press('win')
            time.sleep(0.5)
            pyautogui.type(commands[1])
            time.sleep(0.5)
            pyautogui.press('enter')
            time.sleep(2)
            print(f"✅ Opened {app_name} via Windows search")
            return True
        except:
            print(f"❌ Failed to open {app_name}")
            return False
    
    def quick_excel_task(self, task_type: str):
        """Optimized Excel automation"""
        if not self.fast_open_app('excel'):
            return False
            
        time.sleep(3)  # Wait for Excel to load
        
        if task_type == "sales_report":
            # Create sales report template
            headers = ["Date", "Product", "Quantity", "Price", "Total", "Customer"]
            
            for i, header in enumerate(headers):
                pyautogui.type(header)
                if i < len(headers) - 1:
                    pyautogui.press('tab')
            
            pyautogui.press('enter')
            print("✅ Created sales report template")
            
        elif task_type == "expense_tracker":
            headers = ["Date", "Category", "Description", "Amount", "Receipt"]
            
            for i, header in enumerate(headers):
                pyautogui.type(header)
                if i < len(headers) - 1:
                    pyautogui.press('tab')
            
            pyautogui.press('enter')
            print("✅ Created expense tracker template")
    
    def quick_email_task(self, task_type: str):
        """Optimized Outlook automation"""
        if not self.fast_open_app('outlook'):
            return False
            
        time.sleep(4)  # Wait for Outlook to load
        
        if task_type == "new_email":
            pyautogui.hotkey('ctrl', 'n')  # New email
            time.sleep(1)
            print("✅ Opened new email window")
            
        elif task_type == "calendar":
            pyautogui.hotkey('ctrl', '2')  # Switch to calendar
            time.sleep(1)
            print("✅ Opened calendar view")
    
    def business_workflow(self, workflow: str):
        """Pre-defined business workflows"""
        workflows = {
            "morning_routine": [
                ("outlook", "calendar"),
                ("chrome", "dashboard"),
                ("excel", "sales_report")
            ],
            "end_of_day": [
                ("excel", "expense_tracker"),
                ("outlook", "new_email"),
                ("chrome", "reports")
            ]
        }
        
        if workflow in workflows:
            print(f"🚀 Starting {workflow} workflow...")
            for app, task in workflows[workflow]:
                print(f"📋 {app} -> {task}")
                if app == "excel":
                    self.quick_excel_task(task)
                elif app == "outlook":
                    self.quick_email_task(task)
                else:
                    self.fast_open_app(app)
                time.sleep(1)
            print(f"✅ Completed {workflow} workflow!")

def main():
    agent = BusinessAgent()
    
    print("💼 BUSINESS AGENT - FAST & RELIABLE")
    print("=" * 50)
    print("Available commands:")
    print("1. 'open [app]' - Fast app opening")
    print("2. 'excel [task]' - Excel automation")
    print("3. 'email [task]' - Outlook automation") 
    print("4. 'workflow [name]' - Business workflows")
    print("5. 'quit' - Exit")
    print()
    
    while True:
        try:
            command = input("🗣️  Command: ").strip().lower()
            
            if command == 'quit':
                break
                
            parts = command.split()
            if len(parts) < 2:
                print("❌ Invalid command format")
                continue
                
            action = parts[0]
            target = parts[1]
            
            if action == 'open':
                agent.fast_open_app(target)
                
            elif action == 'excel':
                agent.quick_excel_task(target)
                
            elif action == 'email':
                agent.quick_email_task(target)
                
            elif action == 'workflow':
                agent.business_workflow(target)
                
            else:
                print("❌ Unknown action")
                
        except KeyboardInterrupt:
            print("\n👋 Goodbye!")
            break
        except Exception as e:
            print(f"❌ Error: {e}")

if __name__ == "__main__":
    main()
