#!/usr/bin/env python3
"""
Enhanced AI Vision Integration for Agent-S
Provides production-ready multimodal AI capabilities with GPT-4V and Claude
"""

import os
import sys
import time
import base64
import io
import json
import requests
from typing import Dict, List, Optional, Tuple, Any
from PIL import Image
import pyautogui
from dataclasses import dataclass
from enum import Enum
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class AIProvider(Enum):
    """Supported AI providers"""
    OPENAI = "openai"
    ANTHROPIC = "anthropic"
    OLLAMA = "ollama"

class VisionCapability(Enum):
    """Vision capabilities"""
    SCREEN_UNDERSTANDING = "screen_understanding"
    UI_ELEMENT_DETECTION = "ui_element_detection"
    TEXT_EXTRACTION = "text_extraction"
    ACTION_PLANNING = "action_planning"
    ERROR_DIAGNOSIS = "error_diagnosis"

@dataclass
class VisionRequest:
    """Vision analysis request"""
    image_data: str  # Base64 encoded image
    prompt: str
    capability: VisionCapability
    provider: AIProvider
    model: str
    max_tokens: int = 1000
    temperature: float = 0.1

@dataclass
class VisionResponse:
    """Vision analysis response"""
    success: bool
    content: str
    provider: AIProvider
    model: str
    processing_time: float
    tokens_used: int = 0
    error_message: str = ""
    confidence_score: float = 0.0

class EnhancedAIVision:
    """Enhanced AI vision system with multiple provider support"""
    
    def __init__(self):
        self.providers = {}
        self.default_models = {
            AIProvider.OPENAI: "gpt-4o",
            AIProvider.ANTHROPIC: "claude-3-5-sonnet-20241022",
            AIProvider.OLLAMA: "llava:latest"
        }
        
        # Initialize providers
        self._initialize_providers()
        
        # Vision prompts for different capabilities
        self.capability_prompts = {
            VisionCapability.SCREEN_UNDERSTANDING: """
Analyze this screenshot and provide a comprehensive understanding of what's displayed.
Describe:
1. The main application or window visible
2. Key UI elements and their locations
3. Current state of the interface
4. Any visible text or content
5. Possible user actions available

Be specific and detailed in your analysis.
""",
            VisionCapability.UI_ELEMENT_DETECTION: """
Analyze this screenshot and identify all interactive UI elements.
For each element, provide:
1. Element type (button, text field, menu, etc.)
2. Approximate location (top-left, center, bottom-right, etc.)
3. Visible text or label
4. Whether it appears clickable/interactive

Format your response as a structured list.
""",
            VisionCapability.TEXT_EXTRACTION: """
Extract all visible text from this screenshot.
Organize the text by:
1. Main headings or titles
2. Menu items
3. Button labels
4. Body text
5. Any other visible text

Preserve the hierarchical structure where possible.
""",
            VisionCapability.ACTION_PLANNING: """
Based on this screenshot and the user's request: "{user_request}"

Provide a step-by-step action plan:
1. Identify the current state
2. Determine what needs to be done
3. List specific actions in order
4. Include approximate click locations or keyboard shortcuts
5. Mention any potential issues or alternatives

Be practical and specific in your recommendations.
""",
            VisionCapability.ERROR_DIAGNOSIS: """
Analyze this screenshot for any visible errors, issues, or problems.
Look for:
1. Error messages or dialogs
2. Unresponsive elements
3. Missing content
4. Visual glitches
5. Accessibility issues

Provide diagnosis and suggested solutions.
"""
        }
        
        # Performance tracking
        self.stats = {
            'total_requests': 0,
            'successful_requests': 0,
            'failed_requests': 0,
            'average_response_time': 0.0,
            'provider_usage': {provider.value: 0 for provider in AIProvider}
        }
    
    def _initialize_providers(self):
        """Initialize AI providers with their configurations"""
        # OpenAI GPT-4V
        openai_api_key = os.getenv('OPENAI_API_KEY')
        if openai_api_key:
            self.providers[AIProvider.OPENAI] = {
                'api_key': openai_api_key,
                'base_url': 'https://api.openai.com/v1',
                'available': True
            }
            logger.info("✅ OpenAI GPT-4V initialized")
        else:
            logger.warning("⚠️ OpenAI API key not found")
        
        # Anthropic Claude
        anthropic_api_key = os.getenv('ANTHROPIC_API_KEY')
        if anthropic_api_key:
            self.providers[AIProvider.ANTHROPIC] = {
                'api_key': anthropic_api_key,
                'base_url': 'https://api.anthropic.com/v1',
                'available': True
            }
            logger.info("✅ Anthropic Claude initialized")
        else:
            logger.warning("⚠️ Anthropic API key not found")
        
        # Ollama (local)
        try:
            response = requests.get('http://localhost:11434/api/tags', timeout=5)
            if response.status_code == 200:
                self.providers[AIProvider.OLLAMA] = {
                    'base_url': 'http://localhost:11434',
                    'available': True
                }
                logger.info("✅ Ollama initialized")
            else:
                logger.warning("⚠️ Ollama not available")
        except:
            logger.warning("⚠️ Ollama not available")
    
    def take_screenshot(self, resize_width: int = 1280) -> str:
        """Take and encode screenshot for AI analysis"""
        try:
            # Take screenshot
            screenshot = pyautogui.screenshot()
            
            # Resize for faster processing while maintaining aspect ratio
            original_width, original_height = screenshot.size
            if original_width > resize_width:
                resize_height = int(original_height * resize_width / original_width)
                screenshot = screenshot.resize((resize_width, resize_height), Image.LANCZOS)
            
            # Convert to base64
            buffered = io.BytesIO()
            screenshot.save(buffered, format="JPEG", quality=90)
            img_bytes = buffered.getvalue()
            img_base64 = base64.b64encode(img_bytes).decode('utf-8')
            
            return img_base64
        except Exception as e:
            logger.error(f"Screenshot failed: {e}")
            raise
    
    def analyze_screen(self, 
                      capability: VisionCapability,
                      user_request: str = "",
                      provider: AIProvider = None,
                      model: str = None,
                      custom_prompt: str = None) -> VisionResponse:
        """
        Analyze current screen with specified capability
        
        Args:
            capability: Type of analysis to perform
            user_request: User's specific request (for action planning)
            provider: AI provider to use (auto-select if None)
            model: Specific model to use
            custom_prompt: Custom prompt instead of default
            
        Returns:
            VisionResponse with analysis results
        """
        start_time = time.time()
        self.stats['total_requests'] += 1
        
        try:
            # Take screenshot
            image_data = self.take_screenshot()
            
            # Select provider if not specified
            if provider is None:
                provider = self._select_best_provider()
            
            if provider not in self.providers or not self.providers[provider]['available']:
                raise ValueError(f"Provider {provider.value} not available")
            
            # Select model
            if model is None:
                model = self.default_models[provider]
            
            # Prepare prompt
            if custom_prompt:
                prompt = custom_prompt
            else:
                prompt = self.capability_prompts[capability]
                if capability == VisionCapability.ACTION_PLANNING and user_request:
                    prompt = prompt.format(user_request=user_request)
            
            # Create request
            request = VisionRequest(
                image_data=image_data,
                prompt=prompt,
                capability=capability,
                provider=provider,
                model=model
            )
            
            # Send request to provider
            response = self._send_vision_request(request)
            
            # Update stats
            processing_time = time.time() - start_time
            if response.success:
                self.stats['successful_requests'] += 1
                self._update_average_response_time(processing_time)
            else:
                self.stats['failed_requests'] += 1
            
            self.stats['provider_usage'][provider.value] += 1
            
            return response
            
        except Exception as e:
            processing_time = time.time() - start_time
            self.stats['failed_requests'] += 1
            
            return VisionResponse(
                success=False,
                content="",
                provider=provider or AIProvider.OPENAI,
                model=model or "unknown",
                processing_time=processing_time,
                error_message=str(e)
            )
    
    def _select_best_provider(self) -> AIProvider:
        """Select the best available provider"""
        # Priority order: OpenAI (most reliable) -> Anthropic -> Ollama (local)
        priority_order = [AIProvider.OPENAI, AIProvider.ANTHROPIC, AIProvider.OLLAMA]
        
        for provider in priority_order:
            if provider in self.providers and self.providers[provider]['available']:
                return provider
        
        raise ValueError("No AI providers available")
    
    def _send_vision_request(self, request: VisionRequest) -> VisionResponse:
        """Send vision request to specified provider"""
        if request.provider == AIProvider.OPENAI:
            return self._send_openai_request(request)
        elif request.provider == AIProvider.ANTHROPIC:
            return self._send_anthropic_request(request)
        elif request.provider == AIProvider.OLLAMA:
            return self._send_ollama_request(request)
        else:
            raise ValueError(f"Unsupported provider: {request.provider}")
    
    def _send_openai_request(self, request: VisionRequest) -> VisionResponse:
        """Send request to OpenAI GPT-4V"""
        start_time = time.time()
        
        try:
            headers = {
                'Authorization': f"Bearer {self.providers[AIProvider.OPENAI]['api_key']}",
                'Content-Type': 'application/json'
            }
            
            payload = {
                'model': request.model,
                'messages': [
                    {
                        'role': 'user',
                        'content': [
                            {
                                'type': 'text',
                                'text': request.prompt
                            },
                            {
                                'type': 'image_url',
                                'image_url': {
                                    'url': f"data:image/jpeg;base64,{request.image_data}",
                                    'detail': 'high'
                                }
                            }
                        ]
                    }
                ],
                'max_tokens': request.max_tokens,
                'temperature': request.temperature
            }
            
            response = requests.post(
                f"{self.providers[AIProvider.OPENAI]['base_url']}/chat/completions",
                headers=headers,
                json=payload,
                timeout=60
            )
            
            if response.status_code == 200:
                result = response.json()
                content = result['choices'][0]['message']['content']
                tokens_used = result.get('usage', {}).get('total_tokens', 0)
                
                return VisionResponse(
                    success=True,
                    content=content,
                    provider=request.provider,
                    model=request.model,
                    processing_time=time.time() - start_time,
                    tokens_used=tokens_used,
                    confidence_score=0.9  # OpenAI generally high quality
                )
            else:
                error_msg = f"OpenAI API error: {response.status_code} - {response.text}"
                return VisionResponse(
                    success=False,
                    content="",
                    provider=request.provider,
                    model=request.model,
                    processing_time=time.time() - start_time,
                    error_message=error_msg
                )
                
        except Exception as e:
            return VisionResponse(
                success=False,
                content="",
                provider=request.provider,
                model=request.model,
                processing_time=time.time() - start_time,
                error_message=str(e)
            )

    def _send_anthropic_request(self, request: VisionRequest) -> VisionResponse:
        """Send request to Anthropic Claude"""
        start_time = time.time()

        try:
            headers = {
                'x-api-key': self.providers[AIProvider.ANTHROPIC]['api_key'],
                'Content-Type': 'application/json',
                'anthropic-version': '2023-06-01'
            }

            payload = {
                'model': request.model,
                'max_tokens': request.max_tokens,
                'temperature': request.temperature,
                'messages': [
                    {
                        'role': 'user',
                        'content': [
                            {
                                'type': 'text',
                                'text': request.prompt
                            },
                            {
                                'type': 'image',
                                'source': {
                                    'type': 'base64',
                                    'media_type': 'image/jpeg',
                                    'data': request.image_data
                                }
                            }
                        ]
                    }
                ]
            }

            response = requests.post(
                f"{self.providers[AIProvider.ANTHROPIC]['base_url']}/messages",
                headers=headers,
                json=payload,
                timeout=60
            )

            if response.status_code == 200:
                result = response.json()
                content = result['content'][0]['text']
                tokens_used = result.get('usage', {}).get('input_tokens', 0) + result.get('usage', {}).get('output_tokens', 0)

                return VisionResponse(
                    success=True,
                    content=content,
                    provider=request.provider,
                    model=request.model,
                    processing_time=time.time() - start_time,
                    tokens_used=tokens_used,
                    confidence_score=0.95  # Claude generally very high quality
                )
            else:
                error_msg = f"Anthropic API error: {response.status_code} - {response.text}"
                return VisionResponse(
                    success=False,
                    content="",
                    provider=request.provider,
                    model=request.model,
                    processing_time=time.time() - start_time,
                    error_message=error_msg
                )

        except Exception as e:
            return VisionResponse(
                success=False,
                content="",
                provider=request.provider,
                model=request.model,
                processing_time=time.time() - start_time,
                error_message=str(e)
            )

    def _send_ollama_request(self, request: VisionRequest) -> VisionResponse:
        """Send request to Ollama (local)"""
        start_time = time.time()

        try:
            payload = {
                'model': request.model,
                'messages': [
                    {
                        'role': 'user',
                        'content': request.prompt,
                        'images': [request.image_data]
                    }
                ],
                'stream': False,
                'options': {
                    'temperature': request.temperature,
                    'num_predict': request.max_tokens
                }
            }

            response = requests.post(
                f"{self.providers[AIProvider.OLLAMA]['base_url']}/api/chat",
                json=payload,
                timeout=120  # Ollama can be slower
            )

            if response.status_code == 200:
                result = response.json()
                content = result['message']['content']

                return VisionResponse(
                    success=True,
                    content=content,
                    provider=request.provider,
                    model=request.model,
                    processing_time=time.time() - start_time,
                    confidence_score=0.7  # Local models generally lower quality
                )
            else:
                error_msg = f"Ollama API error: {response.status_code} - {response.text}"
                return VisionResponse(
                    success=False,
                    content="",
                    provider=request.provider,
                    model=request.model,
                    processing_time=time.time() - start_time,
                    error_message=error_msg
                )

        except Exception as e:
            return VisionResponse(
                success=False,
                content="",
                provider=request.provider,
                model=request.model,
                processing_time=time.time() - start_time,
                error_message=str(e)
            )

    def _update_average_response_time(self, response_time: float):
        """Update average response time statistics"""
        successful = self.stats['successful_requests']
        current_avg = self.stats['average_response_time']

        new_avg = ((current_avg * (successful - 1)) + response_time) / successful
        self.stats['average_response_time'] = new_avg

    def get_stats(self) -> Dict:
        """Get vision system statistics"""
        total = self.stats['total_requests']
        success_rate = (self.stats['successful_requests'] / total * 100) if total > 0 else 0

        return {
            'total_requests': total,
            'successful_requests': self.stats['successful_requests'],
            'failed_requests': self.stats['failed_requests'],
            'success_rate': f"{success_rate:.1f}%",
            'average_response_time': f"{self.stats['average_response_time']:.2f}s",
            'provider_usage': self.stats['provider_usage'],
            'available_providers': [p.value for p in self.providers.keys() if self.providers[p]['available']]
        }

    def quick_screen_analysis(self, user_request: str) -> str:
        """Quick screen analysis for user requests"""
        try:
            # Try action planning first
            response = self.analyze_screen(
                capability=VisionCapability.ACTION_PLANNING,
                user_request=user_request
            )

            if response.success:
                return response.content

            # Fallback to general screen understanding
            response = self.analyze_screen(
                capability=VisionCapability.SCREEN_UNDERSTANDING
            )

            if response.success:
                return f"Screen Analysis: {response.content}\n\nFor your request '{user_request}', please provide more specific instructions."

            return "❌ Unable to analyze screen. Please check AI provider configuration."

        except Exception as e:
            return f"❌ Screen analysis failed: {e}"

    def diagnose_screen_issues(self) -> str:
        """Diagnose any visible issues on screen"""
        try:
            response = self.analyze_screen(capability=VisionCapability.ERROR_DIAGNOSIS)

            if response.success:
                return response.content
            else:
                return f"❌ Error diagnosis failed: {response.error_message}"

        except Exception as e:
            return f"❌ Error diagnosis failed: {e}"


# Example usage and testing
if __name__ == "__main__":
    vision = EnhancedAIVision()

    print("👁️ Enhanced AI Vision - Agent-S")
    print("=" * 40)

    # Show available providers
    stats = vision.get_stats()
    print(f"🤖 Available Providers: {', '.join(stats['available_providers'])}")

    if not stats['available_providers']:
        print("❌ No AI providers available. Please configure API keys:")
        print("  - Set OPENAI_API_KEY for GPT-4V")
        print("  - Set ANTHROPIC_API_KEY for Claude")
        print("  - Start Ollama for local vision models")
        sys.exit(1)

    # Interactive mode
    print("\n💡 Enter your request (or 'quit' to exit):")
    print("Examples:")
    print("  - 'What do you see on the screen?'")
    print("  - 'Help me open calculator'")
    print("  - 'Find all buttons on this page'")

    while True:
        try:
            user_input = input("\n🎯 Request: ").strip()
            if user_input.lower() in ['quit', 'exit', 'q']:
                break

            if user_input:
                print("🔍 Analyzing screen...")
                result = vision.quick_screen_analysis(user_input)
                print(f"\n🤖 AI Analysis:")
                print("-" * 50)
                print(result)
                print("-" * 50)
        except KeyboardInterrupt:
            break

    # Final statistics
    print(f"\n📊 Final Statistics:")
    final_stats = vision.get_stats()
    for key, value in final_stats.items():
        print(f"  {key}: {value}")

    print("\n👋 Goodbye!")
