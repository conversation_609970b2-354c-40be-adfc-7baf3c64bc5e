#!/usr/bin/env python3
"""
Simple API Key Setup for Smart Agent
"""

import os
from pathlib import Path

def main():
    print("🔑 Smart Agent API Key Setup")
    print("="*30)
    print("For AI vision capabilities, you can add your OpenAI API key.")
    print("This is optional - the system works without it too.")
    print()
    
    # Check existing
    current_key = os.getenv('OPENAI_API_KEY', '')
    if current_key:
        print(f"Current key: {current_key[:8]}...{current_key[-4:]}")
        keep = input("Keep current key? (y/n): ").lower().strip()
        if keep == 'y':
            print("✅ Keeping existing key")
            return
    
    # Get new key
    print("Get your OpenAI API key from: https://platform.openai.com/api-keys")
    new_key = input("Enter OpenAI API key (or press Enter to skip): ").strip()
    
    if new_key:
        # Save to .env file
        env_content = f"OPENAI_API_KEY={new_key}\n"
        
        with open(".env", "w") as f:
            f.write(env_content)
        
        print("✅ API key saved to .env file")
        print("🚀 Restart Smart Agent to use AI vision")
    else:
        print("⚠️ No API key set - using basic vision")
    
    print("\nDone! Run: python smart_agent.py")

if __name__ == "__main__":
    main()
