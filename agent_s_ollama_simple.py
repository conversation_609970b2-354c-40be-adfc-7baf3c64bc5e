#!/usr/bin/env python3
"""
Simplified Agent-S with Ollama - No External Dependencies
"""

import os
import pyautogui
import io
import time
from PIL import Image
import requests
import base64
import json

# Configuration
OLLAMA_BASE_URL = "http://localhost:11434"
OLLAMA_API_URL = f"{OLLAMA_BASE_URL}/api/generate"
OLLAMA_VISION_URL = f"{OLLAMA_BASE_URL}/api/chat"

def take_screenshot():
    """Take and encode screenshot"""
    screenshot = pyautogui.screenshot()
    screenshot = screenshot.resize((1024, 768), Image.LANCZOS)
    
    buffered = io.BytesIO()
    screenshot.save(buffered, format="JPEG", quality=85)
    img_bytes = buffered.getvalue()
    img_base64 = base64.b64encode(img_bytes).decode('utf-8')
    
    return img_base64

def call_ollama_vision(model, instruction, image_base64):
    """Call Ollama vision model"""
    prompt = f"""
You are a computer automation assistant. Look at the screenshot and generate Python code to accomplish this task: {instruction}

Important rules:
1. Use pyautogui for mouse/keyboard actions
2. Use time.sleep() for delays
3. Be precise with coordinates
4. Generate working Python code only
5. If task is complete, return 'DONE'

Generate the Python code:
"""
    
    payload = {
        "model": model,
        "messages": [
            {
                "role": "user", 
                "content": prompt,
                "images": [image_base64]
            }
        ],
        "stream": False
    }
    
    try:
        response = requests.post(OLLAMA_VISION_URL, json=payload, timeout=30)
        if response.status_code == 200:
            result = response.json()
            return result['message']['content']
        else:
            return f"Error: {response.status_code}"
    except Exception as e:
        return f"Error: {e}"

def extract_python_code(text):
    """Extract Python code from response"""
    if "DONE" in text.upper():
        return "DONE"
    
    # Look for code blocks
    if "```python" in text:
        start = text.find("```python") + 9
        end = text.find("```", start)
        if end != -1:
            return text[start:end].strip()
    
    if "```" in text:
        start = text.find("```") + 3
        end = text.find("```", start)
        if end != -1:
            return text[start:end].strip()
    
    # Look for pyautogui commands
    lines = text.split('\n')
    python_lines = []
    for line in lines:
        if 'pyautogui' in line or 'time.sleep' in line or 'import' in line:
            python_lines.append(line.strip())
    
    if python_lines:
        return '\n'.join(python_lines)
    
    return text.strip()

def main():
    print("🚀 SIMPLE AGENT-S + OLLAMA")
    print("=" * 40)
    
    # Test Ollama connection
    try:
        response = requests.get(f"{OLLAMA_BASE_URL}/api/tags", timeout=5)
        if response.status_code != 200:
            print("❌ Ollama not running. Start with: ollama serve")
            return
    except:
        print("❌ Cannot connect to Ollama. Start with: ollama serve")
        return
    
    # Available models
    models = ["llava:13b", "llava:7b", "minicpm-v:8b", "qwen2.5vl:32b"]
    
    print("\n📋 Available models:")
    for i, model in enumerate(models, 1):
        print(f"{i}. {model}")
    
    choice = input("\nSelect model (1-4) or press Enter for default: ").strip()
    if not choice:
        choice = "1"
    
    try:
        selected_model = models[int(choice) - 1]
    except:
        selected_model = models[0]
    
    print(f"\n🤖 Using model: {selected_model}")
    
    # Interactive loop
    while True:
        print("\n" + "="*50)
        print("💭 What would you like me to do?")
        print("Examples: 'open calculator', 'take screenshot', 'quit'")
        
        instruction = input("\n🎯 Command: ").strip()
        
        if instruction.lower() in ['quit', 'exit', 'q']:
            print("👋 Goodbye!")
            break
        
        if not instruction:
            continue
        
        print(f"\n🔄 Processing: {instruction}")
        
        # Take screenshot
        print("📸 Taking screenshot...")
        try:
            image_base64 = take_screenshot()
        except Exception as e:
            print(f"❌ Screenshot failed: {e}")
            continue
        
        # Get AI response
        print("🤖 Asking AI...")
        try:
            response = call_ollama_vision(selected_model, instruction, image_base64)
            print(f"🧠 AI Response: {response[:200]}...")
        except Exception as e:
            print(f"❌ AI call failed: {e}")
            continue
        
        # Extract and execute code
        code = extract_python_code(response)
        
        if code == "DONE":
            print("✅ Task marked as complete!")
            continue
        
        if not code:
            print("❌ No executable code generated")
            continue
        
        print(f"\n🎯 Generated code:")
        print("-" * 30)
        print(code)
        print("-" * 30)
        
        # Ask for permission
        execute = input("\n⚡ Execute this code? (y/n): ").strip().lower()
        
        if execute != 'y':
            print("⏭️ Skipped execution")
            continue
        
        # Execute code
        print("⚡ Executing...")
        try:
            # Add safety imports
            exec_globals = {
                'pyautogui': pyautogui,
                'time': time,
                'print': print
            }
            
            exec(code, exec_globals)
            print("✅ Code executed successfully!")
            
        except Exception as e:
            print(f"❌ Execution failed: {e}")
        
        time.sleep(1)

if __name__ == "__main__":
    main() 