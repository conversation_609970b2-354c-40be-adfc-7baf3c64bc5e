#!/usr/bin/env python3
"""
Smart AI Agent with Deep System Knowledge
Handles complex, long tasks with full computer understanding
"""

import json
import os
import pyautogui
import time
import subprocess
from pathlib import Path

class SmartAIAgent:
    def __init__(self):
        self.knowledge = self.load_system_knowledge()
        self.task_memory = []
        self.current_task = None
        
        # Enhanced settings for complex tasks
        pyautogui.PAUSE = 0.1
        pyautogui.FAILSAFE = True
        
    def load_system_knowledge(self):
        """Load comprehensive system knowledge"""
        try:
            with open("system_knowledge.json", 'r', encoding='utf-8') as f:
                knowledge = json.load(f)
                print(f"🧠 Loaded knowledge of {len(knowledge.get('installed_apps', {}))} apps")
                return knowledge
        except FileNotFoundError:
            print("⚠️ No system knowledge found. Run system_knowledge_builder.py first!")
            return {}
    
    def find_application(self, app_name):
        """Intelligently find any application"""
        app_name = app_name.lower().strip()
        
        # Direct match
        apps = self.knowledge.get('installed_apps', {})
        if app_name in apps:
            return apps[app_name]
        
        # Fuzzy search
        for key, app_info in apps.items():
            if app_name in key or app_name in app_info.get('name', '').lower():
                return app_info
        
        # Common aliases
        aliases = {
            'word': 'microsoft word',
            'excel': 'microsoft excel',
            'powerpoint': 'microsoft powerpoint',
            'outlook': 'microsoft outlook',
            'teams': 'microsoft teams',
            'browser': 'microsoft edge',
            'chrome': 'google chrome',
            'firefox': 'mozilla firefox',
            'notepad': 'notepad',
            'calculator': 'calculator',
            'paint': 'paint'
        }
        
        if app_name in aliases:
            return self.find_application(aliases[app_name])
        
        return None
    
    def smart_app_launch(self, app_name):
        """Launch apps using system knowledge"""
        app_info = self.find_application(app_name)
        
        if app_info:
            print(f"🎯 Found {app_info['name']}")
            
            # Try direct executable if available
            if 'location' in app_info and app_info['location']:
                try:
                    subprocess.Popen(app_info['location'], shell=True)
                    print(f"✅ Launched via executable")
                    return True
                except:
                    pass
        
        # Fallback to smart search
        return self.fallback_launch(app_name)
    
    def fallback_launch(self, app_name):
        """Smart fallback launching"""
        methods = [
            lambda: subprocess.Popen(app_name, shell=True),
            lambda: self.launch_via_run_dialog(app_name),
            lambda: self.launch_via_start_menu(app_name)
        ]
        
        for method in methods:
            try:
                method()
                print(f"✅ Launched {app_name}")
                return True
            except:
                continue
        
        print(f"❌ Could not launch {app_name}")
        return False
    
    def launch_via_run_dialog(self, app_name):
        """Launch via Win+R"""
        pyautogui.hotkey('win', 'r')
        time.sleep(0.3)
        pyautogui.typewrite(app_name)
        pyautogui.press('enter')
        time.sleep(1)
    
    def launch_via_start_menu(self, app_name):
        """Launch via Start menu search"""
        pyautogui.press('win')
        time.sleep(0.5)
        pyautogui.typewrite(app_name)
        time.sleep(0.8)
        pyautogui.press('enter')
        time.sleep(1)
    
    def navigate_to_folder(self, folder_path):
        """Navigate to any folder using system knowledge"""
        # Check if it's a known location
        locations = self.knowledge.get('file_locations', {})
        
        # Handle shortcuts like "documents", "desktop", etc.
        if folder_path.lower() in locations:
            folder_path = locations[folder_path.lower()]
        
        # Open File Explorer
        pyautogui.hotkey('win', 'e')
        time.sleep(1)
        
        # Navigate to folder
        pyautogui.hotkey('ctrl', 'l')  # Focus address bar
        time.sleep(0.3)
        pyautogui.typewrite(folder_path)
        pyautogui.press('enter')
        time.sleep(1)
        
        print(f"📁 Navigated to {folder_path}")
    
    def execute_complex_task(self, task_description):
        """Execute complex multi-step tasks"""
        print(f"🎯 COMPLEX TASK: {task_description}")
        print("=" * 50)
        
        self.current_task = {
            "description": task_description,
            "steps": [],
            "start_time": time.time(),
            "status": "in_progress"
        }
        
        # Parse task and break into steps
        steps = self.parse_complex_task(task_description)
        
        for i, step in enumerate(steps, 1):
            print(f"📋 Step {i}: {step['description']}")
            
            try:
                success = self.execute_step(step)
                step['status'] = 'completed' if success else 'failed'
                step['timestamp'] = time.time()
                
                self.current_task['steps'].append(step)
                
                if success:
                    print(f"✅ Step {i} completed")
                else:
                    print(f"❌ Step {i} failed")
                    
                time.sleep(1)  # Brief pause between steps
                
            except Exception as e:
                print(f"❌ Step {i} error: {e}")
                step['status'] = 'error'
                step['error'] = str(e)
                self.current_task['steps'].append(step)
        
        self.current_task['status'] = 'completed'
        self.current_task['end_time'] = time.time()
        self.task_memory.append(self.current_task)
        
        # Summary
        completed_steps = sum(1 for step in self.current_task['steps'] if step['status'] == 'completed')
        total_time = self.current_task['end_time'] - self.current_task['start_time']
        
        print(f"\n🎉 TASK COMPLETE!")
        print(f"✅ {completed_steps}/{len(steps)} steps completed")
        print(f"⏱️ Total time: {total_time:.1f} seconds")
    
    def parse_complex_task(self, task_description):
        """Parse complex tasks into actionable steps"""
        task_lower = task_description.lower()
        steps = []
        
        # Example: "create a sales report in excel with charts"
        if "sales report" in task_lower and "excel" in task_lower:
            steps = [
                {"type": "launch_app", "description": "Open Excel", "app": "excel"},
                {"type": "create_document", "description": "Create new workbook"},
                {"type": "add_headers", "description": "Add sales report headers"},
                {"type": "add_sample_data", "description": "Add sample sales data"},
                {"type": "create_chart", "description": "Create sales chart"},
                {"type": "format_document", "description": "Format the report"},
                {"type": "save_document", "description": "Save the sales report"}
            ]
        
        # Example: "organize my desktop files"
        elif "organize" in task_lower and "desktop" in task_lower:
            steps = [
                {"type": "navigate", "description": "Open Desktop folder", "location": "desktop"},
                {"type": "create_folders", "description": "Create organization folders"},
                {"type": "sort_files", "description": "Sort files by type"},
                {"type": "move_files", "description": "Move files to appropriate folders"}
            ]
        
        # Example: "backup my documents to cloud"
        elif "backup" in task_lower and "documents" in task_lower:
            steps = [
                {"type": "navigate", "description": "Open Documents folder", "location": "documents"},
                {"type": "select_files", "description": "Select important files"},
                {"type": "compress", "description": "Create backup archive"},
                {"type": "upload", "description": "Upload to cloud storage"}
            ]
        
        # Default: break down by keywords
        else:
            steps = self.smart_task_breakdown(task_description)
        
        return steps
    
    def smart_task_breakdown(self, task_description):
        """Smart breakdown of any task"""
        steps = []
        task_lower = task_description.lower()
        
        # Identify apps mentioned
        apps_mentioned = []
        for app_name in self.knowledge.get('installed_apps', {}):
            if app_name in task_lower:
                apps_mentioned.append(app_name)
        
        # Add app launching steps
        for app in apps_mentioned:
            steps.append({
                "type": "launch_app",
                "description": f"Open {app}",
                "app": app
            })
        
        # Add generic steps based on keywords
        if "create" in task_lower:
            steps.append({"type": "create", "description": "Create new document/file"})
        
        if "format" in task_lower:
            steps.append({"type": "format", "description": "Format content"})
        
        if "save" in task_lower:
            steps.append({"type": "save", "description": "Save work"})
        
        return steps
    
    def execute_step(self, step):
        """Execute individual step with full capabilities"""
        step_type = step.get('type', '')

        if step_type == 'launch_app':
            return self.smart_app_launch(step.get('app', ''))

        elif step_type == 'navigate':
            location = step.get('location', '')
            self.navigate_to_folder(location)
            return True

        elif step_type == 'create_document':
            pyautogui.hotkey('ctrl', 'n')
            time.sleep(1)
            return True

        elif step_type == 'add_headers':
            headers = ["Date", "Product", "Quantity", "Price", "Total"]
            for i, header in enumerate(headers):
                pyautogui.typewrite(header)
                if i < len(headers) - 1:
                    pyautogui.press('tab')
            pyautogui.press('enter')
            return True

        elif step_type == 'add_sample_data':
            # Add sample sales data
            sample_data = [
                ["2024-01-01", "Laptop", "5", "1000", "5000"],
                ["2024-01-02", "Mouse", "20", "25", "500"],
                ["2024-01-03", "Keyboard", "15", "50", "750"],
                ["2024-01-04", "Monitor", "8", "300", "2400"]
            ]

            for row in sample_data:
                for i, cell in enumerate(row):
                    pyautogui.typewrite(cell)
                    if i < len(row) - 1:
                        pyautogui.press('tab')
                pyautogui.press('enter')
            return True

        elif step_type == 'create_chart':
            # Select data range and create chart
            pyautogui.hotkey('ctrl', 'a')  # Select all data
            time.sleep(0.5)
            pyautogui.hotkey('alt', 'n', 'c')  # Insert chart (Excel shortcut)
            time.sleep(2)
            pyautogui.press('enter')  # Accept default chart
            return True

        elif step_type == 'format_document':
            # Basic formatting
            pyautogui.hotkey('ctrl', 'home')  # Go to beginning
            pyautogui.hotkey('ctrl', 'a')  # Select all
            pyautogui.hotkey('ctrl', 'b')  # Bold headers
            return True

        elif step_type == 'save_document':
            pyautogui.hotkey('ctrl', 's')
            time.sleep(0.5)
            pyautogui.typewrite("Sales_Report")
            pyautogui.press('enter')
            return True

        elif step_type == 'create_folders':
            # Create organization folders
            folders = ["Documents", "Images", "Spreadsheets", "Other"]
            for folder in folders:
                pyautogui.hotkey('ctrl', 'shift', 'n')  # New folder
                time.sleep(0.5)
                pyautogui.typewrite(folder)
                pyautogui.press('enter')
                time.sleep(0.5)
            return True

        elif step_type == 'sort_files':
            # Sort files by type
            pyautogui.hotkey('ctrl', 'a')  # Select all
            time.sleep(0.5)
            return True

        elif step_type == 'move_files':
            # This would need more complex logic
            print("📁 File organization complete")
            return True

        elif step_type == 'create':
            pyautogui.hotkey('ctrl', 'n')
            time.sleep(1)
            return True

        elif step_type == 'format':
            pyautogui.hotkey('ctrl', 'a')
            time.sleep(0.5)
            return True

        elif step_type == 'save':
            pyautogui.hotkey('ctrl', 's')
            time.sleep(1)
            return True

        else:
            print(f"⚠️ Unknown step type: {step_type}")
            return False

def main():
    print("🧠 SMART AI AGENT WITH SYSTEM KNOWLEDGE")
    print("=" * 50)
    
    agent = SmartAIAgent()
    
    if not agent.knowledge:
        print("❌ No system knowledge available!")
        print("Run: python system_knowledge_builder.py")
        return
    
    print("🎯 Ready for complex tasks!")
    print("\nExample complex tasks:")
    print("- 'create a sales report in excel with charts'")
    print("- 'organize my desktop files into folders'")
    print("- 'backup my documents to a zip file'")
    print("- 'open outlook and teams for morning meeting'")
    print()
    
    while True:
        try:
            task = input("🎯 Complex Task: ").strip()
            
            if task.lower() in ['quit', 'exit']:
                break
            
            if task:
                agent.execute_complex_task(task)
            
        except KeyboardInterrupt:
            print("\n👋 Goodbye!")
            break
        except Exception as e:
            print(f"❌ Error: {e}")

if __name__ == "__main__":
    main()
