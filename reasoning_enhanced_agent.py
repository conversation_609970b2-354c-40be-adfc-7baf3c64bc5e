#!/usr/bin/env python3
"""
Reasoning-Enhanced Agent with Advanced AI Capabilities
Integrates sophisticated reasoning for intelligent task planning and execution
"""

import os
import sys
import time
import json
import uuid
from datetime import datetime
from typing import Dict, List, Any, Optional
import logging

# Import our components
from advanced_ai_reasoning import AdvancedAIReasoning, ReasoningType, ReasoningContext, ConfidenceLevel
from memory_enhanced_agent import MemoryEnhancedAgent
from persistent_task_memory import TaskState, MemoryType

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class ReasoningEnhancedAgent(MemoryEnhancedAgent):
    """
    Reasoning-Enhanced Agent with Advanced AI Capabilities
    
    Features:
    - Strategic planning and tactical execution
    - Multi-perspective reasoning and decision making
    - Adaptive learning from experience
    - Causal analysis and predictive modeling
    - Creative problem solving
    - Real-time reasoning adaptation
    """
    
    def __init__(self, memory_dir: str = "agent_memory"):
        # Initialize base agent with memory
        super().__init__(memory_dir)
        
        # Initialize advanced reasoning system
        self.reasoning_engine = AdvancedAIReasoning()
        
        # Reasoning configuration
        self.reasoning_enabled = True
        self.multi_perspective_reasoning = True
        self.adaptive_reasoning = True
        self.reasoning_depth = 3
        
        # Reasoning history and learning
        self.reasoning_sessions = []
        self.reasoning_performance = {}
        
        print("🧠 Reasoning-Enhanced Agent initialized!")
        print("✨ Advanced AI reasoning capabilities enabled")
        
        # Show reasoning stats
        stats = self.reasoning_engine.get_reasoning_stats()
        print(f"📊 Reasoning System Ready:")
        print(f"  • Success Rate: {stats.get('success_rate', 0):.1%}")
        print(f"  • Average Confidence: {stats.get('average_confidence', 0):.1%}")
    
    def execute_intelligent_task(self, user_request: str, reasoning_types: Optional[List[ReasoningType]] = None) -> str:
        """Execute a task with advanced AI reasoning"""
        task_id = str(uuid.uuid4())
        
        print(f"\n🧠 INTELLIGENT TASK EXECUTION")
        print(f"📝 Request: {user_request}")
        print(f"🆔 Task ID: {task_id}")
        print("=" * 60)
        
        try:
            # Step 1: Strategic reasoning and planning
            print("🧠 Phase 1: Strategic Reasoning and Planning...")
            strategic_result = self._perform_strategic_reasoning(user_request, task_id)
            
            if not strategic_result or strategic_result.confidence == ConfidenceLevel.VERY_LOW:
                return "❌ Strategic planning failed - task too complex or unclear"
            
            # Step 2: Multi-perspective analysis (if enabled)
            if self.multi_perspective_reasoning and reasoning_types:
                print("🧠 Phase 2: Multi-Perspective Analysis...")
                multi_results = self._perform_multi_perspective_reasoning(user_request, reasoning_types)
                synthesized_result = self.reasoning_engine.synthesize_reasoning_results(multi_results)
                
                print(f"🎯 Synthesized Confidence: {synthesized_result.confidence.value}")
                print(f"📊 Success Probability: {synthesized_result.success_probability:.1%}")
            else:
                synthesized_result = strategic_result
            
            # Step 3: Create execution plan with reasoning
            print("🧠 Phase 3: Intelligent Execution Planning...")
            execution_plan = self._create_intelligent_execution_plan(synthesized_result, user_request)
            
            # Step 4: Execute with adaptive reasoning
            print("🧠 Phase 4: Adaptive Execution...")
            return self._execute_with_reasoning(task_id, user_request, execution_plan, synthesized_result)
            
        except Exception as e:
            logger.error(f"❌ Intelligent task execution failed: {e}")
            return f"❌ Task execution failed: {e}"
    
    def _perform_strategic_reasoning(self, user_request: str, task_id: str) -> Optional[Any]:
        """Perform strategic reasoning for task planning"""
        try:
            # Take screenshot for visual context
            screenshot_b64 = self.take_screenshot()
            
            # Create reasoning context
            context = ReasoningContext(
                task_description=user_request,
                current_state=self._get_current_system_state(),
                available_tools=list(self.tools.keys()),
                constraints=self._identify_constraints(),
                objectives=self._extract_objectives(user_request),
                complexity_level=self._assess_complexity(user_request),
                user_preferences=self._get_user_preferences(),
                historical_data=self._get_relevant_history(user_request)
            )
            
            # Perform strategic reasoning
            result = self.reasoning_engine.reason(
                ReasoningType.STRATEGIC_PLANNING,
                context,
                screenshot_b64
            )
            
            print(f"🎯 Strategic Analysis Complete:")
            print(f"  • Confidence: {result.confidence.value}")
            print(f"  • Success Probability: {result.success_probability:.1%}")
            print(f"  • Execution Phases: {len(result.execution_plan)}")
            
            return result
            
        except Exception as e:
            logger.error(f"❌ Strategic reasoning failed: {e}")
            return None
    
    def _perform_multi_perspective_reasoning(self, user_request: str, 
                                           reasoning_types: List[ReasoningType]) -> Dict:
        """Perform reasoning from multiple perspectives"""
        try:
            screenshot_b64 = self.take_screenshot()
            
            context = ReasoningContext(
                task_description=user_request,
                current_state=self._get_current_system_state(),
                available_tools=list(self.tools.keys()),
                constraints=self._identify_constraints(),
                objectives=self._extract_objectives(user_request),
                historical_data=self._get_relevant_history(user_request)
            )
            
            # Perform multi-perspective reasoning
            results = self.reasoning_engine.multi_perspective_reasoning(
                context, reasoning_types, screenshot_b64
            )
            
            print(f"🔍 Multi-Perspective Analysis:")
            for reasoning_type, result in results.items():
                print(f"  • {reasoning_type.value}: {result.confidence.value} ({result.success_probability:.1%})")
            
            return results
            
        except Exception as e:
            logger.error(f"❌ Multi-perspective reasoning failed: {e}")
            return {}
    
    def _create_intelligent_execution_plan(self, reasoning_result, user_request: str) -> List[Dict]:
        """Create intelligent execution plan based on reasoning"""
        try:
            # Extract execution steps from reasoning
            base_plan = reasoning_result.execution_plan
            
            # Enhance plan with tactical reasoning
            enhanced_plan = []
            
            for step in base_plan:
                # Perform tactical reasoning for each step
                tactical_context = ReasoningContext(
                    task_description=f"Execute step: {step.get('description', step.get('name', 'Unknown step'))}",
                    current_state=self._get_current_system_state(),
                    available_tools=step.get('tools', list(self.tools.keys())),
                    constraints=self._identify_constraints(),
                    objectives=[step.get('purpose', 'Complete step successfully')]
                )
                
                tactical_result = self.reasoning_engine.reason(
                    ReasoningType.TACTICAL_EXECUTION,
                    tactical_context
                )
                
                # Enhance step with tactical insights
                enhanced_step = step.copy()
                enhanced_step['tactical_guidance'] = tactical_result.decision
                enhanced_step['confidence'] = tactical_result.confidence.value
                enhanced_step['monitoring_points'] = tactical_result.monitoring_points
                enhanced_step['fallback_strategies'] = tactical_result.fallback_strategies
                
                enhanced_plan.append(enhanced_step)
            
            print(f"📋 Intelligent Execution Plan Created: {len(enhanced_plan)} enhanced steps")
            return enhanced_plan
            
        except Exception as e:
            logger.error(f"❌ Intelligent execution planning failed: {e}")
            return reasoning_result.execution_plan if reasoning_result else []
    
    def _execute_with_reasoning(self, task_id: str, user_request: str, 
                              execution_plan: List[Dict], reasoning_result) -> str:
        """Execute task with continuous reasoning and adaptation"""
        try:
            # Create task memory with reasoning context
            task_memory = self.memory.create_task_memory(
                task_id=task_id,
                task_name=f"Intelligent Task: {user_request[:50]}...",
                user_request=user_request,
                execution_steps=execution_plan
            )
            
            # Add reasoning context to memory
            self.memory.update_task_context(task_id, {
                'reasoning_enabled': True,
                'initial_confidence': reasoning_result.confidence.value,
                'initial_success_probability': reasoning_result.success_probability,
                'reasoning_rationale': reasoning_result.rationale,
                'execution_start': datetime.now().isoformat()
            })
            
            start_time = time.time()
            successful_steps = 0
            
            for i, step in enumerate(execution_plan):
                step_id = f"intelligent_step_{i+1}"
                
                print(f"\n📋 Intelligent Step {i+1}/{len(execution_plan)}")
                print(f"🎯 Action: {step.get('description', step.get('name', 'Unknown action'))}")
                
                # Pre-execution reasoning
                if self.adaptive_reasoning:
                    adaptation_result = self._perform_adaptive_reasoning(task_id, step, i)
                    if adaptation_result and adaptation_result.confidence != ConfidenceLevel.VERY_LOW:
                        # Apply adaptive insights
                        step = self._apply_adaptive_insights(step, adaptation_result)
                
                # Execute step with reasoning guidance
                success = self._execute_reasoning_guided_step(task_id, step_id, step)
                
                if success:
                    successful_steps += 1
                    self.memory.mark_step_completed(task_id, step_id, {
                        'reasoning_guided': True,
                        'confidence': step.get('confidence', 'medium'),
                        'execution_time': time.time() - start_time
                    })
                    
                    print(f"✅ Intelligent step {i+1} completed successfully")
                else:
                    # Reasoning-guided recovery
                    print(f"❌ Step {i+1} failed, applying reasoning-guided recovery...")
                    recovery_success = self._reasoning_guided_recovery(task_id, step, i)
                    
                    if recovery_success:
                        successful_steps += 1
                        print(f"🔧 Recovery successful for step {i+1}")
                    else:
                        self.memory.mark_step_failed(task_id, step_id, "Reasoning-guided recovery failed")
                        print(f"❌ Recovery failed for step {i+1}")
                        break
                
                # Brief pause for system stability
                time.sleep(0.3)
            
            # Task completion analysis
            completion_time = time.time() - start_time
            success_rate = successful_steps / len(execution_plan)
            
            # Update task memory with results
            task_memory.current_state = TaskState.COMPLETED if success_rate > 0.8 else TaskState.FAILED
            self.memory.update_task_context(task_id, {
                'completion_time': completion_time,
                'success_rate': success_rate,
                'successful_steps': successful_steps,
                'total_steps': len(execution_plan),
                'reasoning_performance': 'excellent' if success_rate > 0.9 else 'good' if success_rate > 0.7 else 'poor'
            })
            
            # Learn from execution
            if self.learning_enabled:
                self._learn_from_reasoning_execution(task_id, reasoning_result, success_rate)
            
            return self._create_intelligent_completion_summary(task_memory, reasoning_result, success_rate, completion_time)
            
        except Exception as e:
            logger.error(f"❌ Reasoning-guided execution failed: {e}")
            return f"❌ Intelligent execution failed: {e}"
    
    def _perform_adaptive_reasoning(self, task_id: str, step: Dict, step_index: int):
        """Perform adaptive reasoning for step optimization"""
        try:
            # Get current context and history
            context = self.memory.get_task_context(task_id)
            
            adaptive_context = ReasoningContext(
                task_description=f"Optimize execution of step: {step.get('description', 'Unknown step')}",
                current_state=context or {},
                available_tools=step.get('tools', list(self.tools.keys())),
                constraints=self._identify_constraints(),
                objectives=[step.get('purpose', 'Execute step optimally')],
                historical_data=self._get_step_execution_history(step_index)
            )
            
            return self.reasoning_engine.reason(
                ReasoningType.ADAPTIVE_LEARNING,
                adaptive_context
            )
            
        except Exception as e:
            logger.error(f"❌ Adaptive reasoning failed: {e}")
            return None
    
    def _apply_adaptive_insights(self, step: Dict, adaptation_result) -> Dict:
        """Apply adaptive reasoning insights to step execution"""
        try:
            adaptive_strategy = adaptation_result.decision.get('adaptive_strategy', {})
            
            # Apply parameter optimizations
            if 'parameter_optimizations' in adaptive_strategy:
                optimizations = adaptive_strategy['parameter_optimizations']
                if 'parameters' not in step:
                    step['parameters'] = {}
                step['parameters'].update(optimizations)
            
            # Apply strategy adjustments
            if 'strategy_adjustments' in adaptive_strategy:
                step['adaptive_adjustments'] = adaptive_strategy['strategy_adjustments']
            
            # Add predictive insights
            if 'predictive_insights' in adaptation_result.decision:
                step['predictive_insights'] = adaptation_result.decision['predictive_insights']
            
            return step
            
        except Exception as e:
            logger.error(f"❌ Failed to apply adaptive insights: {e}")
            return step
    
    def _execute_reasoning_guided_step(self, task_id: str, step_id: str, step: Dict) -> bool:
        """Execute a step with reasoning guidance"""
        try:
            # Get tactical guidance if available
            tactical_guidance = step.get('tactical_guidance', {})
            immediate_action = tactical_guidance.get('immediate_action', {})
            
            if immediate_action:
                # Use reasoning-guided action
                tool_name = immediate_action.get('tool')
                parameters = immediate_action.get('parameters', {})
                
                if tool_name in self.tools:
                    # Handle parameter mapping
                    if tool_name == 'open_application' and 'application_name' in parameters:
                        parameters['app_name'] = parameters.pop('application_name')
                    
                    # Execute with reasoning context
                    tool_function = self.tools[tool_name]
                    result = tool_function(**parameters)
                    
                    # Update context with reasoning-guided result
                    self.memory.update_task_context(task_id, {
                        f'{step_id}_reasoning_guided': True,
                        f'{step_id}_tactical_action': immediate_action,
                        f'{step_id}_result': result
                    })
                    
                    return result
            
            # Fallback to standard execution
            return self._execute_step_with_memory(task_id, step_id, step)
            
        except Exception as e:
            logger.error(f"❌ Reasoning-guided step execution failed: {e}")
            return False
    
    def _reasoning_guided_recovery(self, task_id: str, failed_step: Dict, step_index: int) -> bool:
        """Perform reasoning-guided recovery from step failure"""
        try:
            # Perform problem-solving reasoning
            problem_context = ReasoningContext(
                task_description=f"Recover from failed step: {failed_step.get('description', 'Unknown step')}",
                current_state=self.memory.get_task_context(task_id) or {},
                available_tools=list(self.tools.keys()),
                constraints=self._identify_constraints(),
                objectives=['Recover from failure', 'Continue task execution']
            )
            
            problem_result = self.reasoning_engine.reason(
                ReasoningType.PROBLEM_SOLVING,
                problem_context
            )
            
            if problem_result.confidence != ConfidenceLevel.VERY_LOW:
                # Apply recommended solution
                recommended_solution = problem_result.decision.get('recommended_solution', {})
                implementation_steps = recommended_solution.get('implementation_steps', [])
                
                for recovery_step in implementation_steps:
                    # Try to execute recovery step
                    if self._execute_recovery_action(recovery_step):
                        print(f"🔧 Recovery action successful: {recovery_step}")
                        return True
            
            # Fallback to memory-guided recovery
            return self._attempt_memory_guided_recovery(task_id, failed_step)
            
        except Exception as e:
            logger.error(f"❌ Reasoning-guided recovery failed: {e}")
            return False
    
    def _execute_recovery_action(self, recovery_step: str) -> bool:
        """Execute a recovery action"""
        try:
            # Simple recovery actions
            if 'wait' in recovery_step.lower():
                time.sleep(2)
                return True
            elif 'retry' in recovery_step.lower():
                return True  # Signal to retry
            elif 'screenshot' in recovery_step.lower():
                self.take_screenshot()
                return True
            
            return False
            
        except Exception:
            return False

    def _learn_from_reasoning_execution(self, task_id: str, reasoning_result, success_rate: float):
        """Learn from reasoning-guided execution"""
        try:
            # Store reasoning performance
            self.reasoning_performance[task_id] = {
                'initial_confidence': reasoning_result.confidence.value,
                'initial_success_probability': reasoning_result.success_probability,
                'actual_success_rate': success_rate,
                'reasoning_accuracy': abs(reasoning_result.success_probability - success_rate),
                'timestamp': datetime.now().isoformat()
            }

            # Learn patterns for future use
            if success_rate > 0.8:
                # Learn successful reasoning pattern
                self.memory.learn_pattern(
                    MemoryType.SUCCESS_PATTERN,
                    {
                        'reasoning_type': reasoning_result.reasoning_type.value,
                        'confidence_level': reasoning_result.confidence.value,
                        'execution_approach': reasoning_result.decision,
                        'success_rate': success_rate
                    },
                    True
                )
            else:
                # Learn from failure
                self.memory.learn_pattern(
                    MemoryType.ERROR_PATTERN,
                    {
                        'reasoning_type': reasoning_result.reasoning_type.value,
                        'confidence_level': reasoning_result.confidence.value,
                        'execution_approach': reasoning_result.decision,
                        'failure_rate': 1.0 - success_rate
                    },
                    False
                )

            logger.info(f"📚 Learned from reasoning execution: {success_rate:.1%} success rate")

        except Exception as e:
            logger.error(f"❌ Learning from reasoning execution failed: {e}")

    def _create_intelligent_completion_summary(self, task_memory, reasoning_result,
                                             success_rate: float, completion_time: float) -> str:
        """Create intelligent task completion summary"""

        reasoning_accuracy = abs(reasoning_result.success_probability - success_rate)
        reasoning_quality = "excellent" if reasoning_accuracy < 0.1 else "good" if reasoning_accuracy < 0.2 else "poor"

        summary = f"""
🧠 INTELLIGENT TASK EXECUTION COMPLETE!

🎯 Task: {task_memory.task_name}
🆔 Task ID: {task_memory.task_id}

📊 EXECUTION RESULTS:
  • Success Rate: {success_rate:.1%}
  • Execution Time: {completion_time:.1f} seconds
  • Steps Completed: {len(task_memory.completed_steps)}/{len(task_memory.execution_steps)}

🧠 REASONING PERFORMANCE:
  • Initial Confidence: {reasoning_result.confidence.value}
  • Predicted Success: {reasoning_result.success_probability:.1%}
  • Actual Success: {success_rate:.1%}
  • Reasoning Accuracy: {reasoning_quality} ({reasoning_accuracy:.1%} deviation)

🎉 INTELLIGENT FEATURES USED:
  • Strategic Planning: ✅
  • Tactical Execution: ✅
  • Adaptive Learning: ✅
  • Memory Integration: ✅
  • Pattern Recognition: ✅

💾 All reasoning patterns and execution data saved for future optimization
🧠 Agent intelligence improved through this execution
"""
        return summary.strip()

    # Utility methods for reasoning context

    def _get_current_system_state(self) -> Dict[str, Any]:
        """Get current system state for reasoning"""
        try:
            import psutil

            return {
                'screen_resolution': f"{self.screen_width}x{self.screen_height}",
                'memory_usage': psutil.virtual_memory().percent,
                'cpu_usage': psutil.cpu_percent(),
                'active_tools': len(self.tools),
                'timestamp': datetime.now().isoformat()
            }
        except Exception:
            return {'timestamp': datetime.now().isoformat()}

    def _identify_constraints(self) -> List[str]:
        """Identify current system constraints"""
        constraints = [
            "Windows operating system",
            "GUI-based interactions only",
            "No destructive operations",
            "Respect user privacy"
        ]

        try:
            import psutil

            # Add resource constraints
            memory_percent = psutil.virtual_memory().percent
            if memory_percent > 80:
                constraints.append("High memory usage - optimize operations")

            cpu_percent = psutil.cpu_percent()
            if cpu_percent > 80:
                constraints.append("High CPU usage - reduce intensive operations")

        except Exception:
            pass

        return constraints

    def _extract_objectives(self, user_request: str) -> List[str]:
        """Extract objectives from user request"""
        objectives = ["Complete user request successfully"]

        # Simple keyword-based objective extraction
        if "fast" in user_request.lower() or "quick" in user_request.lower():
            objectives.append("Optimize for speed")

        if "accurate" in user_request.lower() or "precise" in user_request.lower():
            objectives.append("Optimize for accuracy")

        if "safe" in user_request.lower() or "careful" in user_request.lower():
            objectives.append("Prioritize safety")

        return objectives

    def _assess_complexity(self, user_request: str) -> str:
        """Assess task complexity"""
        request_lower = user_request.lower()

        # Simple complexity assessment
        complex_keywords = ["multiple", "several", "complex", "advanced", "comprehensive", "detailed"]
        simple_keywords = ["open", "click", "type", "simple", "basic"]

        complex_count = sum(1 for keyword in complex_keywords if keyword in request_lower)
        simple_count = sum(1 for keyword in simple_keywords if keyword in request_lower)

        if complex_count > simple_count:
            return "high"
        elif simple_count > complex_count:
            return "low"
        else:
            return "medium"

    def _get_user_preferences(self) -> Dict[str, Any]:
        """Get user preferences for reasoning"""
        # This could be enhanced to load actual user preferences
        return {
            "speed_preference": "balanced",
            "accuracy_preference": "high",
            "automation_level": "high",
            "risk_tolerance": "medium"
        }

    def _get_relevant_history(self, user_request: str) -> List[Dict]:
        """Get relevant historical data for reasoning"""
        try:
            # Get recent task history
            history = self.memory.get_task_history(10)

            # Filter for relevant tasks (simple keyword matching)
            relevant = []
            request_words = set(user_request.lower().split())

            for task in history:
                task_words = set(task['user_request'].lower().split())
                if len(request_words.intersection(task_words)) > 0:
                    relevant.append(task)

            return relevant[:5]  # Return top 5 relevant tasks

        except Exception:
            return []

    def _get_step_execution_history(self, step_index: int) -> List[Dict]:
        """Get execution history for similar steps"""
        try:
            # This could be enhanced to analyze step patterns
            return list(self.reasoning_performance.values())[-5:]  # Last 5 performances
        except Exception:
            return []

    def run_reasoning_enhanced_mode(self):
        """Run the reasoning-enhanced agent"""
        print("\n🧠 REASONING-ENHANCED AGENT")
        print("=" * 50)
        print("🎯 I can perform sophisticated reasoning and intelligent task execution!")
        print("✨ Advanced Capabilities:")
        print("  • Strategic planning and tactical execution")
        print("  • Multi-perspective reasoning and decision making")
        print("  • Adaptive learning from experience")
        print("  • Causal analysis and predictive modeling")
        print("  • Creative problem solving")
        print("  • Real-time reasoning adaptation")
        print()

        # Show reasoning stats
        reasoning_stats = self.reasoning_engine.get_reasoning_stats()
        memory_stats = self.memory.get_memory_stats()

        print(f"📊 System Status:")
        print(f"  • Reasoning Success Rate: {reasoning_stats.get('success_rate', 0):.1%}")
        print(f"  • Average Confidence: {reasoning_stats.get('average_confidence', 0):.1%}")
        print(f"  • Memory Tasks: {memory_stats.get('total_tasks', 0)}")
        print(f"  • Learned Patterns: {sum(memory_stats.get('pattern_counts', {}).values())}")
        print()

        print("💡 Intelligent Commands:")
        print("  • 'intelligent: [description]' - Full reasoning-guided execution")
        print("  • 'strategic: [description]' - Strategic planning focus")
        print("  • 'creative: [description]' - Creative problem solving")
        print("  • 'analyze: [description]' - Multi-perspective analysis")
        print("  • 'reasoning stats' - Show reasoning performance")
        print("  • Regular commands work with enhanced intelligence!")
        print()

        while True:
            try:
                request = input("🧠 What intelligent task can I help you with? ").strip()

                if request.lower() in ['quit', 'exit', 'bye']:
                    break

                if request.lower() == 'reasoning stats':
                    self._show_reasoning_stats()
                    continue

                if request.lower().startswith('intelligent:'):
                    task_description = request[12:].strip()
                    reasoning_types = [
                        ReasoningType.STRATEGIC_PLANNING,
                        ReasoningType.TACTICAL_EXECUTION,
                        ReasoningType.PROBLEM_SOLVING,
                        ReasoningType.DECISION_MAKING
                    ]
                    result = self.execute_intelligent_task(task_description, reasoning_types)
                    print(result)
                    continue

                if request.lower().startswith('strategic:'):
                    task_description = request[10:].strip()
                    result = self.execute_intelligent_task(task_description, [ReasoningType.STRATEGIC_PLANNING])
                    print(result)
                    continue

                if request.lower().startswith('creative:'):
                    task_description = request[9:].strip()
                    result = self.execute_intelligent_task(task_description, [ReasoningType.CREATIVE_SYNTHESIS])
                    print(result)
                    continue

                if request.lower().startswith('analyze:'):
                    task_description = request[8:].strip()
                    reasoning_types = [
                        ReasoningType.CAUSAL_ANALYSIS,
                        ReasoningType.PREDICTIVE_MODELING,
                        ReasoningType.DECISION_MAKING
                    ]
                    result = self.execute_intelligent_task(task_description, reasoning_types)
                    print(result)
                    continue

                if request:
                    # Enhanced execution with basic reasoning
                    result = self.execute_intelligent_task(request, [ReasoningType.TACTICAL_EXECUTION])
                    print(result)

            except KeyboardInterrupt:
                print("\n👋 Goodbye!")
                break
            except Exception as e:
                print(f"❌ Error: {e}")

        # Show final stats
        final_stats = self.reasoning_engine.get_reasoning_stats()
        print(f"\n📊 Session Complete!")
        print(f"  • Total Reasonings: {final_stats.get('performance_metrics', {}).get('total_reasonings', 0)}")
        print(f"  • Success Rate: {final_stats.get('success_rate', 0):.1%}")
        print(f"  • Average Confidence: {final_stats.get('average_confidence', 0):.1%}")

    def _show_reasoning_stats(self):
        """Show detailed reasoning statistics"""
        stats = self.reasoning_engine.get_reasoning_stats()
        history = self.reasoning_engine.get_recent_reasoning_history(5)

        print(f"\n🧠 REASONING SYSTEM STATISTICS")
        print("=" * 40)
        print(f"📊 Performance Metrics:")
        for key, value in stats['performance_metrics'].items():
            if isinstance(value, float):
                print(f"  • {key}: {value:.2f}")
            else:
                print(f"  • {key}: {value}")

        print(f"\n🎯 Recent Reasoning History:")
        for i, entry in enumerate(history, 1):
            print(f"  {i}. {entry['reasoning_type']} - {entry['confidence']} ({entry['success_probability']:.1%})")

        print(f"\n💾 Cache and Memory:")
        print(f"  • Cache Size: {stats['cache_size']}")
        print(f"  • History Size: {stats['history_size']}")
        print(f"  • Types Used: {', '.join(stats['reasoning_types_used'])}")


def main():
    """Main entry point"""
    try:
        agent = ReasoningEnhancedAgent()
        agent.run_reasoning_enhanced_mode()
    except KeyboardInterrupt:
        print("\n👋 Goodbye!")
    except Exception as e:
        print(f"❌ Fatal error: {e}")


if __name__ == "__main__":
    main()
