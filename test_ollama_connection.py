#!/usr/bin/env python3
"""
Test script to verify Ollama connection with Agent-S
"""

import requests
import json
import base64
import pyautogui
import io
from PIL import Image

def test_ollama_connection():
    """Test basic Ollama connection"""
    print("🔍 Testing Ollama Connection...")
    
    try:
        # Test basic connection
        response = requests.get("http://localhost:11434/api/tags", timeout=5)
        if response.status_code == 200:
            print("✅ Ollama server is running")
            models = response.json()
            print(f"📋 Available models: {len(models['models'])}")
            return True
        else:
            print(f"❌ Ollama server returned status: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Cannot connect to Ollama: {e}")
        return False

def test_vision_model():
    """Test vision model with screenshot"""
    print("\n🖼️  Testing Vision Model...")
    
    # Take screenshot
    screenshot = pyautogui.screenshot()
    screenshot = screenshot.resize((800, 600), Image.LANCZOS)
    
    # Convert to base64
    buffered = io.BytesIO()
    screenshot.save(buffered, format="JPEG")
    img_str = base64.b64encode(buffered.getvalue()).decode()
    
    # Test with vision model
    models_to_test = ["qwen2.5vl:32b", "llava:34b", "llava:13b", "minicpm-v:8b", "llava:7b"]
    
    for model in models_to_test:
        try:
            print(f"  Testing {model}...")
            
            payload = {
                "model": model,
                "messages": [
                    {
                        "role": "user",
                        "content": [
                            {"type": "text", "text": "What do you see in this image? Be brief."},
                            {"type": "image_url", "image_url": {"url": f"data:image/jpeg;base64,{img_str}"}}
                        ]
                    }
                ]
            }
            
            response = requests.post(
                "http://localhost:11434/v1/chat/completions",
                json=payload,
                timeout=30
            )
            
            if response.status_code == 200:
                result = response.json()
                content = result['choices'][0]['message']['content']
                print(f"  ✅ {model}: {content[:100]}...")
                return model  # Return first working model
            else:
                print(f"  ❌ {model}: Status {response.status_code}")
                
        except Exception as e:
            print(f"  ❌ {model}: {e}")
    
    return None

def test_openai_compatibility():
    """Test OpenAI API compatibility"""
    print("\n🔌 Testing OpenAI API Compatibility...")
    
    try:
        from openai import OpenAI
        
        client = OpenAI(
            base_url="http://localhost:11434/v1/",
            api_key="not-needed"
        )
        
        # Test with a simple model
        response = client.chat.completions.create(
            model="llama3.2:latest",
            messages=[{"role": "user", "content": "Hello, respond with just 'OK'"}],
            max_tokens=10
        )
        
        print(f"✅ OpenAI compatibility: {response.choices[0].message.content}")
        return True
        
    except Exception as e:
        print(f"❌ OpenAI compatibility failed: {e}")
        return False

def main():
    print("🚀 OLLAMA + AGENT-S COMPATIBILITY TEST")
    print("=" * 50)
    
    # Test basic connection
    if not test_ollama_connection():
        print("\n❌ FAILED: Cannot connect to Ollama")
        print("💡 Start Ollama server: ollama serve")
        return
    
    # Test vision models
    best_model = test_vision_model()
    if best_model:
        print(f"\n✅ Best working vision model: {best_model}")
    else:
        print("\n❌ No vision models working")
    
    # Test OpenAI compatibility
    if test_openai_compatibility():
        print("\n✅ OpenAI API compatibility confirmed")
    else:
        print("\n❌ OpenAI API compatibility failed")
    
    print("\n🎯 RESULTS:")
    print("=" * 30)
    
    if best_model:
        print(f"✅ Ready to use Agent-S with {best_model}")
        print("🚀 Run: python run_agent_s_ollama.py")
    else:
        print("❌ Setup incomplete")
        print("💡 Install vision model: ollama pull llava:13b")

if __name__ == "__main__":
    main() 