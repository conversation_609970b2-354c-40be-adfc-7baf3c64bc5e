#!/usr/bin/env python3
"""
Test script to verify browser opening works correctly
"""

import time
import pya<PERSON><PERSON><PERSON>

def test_browser_opening():
    """Test different browser opening strategies"""
    print("🧪 Testing Browser Opening Strategies")
    print("=" * 50)
    
    # Strategy 1: Windows search for Chrome
    print("\n1. Testing Windows search for Chrome...")
    try:
        pyautogui.press('win')
        time.sleep(1)
        pyautogui.write('chrome')
        time.sleep(1)
        pyautogui.press('enter')
        time.sleep(3)
        
        print("✅ Chrome search completed")
        
        # Close any opened window
        pyautogui.hotkey('alt', 'f4')
        time.sleep(1)
        
    except Exception as e:
        print(f"❌ Chrome search failed: {e}")
    
    # Strategy 2: Run dialog
    print("\n2. Testing Run dialog...")
    try:
        pyautogui.hotkey('win', 'r')
        time.sleep(1)
        pyautogui.write('chrome.exe')
        pyautogui.press('enter')
        time.sleep(3)
        
        print("✅ Run dialog completed")
        
        # Close any opened window
        pyautogui.hotkey('alt', 'f4')
        time.sleep(1)
        
    except Exception as e:
        print(f"❌ Run dialog failed: {e}")
    
    # Strategy 3: Try Edge (built into Windows)
    print("\n3. Testing Edge browser...")
    try:
        pyautogui.press('win')
        time.sleep(1)
        pyautogui.write('edge')
        time.sleep(1)
        pyautogui.press('enter')
        time.sleep(3)
        
        print("✅ Edge search completed")
        
        # Close any opened window
        pyautogui.hotkey('alt', 'f4')
        time.sleep(1)
        
    except Exception as e:
        print(f"❌ Edge search failed: {e}")
    
    print("\n🎉 Browser opening test completed!")
    print("If any strategy worked, the browser fix should work.")

def test_production_agent_browser():
    """Test the production agent browser opening"""
    print("\n🏭 Testing Production Agent Browser Opening...")
    
    try:
        from production_agent import ProductionAgent
        
        agent = ProductionAgent()
        result = agent.execute_task_production("open browser")
        
        if result["success"]:
            print("✅ Production agent browser opening: SUCCESS")
            print(f"   Execution time: {result['execution_time']:.2f}s")
        else:
            print(f"❌ Production agent browser opening: FAILED")
            print(f"   Error: {result.get('error', 'Unknown error')}")
            
    except Exception as e:
        print(f"❌ Production agent test failed: {e}")

def main():
    """Main test function"""
    print("🚀 BROWSER OPENING FIX TEST")
    print("=" * 60)
    print("This will test if browser opening works correctly")
    print("and doesn't open Cursor or other applications instead.")
    print()
    
    # Give user time to prepare
    print("⚠️ This test will open and close browser windows.")
    print("Make sure you're ready and have saved any work.")
    input("Press Enter to continue...")
    
    # Test basic strategies
    test_browser_opening()
    
    # Test production agent
    test_production_agent_browser()
    
    print("\n" + "=" * 60)
    print("🎯 TEST SUMMARY:")
    print("If browsers opened correctly (not Cursor), the fix is working!")
    print("If Cursor still opens, we need to adjust the strategy further.")

if __name__ == "__main__":
    main()
