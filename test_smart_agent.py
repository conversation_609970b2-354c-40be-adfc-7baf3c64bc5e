#!/usr/bin/env python3
"""
Test script for Smart Agent-S
Quick validation that the system works properly
"""

import os
import sys
import time
from smart_agent import SmartAgent

def test_basic_functionality():
    """Test basic agent functionality"""
    print("🧪 Testing Smart Agent-S...")
    print("=" * 40)
    
    try:
        # Initialize agent
        print("1. Initializing agent...")
        agent = SmartAgent()
        print("✅ Agent initialized successfully")
        
        # Test screenshot capability
        print("\n2. Testing screenshot capability...")
        result = agent.execute_task("take a screenshot")
        if result["success"]:
            print("✅ Screenshot test passed")
        else:
            print(f"❌ Screenshot test failed: {result.get('error', 'Unknown error')}")
        
        # Test stats
        print("\n3. Testing stats...")
        print(f"   Tasks completed: {agent.stats['tasks_completed']}")
        print(f"   Success rate: {agent.stats['success_rate']:.1%}")
        print(f"   Adaptations: {agent.stats['adaptations_made']}")
        
        # Test AI vision if available
        print("\n4. Testing AI vision...")
        if agent.vision.providers:
            print(f"✅ AI providers available: {list(agent.vision.providers.keys())}")
            print(f"   Current provider: {agent.vision.current_provider}")
        else:
            print("⚠️ No AI providers available (using basic vision)")
        
        print("\n🎉 All basic tests passed!")
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        return False

def test_browser_task():
    """Test browser opening task"""
    print("\n🌐 Testing browser task...")
    
    try:
        agent = SmartAgent()
        result = agent.execute_task("open browser")
        
        if result["success"]:
            print("✅ Browser task completed successfully")
            print(f"   Execution time: {result['execution_time']:.2f}s")
            print(f"   Attempts: {result.get('attempts', 1)}")
        else:
            print(f"❌ Browser task failed: {result.get('error', 'Unknown error')}")
        
        return result["success"]
        
    except Exception as e:
        print(f"❌ Browser test failed: {e}")
        return False

def main():
    """Main test function"""
    print("🚀 SMART AGENT-S TEST SUITE")
    print("=" * 50)
    
    # Check environment
    has_openai = bool(os.getenv('OPENAI_API_KEY'))
    has_anthropic = bool(os.getenv('ANTHROPIC_API_KEY'))
    
    print(f"Environment check:")
    print(f"   OpenAI API Key: {'✅' if has_openai else '❌'}")
    print(f"   Anthropic API Key: {'✅' if has_anthropic else '❌'}")
    
    if not has_openai and not has_anthropic:
        print("⚠️ No AI API keys found - will use basic vision only")
    
    # Run tests
    tests_passed = 0
    total_tests = 2
    
    if test_basic_functionality():
        tests_passed += 1
    
    if test_browser_task():
        tests_passed += 1
    
    # Results
    print("\n" + "=" * 50)
    print(f"TEST RESULTS: {tests_passed}/{total_tests} tests passed")
    
    if tests_passed == total_tests:
        print("🎉 All tests passed! Agent-S is working correctly.")
    else:
        print("⚠️ Some tests failed. Check the output above for details.")
    
    return tests_passed == total_tests

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
