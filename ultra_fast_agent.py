#!/usr/bin/env python3
"""
Ultra-Fast Agent with Multiple Input Methods
Combines AI vision with pre-programmed efficiency
"""

import pyautogui
import time
import re
import subprocess
import threading
from typing import Dict, List, Tuple

class UltraFastAgent:
    def __init__(self):
        # Ultra-speed settings
        pyautogui.PAUSE = 0.02
        pyautogui.FAILSAFE = True
        
        self.screen_width, self.screen_height = pyautogui.size()
        
        # Pre-compiled patterns for instant recognition
        self.patterns = {
            'open_app': re.compile(r'open\s+(\w+)', re.IGNORECASE),
            'calculate': re.compile(r'calc(?:ulate)?\s+(.+)', re.IGNORECASE),
            'type_text': re.compile(r'type\s+(.+)', re.IGNORECASE),
            'click_button': re.compile(r'click\s+(.+)', re.IGNORECASE),
            'close_app': re.compile(r'close\s+(.+)', re.IGNORECASE),
            'minimize': re.compile(r'minimize', re.IGNORECASE),
            'maximize': re.compile(r'maximize', re.IGNORECASE),
        }
        
        # Lightning-fast app launching with multiple methods
        self.app_commands = {
            'calculator': ['calc', 'calculator'],
            'calc': ['calc', 'calculator'],
            'notepad': ['notepad', 'notepad.exe'],
            'browser': ['msedge', 'chrome', 'firefox'],
            'edge': ['msedge', 'msedge.exe'],
            'chrome': ['chrome', 'chrome.exe'],
            'excel': ['excel', 'EXCEL.EXE', 'start excel'],
            'word': ['winword', 'WINWORD.EXE', 'start winword'],
            'outlook': ['outlook', 'OUTLOOK.EXE', 'start outlook'],
            'teams': ['teams', 'Teams.exe', 'start teams'],
            'explorer': ['explorer', 'explorer.exe'],
            'files': ['explorer', 'explorer.exe'],
            'paint': ['mspaint', 'mspaint.exe'],
            'cmd': ['cmd', 'cmd.exe'],
            'powershell': ['powershell', 'powershell.exe'],
            'terminal': ['wt', 'WindowsTerminal.exe']
        }
        
        # Common UI positions (learned from your screen)
        self.ui_positions = {
            'start': (50, self.screen_height - 50),
            'close': (self.screen_width - 20, 20),
            'minimize': (self.screen_width - 60, 20),
            'maximize': (self.screen_width - 40, 20),
            'taskbar_center': (self.screen_width // 2, self.screen_height - 40)
        }
    
    def instant_parse(self, command: str) -> Tuple[str, str]:
        """Lightning-fast command parsing"""
        command = command.strip()
        
        # Check each pattern
        for action, pattern in self.patterns.items():
            match = pattern.match(command)
            if match:
                return action, match.group(1) if match.groups() else ""
        
        # Default to AI processing if no pattern matches
        return "ai_process", command
    
    def lightning_app_open(self, app_name: str) -> bool:
        """Open apps in <1 second with multiple fallback methods"""
        app_name = app_name.lower().strip()

        if app_name in self.app_commands:
            commands = self.app_commands[app_name]

            # Try each command method until one works
            for cmd in commands:
                try:
                    # Method 1: Direct execution (fastest)
                    subprocess.Popen(cmd, shell=True)
                    print(f"⚡ Opened {app_name} instantly via {cmd}")
                    return True
                except:
                    continue

        # Method 2: Win+R (very fast)
        try:
            pyautogui.hotkey('win', 'r')
            time.sleep(0.1)
            first_cmd = self.app_commands.get(app_name, [app_name])[0]
            pyautogui.typewrite(first_cmd)
            pyautogui.press('enter')
            print(f"🚀 Opened {app_name} via Run dialog")
            return True
        except:
            pass

        # Method 3: Start menu search (reliable)
        try:
            pyautogui.press('win')
            time.sleep(0.2)
            pyautogui.typewrite(app_name)
            time.sleep(0.3)
            pyautogui.press('enter')
            print(f"✅ Opened {app_name} via Start menu")
            return True
        except:
            return False
    
    def smart_calculate(self, expression: str) -> None:
        """Fast calculation with result display"""
        # Clean expression
        expression = expression.replace('x', '*').replace('×', '*')
        
        # Open calculator super fast
        subprocess.Popen('calc', shell=True)
        time.sleep(0.8)  # Wait for calc to load
        
        # Type expression
        pyautogui.typewrite(expression)
        pyautogui.press('enter')
        print(f"🧮 Calculated: {expression}")
    
    def instant_type(self, text: str) -> None:
        """Type text with smart formatting"""
        # Smart replacements
        text = text.replace('\\n', '\n')  # Handle newlines
        
        pyautogui.typewrite(text, interval=0.01)  # Very fast typing
        print(f"⌨️ Typed: {text[:50]}...")
    
    def smart_click(self, target: str) -> bool:
        """Smart clicking based on common targets"""
        target = target.lower().strip()
        
        if target in self.ui_positions:
            pos = self.ui_positions[target]
            pyautogui.click(pos[0], pos[1])
            print(f"🖱️ Clicked {target}")
            return True
        
        # Common button names
        button_map = {
            'start': 'start',
            'close': 'close',
            'minimize': 'minimize',
            'maximize': 'maximize',
            'x': 'close',
            'exit': 'close'
        }
        
        if target in button_map:
            pos = self.ui_positions[button_map[target]]
            pyautogui.click(pos[0], pos[1])
            print(f"🖱️ Clicked {target}")
            return True
        
        return False
    
    def process_command(self, command: str) -> bool:
        """Ultra-fast command processing"""
        action, parameter = self.instant_parse(command)
        
        if action == "open_app":
            return self.lightning_app_open(parameter)
        
        elif action == "calculate":
            self.smart_calculate(parameter)
            return True
        
        elif action == "type_text":
            self.instant_type(parameter)
            return True
        
        elif action == "click_button":
            return self.smart_click(parameter)
        
        elif action == "close_app":
            pyautogui.hotkey('alt', 'f4')
            print(f"❌ Closed application")
            return True
        
        elif action == "minimize":
            self.smart_click('minimize')
            return True
        
        elif action == "maximize":
            self.smart_click('maximize')
            return True
        
        elif action == "ai_process":
            print(f"🤖 Complex command: {command}")
            print("   (Would use AI vision for complex tasks)")
            return False
        
        return False

def main():
    print("⚡ ULTRA-FAST AGENT")
    print("=" * 30)
    print("🚀 Instant Commands:")
    print("- 'open calculator' - Opens in <1s")
    print("- 'calc 15 * 25' - Instant calculation")
    print("- 'type Hello World' - Fast typing")
    print("- 'click start' - Smart UI clicking")
    print("- 'close' - Close current app")
    print("- 'minimize/maximize' - Window controls")
    print()
    
    agent = UltraFastAgent()
    
    while True:
        try:
            command = input("⚡ Command: ").strip()
            
            if command.lower() in ['quit', 'exit']:
                break
            
            start_time = time.time()
            success = agent.process_command(command)
            end_time = time.time()
            
            if success:
                print(f"✅ Completed in {end_time - start_time:.2f}s")
            else:
                print("❌ Command not recognized or failed")
                
        except KeyboardInterrupt:
            print("\n👋 Goodbye!")
            break
        except Exception as e:
            print(f"❌ Error: {e}")

if __name__ == "__main__":
    main()
