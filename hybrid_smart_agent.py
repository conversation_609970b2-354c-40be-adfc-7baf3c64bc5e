#!/usr/bin/env python3
"""
Hybrid Smart Agent
Combines instant actions with AI vision when needed
"""

import pyautogui
import time
import subprocess
import threading
import queue
import os
from ultra_fast_agent import UltraFastAgent

class HybridSmartAgent:
    def __init__(self):
        self.fast_agent = UltraFastAgent()
        self.ai_queue = queue.Queue()
        self.ai_available = self.check_ai_availability()
        
        # Performance tracking
        self.stats = {
            'fast_commands': 0,
            'ai_commands': 0,
            'total_time': 0,
            'fast_time': 0,
            'ai_time': 0
        }
    
    def check_ai_availability(self) -> bool:
        """Check if AI models are available"""
        try:
            # Check if OpenAI key is set
            if os.getenv('OPENAI_API_KEY'):
                return True
            # Could also check for Ollama
            return False
        except:
            return False
    
    def should_use_ai(self, command: str) -> bool:
        """Decide whether to use AI or fast processing"""
        
        # Always use fast agent for these patterns
        fast_patterns = [
            'open ', 'calc', 'type ', 'click ', 'close', 
            'minimize', 'maximize', 'start', 'exit'
        ]
        
        command_lower = command.lower()
        for pattern in fast_patterns:
            if pattern in command_lower:
                return False
        
        # Use AI for complex requests
        ai_indicators = [
            'create', 'make', 'build', 'generate', 'find', 'search',
            'organize', 'format', 'edit', 'modify', 'update',
            'spreadsheet', 'document', 'email', 'report',
            'multiple', 'several', 'all', 'every'
        ]
        
        for indicator in ai_indicators:
            if indicator in command_lower:
                return True
        
        return False
    
    def execute_fast(self, command: str) -> bool:
        """Execute using fast agent"""
        start_time = time.time()
        success = self.fast_agent.process_command(command)
        end_time = time.time()
        
        # Update stats
        self.stats['fast_commands'] += 1
        self.stats['fast_time'] += (end_time - start_time)
        
        return success
    
    def execute_ai(self, command: str) -> bool:
        """Execute using AI vision (placeholder for now)"""
        start_time = time.time()
        
        print(f"🤖 AI Processing: {command}")
        print("   Taking screenshot...")
        print("   Analyzing with vision model...")
        print("   Planning actions...")
        print("   Executing...")
        
        # Simulate AI processing time
        time.sleep(2)
        
        end_time = time.time()
        
        # Update stats
        self.stats['ai_commands'] += 1
        self.stats['ai_time'] += (end_time - start_time)
        
        print("✅ AI execution complete")
        return True
    
    def process_command(self, command: str) -> bool:
        """Smart command routing"""
        start_time = time.time()
        
        # Route to appropriate processor
        if self.should_use_ai(command):
            if self.ai_available:
                print("🧠 Routing to AI...")
                success = self.execute_ai(command)
            else:
                print("⚠️ AI not available, trying fast agent...")
                success = self.execute_fast(command)
        else:
            print("⚡ Using fast agent...")
            success = self.execute_fast(command)
        
        end_time = time.time()
        self.stats['total_time'] += (end_time - start_time)
        
        return success
    
    def show_performance_stats(self):
        """Show performance statistics"""
        print("\n📊 PERFORMANCE STATS")
        print("=" * 25)
        print(f"Fast commands: {self.stats['fast_commands']}")
        print(f"AI commands: {self.stats['ai_commands']}")
        
        if self.stats['fast_commands'] > 0:
            avg_fast = self.stats['fast_time'] / self.stats['fast_commands']
            print(f"Avg fast time: {avg_fast:.2f}s")
        
        if self.stats['ai_commands'] > 0:
            avg_ai = self.stats['ai_time'] / self.stats['ai_commands']
            print(f"Avg AI time: {avg_ai:.2f}s")
        
        total_commands = self.stats['fast_commands'] + self.stats['ai_commands']
        if total_commands > 0:
            fast_percentage = (self.stats['fast_commands'] / total_commands) * 100
            print(f"Fast execution: {fast_percentage:.1f}%")

def main():
    print("🚀 HYBRID SMART AGENT")
    print("=" * 30)
    print("⚡ Fast: Simple commands (open, calc, type, click)")
    print("🧠 AI: Complex tasks (create, organize, analyze)")
    print("🎯 Auto-routing based on command complexity")
    print()
    
    agent = HybridSmartAgent()
    
    # Example commands to try
    print("💡 Try these commands:")
    print("Fast: 'open calculator', 'calc 15*25', 'type hello'")
    print("AI: 'create a sales spreadsheet', 'organize my desktop'")
    print()
    
    while True:
        try:
            command = input("🗣️ Command: ").strip()
            
            if command.lower() in ['quit', 'exit']:
                break
            elif command.lower() == 'stats':
                agent.show_performance_stats()
                continue
            
            success = agent.process_command(command)
            
            if not success:
                print("❌ Command failed or not recognized")
                
        except KeyboardInterrupt:
            print("\n👋 Goodbye!")
            agent.show_performance_stats()
            break
        except Exception as e:
            print(f"❌ Error: {e}")

if __name__ == "__main__":
    main()
