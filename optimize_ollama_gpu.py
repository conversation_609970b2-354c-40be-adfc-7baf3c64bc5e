#!/usr/bin/env python3
"""
Optimize Ollama for GPU Performance
"""

import os
import requests
import subprocess
import time
import json

def check_gpu_availability():
    """Check if GPU is available and what type"""
    try:
        # Check for NVIDIA GPU
        result = subprocess.run(['nvidia-smi'], capture_output=True, text=True)
        if result.returncode == 0:
            print("✅ NVIDIA GPU detected")
            print(result.stdout.split('\n')[8:12])  # Show GPU info
            return "nvidia"
    except:
        pass
    
    try:
        # Check for AMD GPU (ROCm)
        result = subprocess.run(['rocm-smi'], capture_output=True, text=True)
        if result.returncode == 0:
            print("✅ AMD GPU detected")
            return "amd"
    except:
        pass
    
    print("⚠️ No GPU detected or drivers not installed")
    return None

def optimize_ollama_gpu():
    """Set optimal GPU settings for Ollama"""
    
    # Set environment variables for GPU optimization
    gpu_env = {
        'OLLAMA_NUM_GPU': '-1',  # Use all available GPUs
        'OLLAMA_GPU_LAYERS': '50',  # Offload layers to GPU
        'OLLAMA_MAIN_GPU': '0',  # Primary GPU
        'OLLAMA_TENSOR_SPLIT': '',  # Auto-split tensors
        'OLLAMA_HOST': '0.0.0.0:11434',  # Bind to all interfaces
        'OLLAMA_ORIGINS': '*',  # Allow all origins
        'OLLAMA_KEEP_ALIVE': '10m',  # Keep models loaded longer
        'OLLAMA_MAX_LOADED_MODELS': '3',  # Allow multiple models
        'OLLAMA_FLASH_ATTENTION': '1',  # Enable flash attention
        'OLLAMA_USE_MLOCK': '1',  # Lock memory
    }
    
    print("🔧 Setting GPU optimization environment variables...")
    for key, value in gpu_env.items():
        os.environ[key] = value
        print(f"   {key}={value}")
    
    # Create Windows batch file for persistent settings
    batch_content = """@echo off
echo Setting Ollama GPU optimization environment variables...
set OLLAMA_NUM_GPU=-1
set OLLAMA_GPU_LAYERS=50
set OLLAMA_MAIN_GPU=0
set OLLAMA_TENSOR_SPLIT=
set OLLAMA_HOST=0.0.0.0:11434
set OLLAMA_ORIGINS=*
set OLLAMA_KEEP_ALIVE=10m
set OLLAMA_MAX_LOADED_MODELS=3
set OLLAMA_FLASH_ATTENTION=1
set OLLAMA_USE_MLOCK=1

echo Starting optimized Ollama server...
ollama serve
"""
    
    with open("start_ollama_gpu.bat", "w") as f:
        f.write(batch_content)
    
    print("✅ Created start_ollama_gpu.bat for optimized startup")
    return gpu_env

def check_model_performance():
    """Check current model performance and suggest optimizations"""
    try:
        # Get running models
        response = requests.get("http://localhost:11434/api/ps", timeout=5)
        if response.status_code == 200:
            data = response.json()
            models = data.get('models', [])
            
            if not models:
                print("📊 No models currently running")
                return
            
            print("\n📊 Current model performance:")
            print("-" * 50)
            
            for model in models:
                name = model.get('name', 'Unknown')
                size = model.get('size', 0)
                processor = model.get('processor', 'Unknown')
                
                print(f"Model: {name}")
                print(f"Size: {size / 1e9:.1f}GB")
                print(f"Processor: {processor}")
                
                # Analyze performance
                if 'GPU' in processor:
                    gpu_usage = processor.split('/')[-1].replace('GPU', '').strip()
                    print(f"🚀 GPU Usage: {gpu_usage}")
                    
                    if int(gpu_usage.replace('%', '')) < 80:
                        print("💡 Suggestion: Increase GPU layers for better performance")
                else:
                    print("⚠️ Running on CPU only - check GPU settings")
                
                print("-" * 30)
        
    except Exception as e:
        print(f"❌ Cannot check model performance: {e}")

def recommend_model():
    """Recommend best model based on system specs"""
    try:
        # Get available models
        response = requests.get("http://localhost:11434/api/tags", timeout=5)
        if response.status_code == 200:
            data = response.json()
            models = [m['name'] for m in data.get('models', [])]
            
            vision_models = [m for m in models if any(x in m for x in ['llava', 'minicpm-v', 'qwen2.5vl'])]
            
            print("\n🎯 Model recommendations based on your system:")
            print("-" * 50)
            
            # Check GPU type and memory
            gpu_type = check_gpu_availability()
            
            if gpu_type == "nvidia":
                print("🚀 NVIDIA GPU detected - you can use larger models:")
                print("   • qwen2.5vl:32b - Best quality (requires 16GB+ VRAM)")
                print("   • llava:13b - Good balance (requires 8GB+ VRAM)")
                print("   • minicpm-v:8b - Fast option (requires 6GB+ VRAM)")
            else:
                print("⚡ CPU/Limited GPU - recommend smaller models:")
                print("   • llava:7b - Fastest (requires 4GB+ RAM)")
                print("   • minicpm-v:8b - Good balance (requires 6GB+ RAM)")
            
            print("\n📋 Available vision models on your system:")
            for i, model in enumerate(vision_models, 1):
                print(f"   {i}. {model}")
    
    except Exception as e:
        print(f"❌ Cannot get model recommendations: {e}")

def main():
    print("🚀 OLLAMA GPU OPTIMIZATION")
    print("=" * 50)
    
    # Check GPU availability
    gpu_type = check_gpu_availability()
    
    # Optimize settings
    gpu_env = optimize_ollama_gpu()
    
    # Check if Ollama is running
    try:
        response = requests.get("http://localhost:11434/api/tags", timeout=5)
        if response.status_code == 200:
            print("\n✅ Ollama server is running")
            check_model_performance()
            recommend_model()
        else:
            print("\n❌ Ollama server not responding")
            print("💡 Start with: ollama serve")
            print("💡 Or use: start_ollama_gpu.bat")
    except:
        print("\n❌ Cannot connect to Ollama server")
        print("💡 Start optimized server with: start_ollama_gpu.bat")
    
    print("\n🎯 OPTIMIZATION COMPLETE!")
    print("=" * 50)
    print("✅ GPU environment variables set")
    print("✅ Batch file created: start_ollama_gpu.bat")
    print("✅ Model recommendations provided")
    print("\n🚀 To use optimized Claude-style agent:")
    print("   python agent_s_claude_style.py")

if __name__ == "__main__":
    main() 