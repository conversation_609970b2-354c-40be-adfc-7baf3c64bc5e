#!/usr/bin/env python3
"""
Smart Agent-S with Adaptive Automation
Uses intelligent strategies instead of hardcoded coordinates
"""

import os
import pyautogui
import io
import time
from PIL import Image
import requests
import base64
import json
import subprocess
import psutil

# Configuration
OLLAMA_BASE_URL = "http://localhost:11434"
OLLAMA_VISION_URL = f"{OLLAMA_BASE_URL}/api/chat"

def take_screenshot():
    """Take and encode screenshot for AI analysis"""
    screenshot = pyautogui.screenshot()
    screenshot = screenshot.resize((1280, 720), Image.LANCZOS)
    
    buffered = io.BytesIO()
    screenshot.save(buffered, format="JPEG", quality=90)
    img_bytes = buffered.getvalue()
    img_base64 = base64.b64encode(img_bytes).decode('utf-8')
    
    return img_base64

def smart_open_application(app_name):
    """Smart way to open applications using multiple methods"""
    print(f"🚀 Opening {app_name} using smart methods...")
    
    # Method 1: Try Windows key + type (most reliable)
    try:
        pyautogui.press('win')
        time.sleep(1)
        pyautogui.type(app_name)
        time.sleep(1)
        pyautogui.press('enter')
        time.sleep(3)
        print(f"✅ Opened {app_name} via Windows key")
        return True
    except Exception as e:
        print(f"❌ Windows key method failed: {e}")
    
    # Method 2: Try start menu click
    try:
        screen_width, screen_height = pyautogui.size()
        start_button_x = 50
        start_button_y = screen_height - 50
        
        pyautogui.click(start_button_x, start_button_y)
        time.sleep(1)
        pyautogui.type(app_name)
        time.sleep(1)
        pyautogui.press('enter')
        time.sleep(3)
        print(f"✅ Opened {app_name} via Start menu")
        return True
    except Exception as e:
        print(f"❌ Start menu method failed: {e}")
    
    # Method 3: Try Run dialog
    try:
        pyautogui.hotkey('win', 'r')
        time.sleep(1)
        pyautogui.type(app_name)
        time.sleep(1)
        pyautogui.press('enter')
        time.sleep(3)
        print(f"✅ Opened {app_name} via Run dialog")
        return True
    except Exception as e:
        print(f"❌ Run dialog method failed: {e}")
    
    return False

def smart_automation_action(action_type, **kwargs):
    """Smart automation actions that adapt to different scenarios"""
    
    if action_type == "open_app":
        app_name = kwargs.get("app_name", "notepad")
        return smart_open_application(app_name)
    
    elif action_type == "type_text":
        text = kwargs.get("text", "")
        pyautogui.type(text)
        return True
    
    elif action_type == "take_screenshot":
        screenshot = pyautogui.screenshot()
        filename = f"screenshot_{int(time.time())}.png"
        screenshot.save(filename)
        print(f"✅ Screenshot saved as {filename}")
        return True
    
    elif action_type == "close_window":
        pyautogui.hotkey('alt', 'f4')
        return True
    
    elif action_type == "save_file":
        pyautogui.hotkey('ctrl', 's')
        time.sleep(1)
        filename = kwargs.get("filename", "document")
        pyautogui.type(filename)
        time.sleep(1)
        pyautogui.press('enter')
        return True
    
    elif action_type == "calculator":
        expression = kwargs.get("expression", "2+2")
        if smart_open_application("calculator"):
            time.sleep(2)
            # Type the expression
            for char in expression:
                if char.isdigit():
                    pyautogui.type(char)
                elif char == '+':
                    pyautogui.type('+')
                elif char == '-':
                    pyautogui.type('-')
                elif char == '*':
                    pyautogui.type('*')
                elif char == '/':
                    pyautogui.type('/')
                time.sleep(0.2)
            pyautogui.press('enter')
            return True
    
    return False

def call_ollama_smart(model, instruction, image_base64):
    """Smart AI that chooses actions instead of generating code"""
    
    prompt = f"""You are Claude, an AI assistant that can control computers intelligently.

TASK: {instruction}

Look at this screenshot and determine the BEST ACTION to accomplish this task.

Instead of generating PyAutoGUI code, choose from these SMART ACTIONS:

1. open_app: {{app_name: "notepad"|"calculator"|"browser"|"word"|"excel"}}
2. type_text: {{text: "content to type"}}
3. take_screenshot: {{}}
4. close_window: {{}}
5. save_file: {{filename: "name"}}
6. calculator: {{expression: "15*25"}}

RESPOND ONLY WITH JSON in this format:
{{
  "action": "action_name",
  "parameters": {{...}},
  "explanation": "Brief explanation of what this will do"
}}

EXAMPLES:

For "open notepad and write shopping list":
{{
  "action": "open_app",
  "parameters": {{"app_name": "notepad"}},
  "explanation": "Opening Notepad application"
}}

For "calculate 15 * 25":
{{
  "action": "calculator", 
  "parameters": {{"expression": "15*25"}},
  "explanation": "Opening calculator and computing 15 × 25"
}}

For "take a screenshot":
{{
  "action": "take_screenshot",
  "parameters": {{}},
  "explanation": "Capturing current screen"
}}

BE SMART: Choose the most reliable action for the task."""
    
    payload = {
        "model": model,
        "messages": [
            {
                "role": "user", 
                "content": prompt,
                "images": [image_base64]
            }
        ],
        "stream": False,
        "options": {
            "temperature": 0.1,
            "top_p": 0.9,
            "num_gpu": -1,
            "gpu_layers": 50,
            "num_thread": 8,
        }
    }
    
    try:
        response = requests.post(OLLAMA_VISION_URL, json=payload, timeout=60)
        if response.status_code == 200:
            result = response.json()
            return result['message']['content']
        else:
            return f"API Error: {response.status_code}"
    except Exception as e:
        return f"Error: {e}"

def parse_ai_response(response):
    """Parse AI response and extract action"""
    try:
        # Find JSON in response
        if '{' in response and '}' in response:
            start = response.find('{')
            end = response.rfind('}') + 1
            json_str = response[start:end]
            return json.loads(json_str)
    except:
        pass
    
    # Fallback parsing
    if "open_app" in response.lower():
        if "notepad" in response.lower():
            return {"action": "open_app", "parameters": {"app_name": "notepad"}}
        elif "calculator" in response.lower():
            return {"action": "calculator", "parameters": {"expression": "2+2"}}
    
    return None

def main():
    print("🧠 SMART AGENT-S - NO MORE HARDCODED COORDINATES!")
    print("=" * 60)
    
    # Test Ollama connection
    try:
        response = requests.get(f"{OLLAMA_BASE_URL}/api/tags", timeout=5)
        if response.status_code != 200:
            print("❌ Ollama not running. Start with: ollama serve")
            return
    except:
        print("❌ Cannot connect to Ollama. Start with: ollama serve")
        return
    
    # Get best available model
    try:
        response = requests.get(f"{OLLAMA_BASE_URL}/api/tags", timeout=5)
        models_data = response.json()
        available_models = [m['name'] for m in models_data['models']]
        vision_models = [m for m in available_models if any(x in m for x in ['llava', 'minicpm-v', 'qwen2.5vl'])]
        
        # Auto-select best model
        if 'llava:13b' in vision_models:
            selected_model = 'llava:13b'
        elif 'minicpm-v:8b' in vision_models:
            selected_model = 'minicpm-v:8b'
        else:
            selected_model = vision_models[0] if vision_models else 'llava:7b'
            
    except:
        selected_model = 'llava:13b'
    
    print(f"🤖 Using model: {selected_model}")
    print("✨ This agent uses SMART ACTIONS instead of hardcoded coordinates!")
    print("💡 It can open apps, type text, take screenshots, and more!")
    
    # Interactive loop
    while True:
        print("\n" + "🎯 " + "="*50)
        print("What would you like me to do?")
        print("💡 Examples:")
        print("  • 'Open notepad and write a shopping list'")
        print("  • 'Calculate 123 * 456'")
        print("  • 'Take a screenshot'")
        print("  • 'Open calculator and compute 15 + 25'")
        print("  • 'quit' to exit")
        
        instruction = input("\n🗣️  You: ").strip()
        
        if instruction.lower() in ['quit', 'exit', 'q']:
            print("\n👋 Goodbye! Smart automation session ended.")
            break
        
        if not instruction:
            continue
        
        print(f"\n🤖 Claude: Let me handle that smartly...")
        
        # Take screenshot for context
        try:
            image_base64 = take_screenshot()
            print("📸 Screenshot captured for AI analysis")
        except Exception as e:
            print(f"❌ Screenshot failed: {e}")
            continue
        
        # Get AI decision
        print("🧠 AI is choosing the best action...")
        try:
            response = call_ollama_smart(selected_model, instruction, image_base64)
            action_data = parse_ai_response(response)
            
            if not action_data:
                print("❌ AI couldn't determine the right action")
                print(f"AI Response: {response[:200]}...")
                continue
            
            action = action_data.get("action")
            parameters = action_data.get("parameters", {})
            explanation = action_data.get("explanation", "Performing action")
            
            print(f"\n✨ Smart Action: {action}")
            print(f"📝 Plan: {explanation}")
            print(f"⚙️  Parameters: {parameters}")
            
            # Execute smart action
            confirm = input("\n⚡ Execute this smart action? (y/n): ").strip().lower()
            if confirm == 'y':
                print("\n🚀 Executing smart action...")
                success = smart_automation_action(action, **parameters)
                if success:
                    print("✅ Smart action completed successfully!")
                    
                    # Handle follow-up actions
                    if action == "open_app" and parameters.get("app_name") == "notepad":
                        if "shopping list" in instruction.lower():
                            time.sleep(2)
                            smart_automation_action("type_text", text="Shopping List:\n• Apples\n• Bananas\n• Bread\n• Milk\n• Eggs")
                            print("📝 Shopping list written!")
                        elif "write" in instruction.lower():
                            time.sleep(2)
                            smart_automation_action("type_text", text="Hello! This was created by Smart Agent-S!")
                            print("📝 Text written!")
                else:
                    print("❌ Smart action failed")
            else:
                print("⏭️ Action skipped")
                
        except Exception as e:
            print(f"❌ Smart action failed: {e}")

if __name__ == "__main__":
    main() 