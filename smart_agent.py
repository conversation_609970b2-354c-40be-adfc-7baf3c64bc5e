#!/usr/bin/env python3
"""
Smart Agent-S - Clean, Working AI Automation
Actually understands and adapts intelligently
"""

import os
import sys
import time
import json
import logging
from datetime import datetime
from pathlib import Path
import base64
import io

# Core automation
import pyautogui
from PIL import Image
import cv2
import numpy as np

# AI imports with fallbacks
try:
    import openai
    HAS_OPENAI = True
except ImportError:
    HAS_OPENAI = False

# Configure
pyautogui.PAUSE = 0.1
pyautogui.FAILSAFE = True
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class SmartVision:
    """AI-powered screen understanding with multiple providers and robust error handling"""

    def __init__(self):
        self.ai_client = None
        self.providers = {}
        self.current_provider = None
        self._init_ai()

    def _init_ai(self):
        """Initialize AI providers with fallbacks"""
        # Try OpenAI first
        if HAS_OPENAI and os.getenv('OPENAI_API_KEY'):
            try:
                self.ai_client = openai.OpenAI(api_key=os.getenv('OPENAI_API_KEY'))
                self.providers['openai'] = {
                    'client': self.ai_client,
                    'model': 'gpt-4o',
                    'available': True
                }
                self.current_provider = 'openai'
                logger.info("✅ AI Vision enabled with GPT-4o")
            except Exception as e:
                logger.warning(f"OpenAI init failed: {e}")

        # Add Anthropic as fallback if available
        try:
            import anthropic
            if os.getenv('ANTHROPIC_API_KEY'):
                anthropic_client = anthropic.Anthropic(api_key=os.getenv('ANTHROPIC_API_KEY'))
                self.providers['anthropic'] = {
                    'client': anthropic_client,
                    'model': 'claude-3-5-sonnet-20241022',
                    'available': True
                }
                if not self.current_provider:
                    self.current_provider = 'anthropic'
                    logger.info("✅ AI Vision enabled with Claude")
        except ImportError:
            pass
        except Exception as e:
            logger.warning(f"Anthropic init failed: {e}")

        if not self.providers:
            logger.warning("⚠️ No AI providers available - using basic vision")
    
    def analyze_screen(self, task: str, max_retries: int = 2):
        """Analyze current screen for the task with robust error handling"""
        for attempt in range(max_retries + 1):
            try:
                screenshot = pyautogui.screenshot()

                if self.providers:
                    return self._ai_analyze_robust(screenshot, task, attempt)
                else:
                    return self._basic_analyze(screenshot, task)

            except Exception as e:
                logger.error(f"Screen analysis attempt {attempt + 1} failed: {e}")
                if attempt == max_retries:
                    return {
                        "description": "Analysis failed after retries",
                        "actions": ["basic_fallback"],
                        "errors": [str(e)],
                        "current_state": "error"
                    }
                time.sleep(1)  # Brief pause before retry
    
    def _ai_analyze_robust(self, screenshot, task, attempt=0):
        """Robust AI analysis with multiple providers and fallbacks"""
        # Convert to base64 once
        buffer = io.BytesIO()
        screenshot.save(buffer, format='PNG')
        image_base64 = base64.b64encode(buffer.getvalue()).decode()

        # Try current provider first, then fallbacks
        providers_to_try = [self.current_provider] if self.current_provider else []
        providers_to_try.extend([p for p in self.providers.keys() if p != self.current_provider])

        for provider in providers_to_try:
            if not self.providers[provider]['available']:
                continue

            try:
                if provider == 'openai':
                    return self._analyze_with_openai(image_base64, task)
                elif provider == 'anthropic':
                    return self._analyze_with_anthropic(image_base64, task)
            except Exception as e:
                logger.warning(f"{provider} analysis failed: {e}")
                # Mark provider as temporarily unavailable if multiple failures
                if attempt > 0:
                    self.providers[provider]['available'] = False
                continue

        # All AI providers failed, use basic analysis
        logger.warning("All AI providers failed, using basic analysis")
        return self._basic_analyze(screenshot, task)

    def _analyze_with_openai(self, image_base64, task):
        """Analyze with OpenAI GPT-4o"""
        prompt = f"""
        Analyze this screen for the task: "{task}"

        IMPORTANT: Distinguish between applications:
        - Web browsers (Chrome, Firefox, Edge) have address bars, tabs, navigation buttons
        - Code editors (Cursor, VS Code) have file explorers, code syntax highlighting
        - Other apps have their specific interfaces

        Respond with JSON:
        {{
            "description": "What's on screen - be specific about the application type",
            "current_state": "desktop/browser_open/code_editor_open/loading/ready/etc",
            "actions": ["specific actions to take"],
            "errors": ["any blocking issues like wrong app opened"],
            "coordinates": {{"element_name": [x, y]}},
            "next_step": "best immediate action",
            "confidence": 0.9,
            "app_detected": "browser/code_editor/desktop/other"
        }}

        If a code editor opened instead of browser, mark it as an error.
        Be specific about coordinates and actionable steps.
        """

        response = self.providers['openai']['client'].chat.completions.create(
            model=self.providers['openai']['model'],
            messages=[{
                "role": "user",
                "content": [
                    {"type": "text", "text": prompt},
                    {"type": "image_url", "image_url": {"url": f"data:image/png;base64,{image_base64}", "detail": "high"}}
                ]
            }],
            max_tokens=1000,
            temperature=0.1
        )

        analysis_text = response.choices[0].message.content
        return self._parse_ai_response(analysis_text)

    def _analyze_with_anthropic(self, image_base64, task):
        """Analyze with Anthropic Claude"""
        prompt = f"""
        Analyze this screen for the task: "{task}"

        Respond with JSON:
        {{
            "description": "What's on screen",
            "current_state": "ready/loading/error/account_selection/browser_open/desktop/etc",
            "actions": ["specific actions to take"],
            "errors": ["any blocking issues"],
            "coordinates": {{"element_name": [x, y]}},
            "next_step": "best immediate action",
            "confidence": 0.9
        }}

        Be specific about coordinates and actionable steps.
        """

        response = self.providers['anthropic']['client'].messages.create(
            model=self.providers['anthropic']['model'],
            max_tokens=1000,
            messages=[{
                "role": "user",
                "content": [
                    {"type": "text", "text": prompt},
                    {"type": "image", "source": {"type": "base64", "media_type": "image/png", "data": image_base64}}
                ]
            }]
        )

        analysis_text = response.content[0].text
        return self._parse_ai_response(analysis_text)

    def _parse_ai_response(self, analysis_text):
        """Parse AI response with robust error handling"""
        try:
            # Try to parse JSON directly
            return json.loads(analysis_text)
        except json.JSONDecodeError:
            # Try to extract JSON from text
            import re
            json_match = re.search(r'\{.*\}', analysis_text, re.DOTALL)
            if json_match:
                try:
                    return json.loads(json_match.group())
                except json.JSONDecodeError:
                    pass

            # Fallback to text parsing
            return self._parse_text_analysis(analysis_text)
    
    def _basic_analyze(self, screenshot, task):
        """Basic analysis without AI"""
        screen_width, screen_height = screenshot.size
        
        analysis = {
            "description": "Basic screen analysis",
            "current_state": "unknown",
            "actions": [],
            "errors": [],
            "coordinates": {},
            "next_step": ""
        }
        
        # Simple task-based analysis
        task_lower = task.lower()
        
        if "browser" in task_lower or "search" in task_lower:
            analysis["actions"] = ["open_browser", "search"]
            analysis["next_step"] = "open_browser"
        elif "calculator" in task_lower:
            analysis["actions"] = ["open_calculator"]
            analysis["next_step"] = "open_calculator"
        elif "screenshot" in task_lower:
            analysis["actions"] = ["take_screenshot"]
            analysis["next_step"] = "take_screenshot"
        
        return analysis
    
    def _parse_text_analysis(self, text):
        """Parse AI text when JSON fails"""
        analysis = {
            "description": text[:100] + "..." if len(text) > 100 else text,
            "current_state": "unknown",
            "actions": [],
            "errors": [],
            "coordinates": {},
            "next_step": ""
        }
        
        text_lower = text.lower()
        
        # Detect states
        if "account" in text_lower and "select" in text_lower:
            analysis["current_state"] = "account_selection"
            analysis["errors"] = ["account_selection_blocking"]
            analysis["actions"] = ["open_incognito", "skip_account"]
        elif "loading" in text_lower:
            analysis["current_state"] = "loading"
            analysis["actions"] = ["wait"]
        elif "search" in text_lower:
            analysis["current_state"] = "search_ready"
            analysis["actions"] = ["type_search", "click_search"]
        
        return analysis

class SmartExecutor:
    """Intelligent action execution with adaptation and robust error handling"""

    def __init__(self, vision):
        self.vision = vision
        self.execution_history = []
        self.strategies = {
            "open_browser": self._open_browser_smart,
            "search": self._search_smart,
            "open_calculator": self._open_calculator,
            "take_screenshot": self._take_screenshot,
            "handle_account_selection": self._handle_account_selection,
            "basic_fallback": self._basic_fallback,
            "retry": self._retry_last_action,
            "wait": self._wait_action
        }
    
    def execute_action(self, action, context=None):
        """Execute action with smart strategies and history tracking"""
        logger.info(f"🎯 Executing: {action}")

        # Record action in history
        action_record = {
            "action": action,
            "context": context,
            "timestamp": time.time()
        }

        try:
            if action in self.strategies:
                result = self.strategies[action](context)
            else:
                result = self._generic_action(action, context)

            # Record result
            action_record["result"] = result
            action_record["success"] = result.get("success", False)
            self.execution_history.append(action_record)

            return result

        except Exception as e:
            logger.error(f"Action failed: {e}")
            result = {"success": False, "error": str(e)}
            action_record["result"] = result
            action_record["success"] = False
            self.execution_history.append(action_record)
            return result

    def _basic_fallback(self, context):
        """Basic fallback when AI analysis fails"""
        try:
            task = context.get("original_task", "") if context else ""
            task_lower = task.lower()

            if "browser" in task_lower or "search" in task_lower:
                return self._open_browser_smart(context)
            elif "calculator" in task_lower:
                return self._open_calculator(context)
            elif "screenshot" in task_lower:
                return self._take_screenshot(context)
            else:
                return {"success": True, "action": "basic_fallback_completed", "message": "Used basic task interpretation"}
        except Exception as e:
            return {"success": False, "error": str(e)}

    def _retry_last_action(self, context):
        """Retry the last failed action"""
        if not self.execution_history:
            return {"success": False, "error": "No previous action to retry"}

        last_action = self.execution_history[-1]
        if last_action["success"]:
            return {"success": True, "action": "retry_not_needed", "message": "Last action was successful"}

        # Retry the action
        return self.execute_action(last_action["action"], last_action["context"])

    def _wait_action(self, context):
        """Wait for a specified time or default"""
        wait_time = context.get("wait_time", 3) if context else 3
        time.sleep(wait_time)
        return {"success": True, "action": "wait_completed", "wait_time": wait_time}

    def _generic_action(self, action, context):
        """Handle generic actions that don't have specific strategies"""
        try:
            logger.info(f"🔧 Handling generic action: {action}")

            # Try to interpret the action
            action_lower = action.lower()

            if "click" in action_lower:
                # Try to extract coordinates or use center screen
                return self._generic_click(action, context)
            elif "type" in action_lower or "write" in action_lower:
                return self._generic_type(action, context)
            elif "press" in action_lower or "key" in action_lower:
                return self._generic_keypress(action, context)
            elif "open" in action_lower:
                if "browser" in action_lower:
                    return self._open_browser_smart(context)
                elif "calculator" in action_lower:
                    return self._open_calculator(context)
                else:
                    return self._generic_open(action, context)
            else:
                # Default fallback
                return {"success": True, "action": "generic_action_interpreted", "message": f"Interpreted action: {action}"}

        except Exception as e:
            return {"success": False, "error": str(e)}

    def _generic_click(self, action, context):
        """Handle generic click actions"""
        try:
            # Try to extract coordinates from context
            if context and "coordinates" in context:
                coords = context["coordinates"]
                if coords and len(coords) >= 2:
                    pyautogui.click(coords[0], coords[1])
                    return {"success": True, "action": "click_executed", "coordinates": coords}

            # Fallback: click center of screen
            screen_width, screen_height = pyautogui.size()
            center_x, center_y = screen_width // 2, screen_height // 2
            pyautogui.click(center_x, center_y)
            return {"success": True, "action": "click_center", "coordinates": [center_x, center_y]}

        except Exception as e:
            return {"success": False, "error": str(e)}

    def _generic_type(self, action, context):
        """Handle generic typing actions"""
        try:
            # Extract text to type
            text_to_type = ""
            if context and "text" in context:
                text_to_type = context["text"]
            elif context and "search_query" in context:
                text_to_type = context["search_query"]
            else:
                # Try to extract from action
                words = action.split()
                if len(words) > 1:
                    text_to_type = " ".join(words[1:])

            if text_to_type:
                pyautogui.write(text_to_type)
                return {"success": True, "action": "text_typed", "text": text_to_type}
            else:
                return {"success": False, "error": "No text to type"}

        except Exception as e:
            return {"success": False, "error": str(e)}

    def _generic_keypress(self, action, context):
        """Handle generic key press actions"""
        try:
            # Extract key from action
            action_lower = action.lower()
            if "enter" in action_lower:
                pyautogui.press('enter')
                return {"success": True, "action": "key_pressed", "key": "enter"}
            elif "escape" in action_lower or "esc" in action_lower:
                pyautogui.press('esc')
                return {"success": True, "action": "key_pressed", "key": "escape"}
            elif "tab" in action_lower:
                pyautogui.press('tab')
                return {"success": True, "action": "key_pressed", "key": "tab"}
            else:
                return {"success": True, "action": "keypress_interpreted", "message": f"Key press action: {action}"}

        except Exception as e:
            return {"success": False, "error": str(e)}

    def _generic_open(self, action, context):
        """Handle generic open actions"""
        try:
            # Try to open using Windows search
            pyautogui.press('win')
            time.sleep(1)

            # Extract application name
            words = action.lower().split()
            app_name = ""
            for word in words:
                if word not in ["open", "launch", "start", "run"]:
                    app_name = word
                    break

            if app_name:
                pyautogui.write(app_name)
                time.sleep(1)
                pyautogui.press('enter')
                time.sleep(2)
                return {"success": True, "action": "app_opened", "app": app_name}
            else:
                return {"success": False, "error": "Could not determine application to open"}

        except Exception as e:
            return {"success": False, "error": str(e)}
    
    def _open_browser_smart(self, context):
        """Smart browser opening with multiple strategies"""
        try:
            logger.info("🌐 Opening web browser...")

            # Strategy 1: Try to open browser directly via Windows search
            pyautogui.press('win')
            time.sleep(1)

            # Try multiple browser names in order of preference
            browsers = ['chrome', 'firefox', 'edge', 'browser']

            for browser in browsers:
                try:
                    logger.info(f"🔍 Trying to open {browser}...")
                    pyautogui.write(browser)
                    time.sleep(1)
                    pyautogui.press('enter')
                    time.sleep(3)

                    # Verify a browser window opened (not cursor or other app)
                    analysis = self.vision.analyze_screen("check if browser opened")
                    if "browser" in str(analysis).lower() or "chrome" in str(analysis).lower() or "firefox" in str(analysis).lower():
                        logger.info(f"✅ Successfully opened {browser}")
                        return {"success": True, "action": "browser_opened", "browser": browser}
                    else:
                        # Close whatever opened and try next browser
                        pyautogui.hotkey('alt', 'f4')
                        time.sleep(1)
                        pyautogui.press('win')
                        time.sleep(1)

                except Exception as e:
                    logger.warning(f"Failed to open {browser}: {e}")
                    continue

            # Strategy 2: Try keyboard shortcut for existing browser
            logger.info("🔄 Trying browser keyboard shortcuts...")
            pyautogui.hotkey('ctrl', 'shift', 'n')  # Chrome incognito
            time.sleep(2)

            # Strategy 3: Try opening via Run dialog
            logger.info("🔄 Trying Run dialog...")
            pyautogui.hotkey('win', 'r')
            time.sleep(1)
            pyautogui.write('chrome.exe')
            pyautogui.press('enter')
            time.sleep(3)

            return {"success": True, "action": "browser_opened", "method": "fallback"}

        except Exception as e:
            return {"success": False, "error": str(e)}
    
    def _search_smart(self, context):
        """Smart search execution"""
        try:
            search_query = context.get("search_query", "bitcoin financial insights")
            
            # Multiple search strategies
            strategies = [
                lambda: self._direct_url_search(search_query),
                lambda: self._address_bar_search(search_query),
                lambda: self._new_tab_search(search_query)
            ]
            
            for strategy in strategies:
                try:
                    if strategy():
                        return {"success": True, "action": "search_completed", "query": search_query}
                except Exception:
                    continue
            
            return {"success": False, "error": "All search strategies failed"}
            
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    def _direct_url_search(self, query):
        """Search using direct Google URL"""
        pyautogui.hotkey('ctrl', 'l')
        time.sleep(1)
        search_url = f"https://www.google.com/search?q={query.replace(' ', '+')}"
        pyautogui.write(search_url)
        pyautogui.press('enter')
        time.sleep(3)
        return True
    
    def _address_bar_search(self, query):
        """Search using address bar"""
        pyautogui.hotkey('ctrl', 'l')
        time.sleep(1)
        pyautogui.write(query)
        pyautogui.press('enter')
        time.sleep(3)
        return True
    
    def _new_tab_search(self, query):
        """Search in new tab"""
        pyautogui.hotkey('ctrl', 't')
        time.sleep(2)
        pyautogui.write(query)
        pyautogui.press('enter')
        time.sleep(3)
        return True
    
    def _open_calculator(self, context):
        """Open calculator"""
        try:
            pyautogui.press('win')
            time.sleep(1)
            pyautogui.write('calculator')
            time.sleep(1)
            pyautogui.press('enter')
            time.sleep(2)
            return {"success": True, "action": "calculator_opened"}
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    def _take_screenshot(self, context):
        """Take screenshot"""
        try:
            screenshot = pyautogui.screenshot()
            filename = f"screenshot_{int(time.time())}.png"
            screenshot.save(filename)
            return {"success": True, "action": "screenshot_taken", "filename": filename}
        except Exception as e:
            return {"success": False, "error": str(e)}

    def _handle_account_selection(self, context):
        """Handle account selection screens"""
        try:
            logger.info("🔧 Handling account selection...")
            # Try multiple approaches
            pyautogui.press('esc')
            time.sleep(1)
            pyautogui.hotkey('ctrl', 'shift', 'n')  # Force incognito
            time.sleep(2)
            return {"success": True, "action": "account_selection_handled"}
        except Exception as e:
            return {"success": False, "error": str(e)}

class SmartAgent:
    """Main Smart Agent with intelligence, adaptation, and interactive assistance"""

    def __init__(self):
        logger.info("🚀 Initializing Smart Agent...")

        self.vision = SmartVision()
        self.executor = SmartExecutor(self.vision)

        # Enhanced stats and tracking
        self.stats = {
            "tasks_completed": 0,
            "success_rate": 0.0,
            "adaptations_made": 0,
            "user_assists": 0,
            "stuck_count": 0
        }

        # Task tracking
        self.current_task = None
        self.task_steps = []
        self.stuck_threshold = 3  # Number of failed attempts before asking for help
        self.last_action_time = time.time()
        self.max_task_time = 60  # Max time for a task before asking for help

        logger.info("✅ Smart Agent ready!")
        self._print_capabilities()

    def _print_capabilities(self):
        """Print capabilities"""
        has_ai = bool(self.vision.ai_client)

        print("\n🧠 SMART AGENT CAPABILITIES:")
        print("="*40)
        print(f"   {'✅' if has_ai else '❌'} AI Screen Understanding")
        print(f"   ✅ Intelligent Action Execution")
        print(f"   ✅ Error Recovery & Adaptation")
        print(f"   ✅ Natural Language Processing")
        print(f"   📊 Success Rate: {self.stats['success_rate']:.1%}")

    def execute_task_interactive(self, user_request, max_attempts=3):
        """Execute task with interactive assistance and stuck detection"""
        start_time = time.time()
        task_id = f"task_{int(time.time())}"
        self.current_task = user_request
        self.task_steps = []
        self.last_action_time = time.time()

        logger.info(f"🎯 INTERACTIVE EXECUTION: {user_request}")

        for attempt in range(max_attempts):
            try:
                # Check if we're stuck (taking too long)
                if time.time() - start_time > self.max_task_time:
                    return self._ask_for_help("Task taking too long", user_request, start_time)

                # Step 1: Analyze current screen
                logger.info(f"🔍 Analyzing screen (attempt {attempt + 1})...")
                analysis = self.vision.analyze_screen(user_request)
                self.task_steps.append(f"Attempt {attempt + 1}: {analysis.get('description', 'Analysis')}")

                # Check if AI detected we're stuck or confused
                if self._is_stuck_analysis(analysis):
                    return self._ask_for_help("AI detected confusion", user_request, start_time, analysis)

                # Step 2: Handle any errors first
                if analysis.get("errors"):
                    logger.info("🔧 Handling errors...")
                    for error in analysis["errors"]:
                        if "account_selection" in error:
                            result = self.executor.execute_action("handle_account_selection", {"original_task": user_request})
                            if result.get("success"):
                                self.stats["adaptations_made"] += 1

                # Step 3: Execute main actions with progress tracking
                logger.info("⚡ Executing actions...")
                results = []

                # Extract context from user request
                context = self._extract_context(user_request)
                context["original_task"] = user_request
                context["attempt"] = attempt

                # Get actions from analysis or use fallback
                actions = analysis.get("actions", ["basic_fallback"])

                for i, action in enumerate(actions):
                    logger.info(f"📋 Step {i+1}/{len(actions)}: {action}")

                    # Update progress
                    self.last_action_time = time.time()

                    result = self.executor.execute_action(action, context)
                    results.append(result)

                    self.task_steps.append(f"Action: {action} - {'✅' if result.get('success') else '❌'}")

                    # If action failed multiple times, ask for help
                    if not result.get("success", False):
                        failed_count = sum(1 for r in results if not r.get("success", False))
                        if failed_count >= self.stuck_threshold:
                            return self._ask_for_help(f"Multiple actions failed ({failed_count})", user_request, start_time, analysis, results)

                        logger.info(f"🔄 Attempting recovery for: {action}")
                        recovery_result = self._attempt_recovery(action, context)
                        if recovery_result.get("success", False):
                            self.stats["adaptations_made"] += 1
                            results.append(recovery_result)
                            self.task_steps.append(f"Recovery: {action} - ✅")
                            break  # Success, continue with task
                        else:
                            self.task_steps.append(f"Recovery: {action} - ❌")

                # Calculate success
                successful_actions = sum(1 for r in results if r.get("success", False))
                overall_success = successful_actions > 0

                # Check if we made progress but task isn't complete
                if overall_success and attempt < max_attempts - 1:
                    # Ask user if task is complete or needs more steps
                    if self._should_continue_task(user_request, analysis, results):
                        continue  # Keep going
                    else:
                        # Task appears complete
                        break

                # If successful or last attempt, return result
                if overall_success or attempt == max_attempts - 1:
                    # Update stats
                    self.stats["tasks_completed"] += 1
                    if overall_success:
                        success_count = self.stats["success_rate"] * (self.stats["tasks_completed"] - 1) + 1
                        self.stats["success_rate"] = success_count / self.stats["tasks_completed"]
                    else:
                        success_count = self.stats["success_rate"] * (self.stats["tasks_completed"] - 1)
                        self.stats["success_rate"] = success_count / self.stats["tasks_completed"]

                    execution_time = time.time() - start_time

                    return {
                        "success": overall_success,
                        "task_id": task_id,
                        "execution_time": execution_time,
                        "user_request": user_request,
                        "analysis": analysis,
                        "results": results,
                        "adaptations": self.stats["adaptations_made"],
                        "attempts": attempt + 1,
                        "steps": self.task_steps
                    }

                # Wait before retry
                logger.info(f"⏳ Attempt {attempt + 1} failed, retrying in 2 seconds...")
                time.sleep(2)

            except Exception as e:
                logger.error(f"❌ Attempt {attempt + 1} failed: {e}")
                if attempt == max_attempts - 1:
                    execution_time = time.time() - start_time
                    return self._ask_for_help(f"Exception: {str(e)}", user_request, start_time)
                time.sleep(2)

        # If we get here, all attempts failed
        return self._ask_for_help("All attempts failed", user_request, start_time)

    def _extract_context(self, user_request):
        """Extract context from user request"""
        context = {}

        # Extract search query
        request_lower = user_request.lower()
        if any(word in request_lower for word in ["search", "look", "find", "bitcoin"]):
            # Remove common words
            words = user_request.lower().split()
            stop_words = ["open", "browser", "search", "for", "look", "up", "find", "my"]
            search_words = [w for w in words if w not in stop_words]
            context["search_query"] = " ".join(search_words) if search_words else "bitcoin financial insights"

        return context

    def _is_stuck_analysis(self, analysis):
        """Check if AI analysis indicates we're stuck"""
        if not analysis:
            return True

        description = analysis.get("description", "").lower()
        current_state = analysis.get("current_state", "").lower()
        confidence = analysis.get("confidence", 0)

        # Signs we might be stuck
        stuck_indicators = [
            "confused", "unclear", "unknown", "error", "failed",
            "cannot", "unable", "stuck", "frozen", "unresponsive"
        ]

        return (
            confidence < 0.3 or  # Low confidence
            any(indicator in description for indicator in stuck_indicators) or
            current_state in ["error", "unknown", "stuck"] or
            len(analysis.get("actions", [])) == 0  # No actions suggested
        )

    def _should_continue_task(self, user_request, analysis, results):
        """Check if task needs more steps or is complete"""
        # Simple heuristic: if we opened browser and user wanted to search, continue
        request_lower = user_request.lower()

        if "search" in request_lower and "browser" in request_lower:
            # Check if we only opened browser but haven't searched yet
            browser_opened = any("browser" in str(r) for r in results)
            search_done = any("search" in str(r) for r in results)

            if browser_opened and not search_done:
                logger.info("🔄 Browser opened, continuing with search...")
                return True

        return False

    def _ask_for_help(self, reason, user_request, start_time, analysis=None, results=None):
        """Ask user for help when stuck"""
        self.stats["stuck_count"] += 1
        execution_time = time.time() - start_time

        print("\n" + "="*60)
        print("🆘 AGENT NEEDS ASSISTANCE")
        print("="*60)
        print(f"📋 Original Task: {user_request}")
        print(f"⚠️ Issue: {reason}")
        print(f"⏱️ Time Elapsed: {execution_time:.1f}s")

        if self.task_steps:
            print(f"\n📝 Steps Taken:")
            for i, step in enumerate(self.task_steps[-5:], 1):  # Show last 5 steps
                print(f"   {i}. {step}")

        if analysis:
            print(f"\n🔍 Current Screen Analysis:")
            print(f"   State: {analysis.get('current_state', 'Unknown')}")
            print(f"   Description: {analysis.get('description', 'No description')[:100]}...")
            print(f"   Confidence: {analysis.get('confidence', 0):.1%}")

        print(f"\n🤔 What should I do?")
        print("Options:")
        print("1. 'continue' - Keep trying the current approach")
        print("2. 'retry' - Start the task over from beginning")
        print("3. 'manual' - Tell me exactly what to do next")
        print("4. 'abort' - Give up on this task")
        print("5. 'screenshot' - Take a screenshot to see current state")

        while True:
            try:
                user_help = input("\n🆘 Your guidance: ").strip().lower()

                if user_help == 'continue':
                    print("✅ Continuing with current approach...")
                    self.stats["user_assists"] += 1
                    return self._continue_task_with_help(user_request, start_time)

                elif user_help == 'retry':
                    print("🔄 Restarting task from beginning...")
                    self.stats["user_assists"] += 1
                    return self.execute_task_interactive(user_request)

                elif user_help == 'manual':
                    return self._get_manual_instructions(user_request, start_time)

                elif user_help == 'abort':
                    print("❌ Task aborted by user")
                    return {
                        "success": False,
                        "task_id": f"aborted_{int(time.time())}",
                        "execution_time": execution_time,
                        "error": "Task aborted by user",
                        "user_aborted": True
                    }

                elif user_help == 'screenshot':
                    screenshot_result = self.executor.execute_action("take_screenshot", {})
                    if screenshot_result.get("success"):
                        print(f"📸 Screenshot saved: {screenshot_result.get('filename')}")
                    else:
                        print("❌ Failed to take screenshot")
                    continue

                else:
                    print("❌ Invalid option. Please choose: continue, retry, manual, abort, or screenshot")
                    continue

            except KeyboardInterrupt:
                print("\n❌ Task interrupted by user")
                return {
                    "success": False,
                    "task_id": f"interrupted_{int(time.time())}",
                    "execution_time": execution_time,
                    "error": "Task interrupted by user"
                }

    def _continue_task_with_help(self, user_request, start_time):
        """Continue task with user assistance"""
        try:
            # Re-analyze current situation
            analysis = self.vision.analyze_screen(user_request)
            context = self._extract_context(user_request)
            context["user_assisted"] = True

            # Try to execute next logical step
            actions = analysis.get("actions", ["basic_fallback"])
            results = []

            for action in actions:
                result = self.executor.execute_action(action, context)
                results.append(result)
                if result.get("success"):
                    break

            execution_time = time.time() - start_time
            successful_actions = sum(1 for r in results if r.get("success", False))

            return {
                "success": successful_actions > 0,
                "task_id": f"assisted_{int(time.time())}",
                "execution_time": execution_time,
                "user_request": user_request,
                "results": results,
                "user_assisted": True
            }

        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "execution_time": time.time() - start_time
            }

    def _get_manual_instructions(self, user_request, start_time):
        """Get manual step-by-step instructions from user"""
        print("\n📋 MANUAL INSTRUCTION MODE")
        print("Tell me exactly what to do step by step.")
        print("Available actions: click, type, press, open_browser, search, take_screenshot")
        print("Type 'done' when task is complete, 'help' for action examples")

        results = []
        self.stats["user_assists"] += 1

        while True:
            try:
                instruction = input("\n📝 Next step: ").strip()

                if instruction.lower() == 'done':
                    print("✅ Task marked as complete by user")
                    return {
                        "success": True,
                        "task_id": f"manual_{int(time.time())}",
                        "execution_time": time.time() - start_time,
                        "user_request": user_request,
                        "results": results,
                        "manual_completion": True
                    }

                elif instruction.lower() == 'help':
                    print("\n💡 Action Examples:")
                    print("• 'click 500 300' - Click at coordinates")
                    print("• 'type hello world' - Type text")
                    print("• 'press enter' - Press a key")
                    print("• 'open_browser' - Open web browser")
                    print("• 'search bitcoin' - Search for something")
                    print("• 'take_screenshot' - Take a screenshot")
                    continue

                elif not instruction:
                    continue

                # Parse and execute instruction
                result = self._execute_manual_instruction(instruction)
                results.append(result)

                if result.get("success"):
                    print(f"✅ {result.get('action', 'Action')} completed")
                else:
                    print(f"❌ Failed: {result.get('error', 'Unknown error')}")

            except KeyboardInterrupt:
                print("\n❌ Manual mode interrupted")
                return {
                    "success": False,
                    "task_id": f"manual_interrupted_{int(time.time())}",
                    "execution_time": time.time() - start_time,
                    "error": "Manual mode interrupted"
                }

    def _execute_manual_instruction(self, instruction):
        """Execute a manual instruction from user"""
        try:
            parts = instruction.split()
            if not parts:
                return {"success": False, "error": "Empty instruction"}

            command = parts[0].lower()

            if command == "click" and len(parts) >= 3:
                x, y = int(parts[1]), int(parts[2])
                pyautogui.click(x, y)
                return {"success": True, "action": f"click_{x}_{y}"}

            elif command == "type" and len(parts) > 1:
                text = " ".join(parts[1:])
                pyautogui.write(text)
                return {"success": True, "action": f"type_{text}"}

            elif command == "press" and len(parts) > 1:
                key = parts[1]
                pyautogui.press(key)
                return {"success": True, "action": f"press_{key}"}

            elif command in ["open_browser", "search", "take_screenshot"]:
                context = {"search_query": " ".join(parts[1:])} if len(parts) > 1 else {}
                return self.executor.execute_action(command, context)

            else:
                return {"success": False, "error": f"Unknown command: {command}"}

        except Exception as e:
            return {"success": False, "error": str(e)}

    def _attempt_recovery(self, failed_action, context):
        """Attempt to recover from failed action"""
        try:
            logger.info(f"🔧 Attempting recovery for: {failed_action}")

            # Re-analyze screen
            analysis = self.vision.analyze_screen(f"recover from {failed_action}")

            # Try alternative actions
            if "browser" in failed_action:
                return self.executor.execute_action("open_browser", context)
            elif "search" in failed_action:
                return self.executor.execute_action("search", context)
            else:
                # Generic recovery
                time.sleep(2)
                return {"success": True, "action": "recovery_wait"}

        except Exception as e:
            return {"success": False, "error": str(e)}

def setup_environment():
    """Setup environment"""
    env_file = Path(".env")
    if env_file.exists():
        with open(env_file, 'r') as f:
            for line in f:
                line = line.strip()
                if line and not line.startswith('#') and '=' in line:
                    key, value = line.split('=', 1)
                    os.environ[key] = value

def main():
    """Main function"""
    print("🧠 SMART AGENT-S SYSTEM")
    print("="*30)
    print("Intelligent automation that actually works!")
    print()

    # Setup
    setup_environment()

    # Check AI
    has_ai = HAS_OPENAI and os.getenv('OPENAI_API_KEY')
    if has_ai:
        print("✅ AI Vision enabled")
    else:
        print("⚠️ Using basic vision (set OPENAI_API_KEY for AI)")

    try:
        # Initialize agent
        agent = SmartAgent()

        # Interactive mode
        print("\n💬 Smart Agent Ready!")
        print("Just tell me what you want to do!")
        print("Examples:")
        print("• 'open browser and search bitcoin'")
        print("• 'take a screenshot'")
        print("• 'open calculator'")
        print("• 'stats' for performance")
        print("\nType 'quit' to exit.")
        print("-" * 40)

        while True:
            try:
                user_input = input("\n🧠 You: ").strip()

                if user_input.lower() in ['quit', 'exit']:
                    print("👋 Goodbye!")
                    break

                if user_input.lower() in ['stats', 'statistics']:
                    print(f"\n📊 SMART AGENT STATS:")
                    print(f"   Tasks: {agent.stats['tasks_completed']}")
                    print(f"   Success Rate: {agent.stats['success_rate']:.1%}")
                    print(f"   Adaptations: {agent.stats['adaptations_made']}")
                    print(f"   User Assists: {agent.stats['user_assists']}")
                    print(f"   Times Stuck: {agent.stats['stuck_count']}")
                    if agent.current_task:
                        print(f"   Current Task: {agent.current_task}")
                    continue

                if not user_input:
                    continue

                # Execute task with interactive assistance
                result = agent.execute_task_interactive(user_input)

                # Show results
                if result['success']:
                    print(f"✅ SUCCESS! Completed in {result['execution_time']:.2f}s")
                    if result.get('adaptations', 0) > 0:
                        print(f"🔧 Made {result['adaptations']} adaptations")
                    if result.get('user_assisted'):
                        print(f"🤝 Completed with user assistance")
                    if result.get('manual_completion'):
                        print(f"📝 Completed via manual instructions")
                    if result.get('steps'):
                        print(f"📋 Total steps: {len(result['steps'])}")
                else:
                    print(f"❌ Failed: {result.get('error', 'Unknown error')}")
                    print(f"⏱️ Time: {result['execution_time']:.2f}s")
                    if result.get('user_aborted'):
                        print("🛑 Task was aborted by user")
                    elif result.get('steps'):
                        print(f"📋 Steps attempted: {len(result['steps'])}")

            except KeyboardInterrupt:
                print("\n👋 Goodbye!")
                break
            except Exception as e:
                print(f"❌ Error: {e}")

    except Exception as e:
        print(f"❌ Failed to start: {e}")
        print("\nTroubleshooting:")
        print("1. Install: pip install pyautogui pillow opencv-python numpy")
        print("2. Optional AI: pip install openai")
        print("3. Set OPENAI_API_KEY for AI vision")

if __name__ == "__main__":
    main()
