#!/usr/bin/env python3
"""
Ultimate Business Agent
Combines all optimizations for maximum business productivity
"""

import pyautogui
import time
import subprocess
import os
import json
from pathlib import Path

class UltimateBusinessAgent:
    def __init__(self):
        # Ultra-speed settings
        pyautogui.PAUSE = 0.01
        pyautogui.FAILSAFE = True
        
        self.screen_width, self.screen_height = pyautogui.size()
        
        # Business-focused app commands with multiple methods
        self.business_apps = {
            'excel': ['excel', 'EXCEL.EXE', 'start excel', 'Microsoft Excel'],
            'word': ['winword', 'WINWORD.EXE', 'start winword', 'Microsoft Word'],
            'outlook': ['outlook', 'OUTLOOK.EXE', 'start outlook', 'Microsoft Outlook'],
            'powerpoint': ['powerpnt', 'POWERPNT.EXE', 'start powerpnt', 'PowerPoint'],
            'teams': ['teams', 'Teams.exe', 'start teams', 'Microsoft Teams'],
            'onenote': ['onenote', 'ONENOTE.EXE', 'start onenote', 'OneNote'],
            'calculator': ['calc', 'calculator'],
            'notepad': ['notepad', 'notepad.exe'],
            'browser': ['msedge', 'chrome', 'firefox'],
            'edge': ['msedge', 'msedge.exe'],
            'chrome': ['chrome', 'chrome.exe'],
            'firefox': ['firefox', 'firefox.exe'],
            'explorer': ['explorer', 'explorer.exe'],
            'cmd': ['cmd', 'cmd.exe'],
            'powershell': ['powershell', 'powershell.exe']
        }
        
        # Business workflows
        self.workflows = {
            'morning': ['outlook', 'teams', 'excel'],
            'presentation': ['powerpoint', 'browser', 'onenote'],
            'analysis': ['excel', 'calculator', 'notepad'],
            'communication': ['outlook', 'teams', 'browser'],
            'documentation': ['word', 'onenote', 'browser']
        }
        
        # Performance tracking
        self.stats = {
            'commands_executed': 0,
            'total_time': 0,
            'successful_opens': 0,
            'failed_opens': 0
        }
    
    def ultra_fast_open(self, app_name: str) -> bool:
        """Ultra-fast app opening with business focus"""
        start_time = time.time()
        app_name = app_name.lower().strip()
        
        # Handle aliases
        aliases = {
            'calc': 'calculator',
            'files': 'explorer',
            'mail': 'outlook',
            'email': 'outlook',
            'spreadsheet': 'excel',
            'document': 'word',
            'presentation': 'powerpoint',
            'browser': 'edge'
        }
        
        app_name = aliases.get(app_name, app_name)
        
        if app_name in self.business_apps:
            commands = self.business_apps[app_name]
            
            # Try each method until success
            for i, cmd in enumerate(commands):
                try:
                    if i == 0:  # First try: direct command
                        subprocess.Popen(cmd, shell=True, 
                                       stdout=subprocess.DEVNULL, 
                                       stderr=subprocess.DEVNULL)
                        time.sleep(0.5)  # Brief check
                        
                    elif i == 1:  # Second try: Win+R
                        pyautogui.hotkey('win', 'r')
                        time.sleep(0.1)
                        pyautogui.typewrite(cmd)
                        pyautogui.press('enter')
                        time.sleep(0.5)
                        
                    else:  # Final try: Start menu
                        pyautogui.press('win')
                        time.sleep(0.2)
                        pyautogui.typewrite(app_name)
                        time.sleep(0.3)
                        pyautogui.press('enter')
                    
                    end_time = time.time()
                    execution_time = end_time - start_time
                    
                    print(f"⚡ Opened {app_name} in {execution_time:.2f}s (method {i+1})")
                    self.stats['successful_opens'] += 1
                    self.stats['total_time'] += execution_time
                    return True
                    
                except Exception as e:
                    continue
        
        # If all methods fail
        self.stats['failed_opens'] += 1
        print(f"❌ Failed to open {app_name}")
        return False
    
    def execute_workflow(self, workflow_name: str) -> bool:
        """Execute pre-defined business workflows"""
        workflow_name = workflow_name.lower().strip()
        
        if workflow_name not in self.workflows:
            print(f"❌ Unknown workflow: {workflow_name}")
            return False
        
        apps = self.workflows[workflow_name]
        print(f"🚀 Starting {workflow_name} workflow...")
        
        success_count = 0
        for app in apps:
            print(f"  📱 Opening {app}...")
            if self.ultra_fast_open(app):
                success_count += 1
                time.sleep(1)  # Brief pause between apps
            else:
                print(f"  ⚠️ Failed to open {app}")
        
        print(f"✅ Workflow complete: {success_count}/{len(apps)} apps opened")
        return success_count == len(apps)
    
    def smart_calculation(self, expression: str) -> None:
        """Business-focused calculations"""
        # Clean and prepare expression
        expression = expression.replace('x', '*').replace('×', '*').replace('÷', '/')
        
        # Open calculator ultra-fast
        if self.ultra_fast_open('calculator'):
            time.sleep(1)  # Wait for calculator
            
            # Type expression
            pyautogui.typewrite(expression)
            pyautogui.press('enter')
            print(f"🧮 Calculated: {expression}")
        else:
            print("❌ Could not open calculator")
    
    def business_shortcuts(self, action: str) -> bool:
        """Common business keyboard shortcuts"""
        shortcuts = {
            'save': ['ctrl', 's'],
            'copy': ['ctrl', 'c'],
            'paste': ['ctrl', 'v'],
            'undo': ['ctrl', 'z'],
            'redo': ['ctrl', 'y'],
            'find': ['ctrl', 'f'],
            'new': ['ctrl', 'n'],
            'print': ['ctrl', 'p'],
            'close': ['alt', 'f4'],
            'switch': ['alt', 'tab'],
            'desktop': ['win', 'd'],
            'lock': ['win', 'l'],
            'screenshot': ['win', 'shift', 's']
        }
        
        action = action.lower().strip()
        if action in shortcuts:
            pyautogui.hotkey(*shortcuts[action])
            print(f"⌨️ Executed: {action}")
            return True
        
        return False
    
    def show_stats(self):
        """Show performance statistics"""
        print("\n📊 PERFORMANCE STATISTICS")
        print("=" * 30)
        print(f"Commands executed: {self.stats['commands_executed']}")
        print(f"Successful opens: {self.stats['successful_opens']}")
        print(f"Failed opens: {self.stats['failed_opens']}")
        
        if self.stats['successful_opens'] > 0:
            avg_time = self.stats['total_time'] / self.stats['successful_opens']
            print(f"Average open time: {avg_time:.2f}s")
            
        success_rate = (self.stats['successful_opens'] / 
                       (self.stats['successful_opens'] + self.stats['failed_opens']) * 100
                       if (self.stats['successful_opens'] + self.stats['failed_opens']) > 0 else 0)
        print(f"Success rate: {success_rate:.1f}%")

def main():
    print("🚀 ULTIMATE BUSINESS AGENT")
    print("=" * 35)
    print("⚡ Ultra-fast business automation")
    print("🎯 Optimized for productivity")
    print()
    
    agent = UltimateBusinessAgent()
    
    print("💼 BUSINESS COMMANDS:")
    print("Apps: 'open excel', 'open outlook', 'open teams'")
    print("Workflows: 'workflow morning', 'workflow analysis'")
    print("Calculations: 'calc 15*25+100'")
    print("Shortcuts: 'shortcut save', 'shortcut copy'")
    print("Other: 'stats', 'help', 'quit'")
    print()
    
    while True:
        try:
            command = input("💼 Business Command: ").strip().lower()
            start_time = time.time()
            
            if command in ['quit', 'exit']:
                break
            elif command == 'stats':
                agent.show_stats()
                continue
            elif command == 'help':
                print("Available workflows:", list(agent.workflows.keys()))
                print("Available apps:", list(agent.business_apps.keys()))
                continue
            
            # Parse commands
            if command.startswith('open '):
                app = command[5:]
                agent.ultra_fast_open(app)
                
            elif command.startswith('workflow '):
                workflow = command[9:]
                agent.execute_workflow(workflow)
                
            elif command.startswith('calc '):
                expression = command[5:]
                agent.smart_calculation(expression)
                
            elif command.startswith('shortcut '):
                shortcut = command[9:]
                agent.business_shortcuts(shortcut)
                
            else:
                print("❌ Unknown command. Type 'help' for assistance.")
                continue
            
            # Update stats
            end_time = time.time()
            agent.stats['commands_executed'] += 1
            
            print(f"⏱️ Command completed in {end_time - start_time:.2f}s")
            
        except KeyboardInterrupt:
            print("\n👋 Goodbye!")
            agent.show_stats()
            break
        except Exception as e:
            print(f"❌ Error: {e}")

if __name__ == "__main__":
    main()
