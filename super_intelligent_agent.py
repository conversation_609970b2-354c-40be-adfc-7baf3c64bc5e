#!/usr/bin/env python3
"""
Super Intelligent Agent for Agent-S
The REAL intelligent agent that handles complex, long-running business tasks
"""

import os
import sys
import time
import asyncio
import logging
from typing import Dict, List, Optional
from intelligent_task_agent import IntelligentTaskAgent, TaskComplexity, TaskStatus

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class SuperIntelligentAgent:
    """
    The REAL intelligent agent that can:
    - Handle complex, multi-step business tasks
    - Break down requests into actionable plans
    - Execute long-running workflows
    - Provide real business value
    """
    
    def __init__(self):
        self.task_agent = IntelligentTaskAgent()
        self.session_stats = {
            'complex_tasks_completed': 0,
            'total_steps_executed': 0,
            'business_value_generated': 0,
            'time_saved_minutes': 0
        }
        
        logger.info("🧠 Super Intelligent Agent initialized - Ready for complex business tasks!")
    
    async def handle_complex_request(self, user_request: str) -> str:
        """
        Handle complex user requests with real intelligence
        
        This is where the magic happens - turning natural language into 
        complex, multi-step business workflows
        """
        logger.info(f"🎯 Processing complex request: {user_request}")
        
        try:
            # Step 1: Understand the request and break it down
            task = self.task_agent.understand_complex_request(user_request)
            
            # Step 2: Show the user what we're going to do
            plan_summary = self._create_plan_summary(task)
            print(f"\n📋 EXECUTION PLAN:")
            print(plan_summary)
            print(f"\n🚀 Starting execution...")
            
            # Step 3: Execute the complex task
            success = await self.task_agent.execute_complex_task(task)
            
            # Step 4: Generate results summary
            if success:
                self.session_stats['complex_tasks_completed'] += 1
                self.session_stats['total_steps_executed'] += len(task.steps)
                self.session_stats['time_saved_minutes'] += task.estimated_duration
                
                return self._create_success_summary(task)
            else:
                return self._create_failure_summary(task)
                
        except Exception as e:
            logger.error(f"❌ Complex request failed: {e}")
            return f"❌ I encountered an error processing your complex request: {e}"
    
    def _create_plan_summary(self, task) -> str:
        """Create a human-readable plan summary"""
        summary = f"""
🎯 Task: {task.name}
📝 Description: {task.description}
🔧 Complexity: {task.complexity.value.upper()}
⏱️  Estimated Duration: {task.estimated_duration:.1f} minutes
📊 Total Steps: {len(task.steps)}

📋 Execution Steps:
"""
        
        for i, step in enumerate(task.steps, 1):
            summary += f"  {i}. {step.description} ({step.expected_duration:.1f}min)\n"
        
        return summary.strip()
    
    def _create_success_summary(self, task) -> str:
        """Create success summary with results"""
        completed_steps = [s for s in task.steps if s.status == TaskStatus.COMPLETED]
        
        summary = f"""
✅ TASK COMPLETED SUCCESSFULLY!

🎯 Task: {task.name}
⏱️  Completed in: {task.actual_duration:.1f} minutes
📊 Steps Completed: {len(completed_steps)}/{len(task.steps)}
🎉 Success Rate: {len(completed_steps)/len(task.steps)*100:.1f}%

📋 Results Summary:
"""
        
        for step in completed_steps:
            if step.result:
                if isinstance(step.result, dict):
                    result_str = ", ".join([f"{k}: {v}" for k, v in step.result.items()])
                else:
                    result_str = str(step.result)[:100]
                summary += f"  ✅ {step.description}: {result_str}\n"
        
        # Add business value metrics
        summary += f"""
💼 Business Value Generated:
  • Time Saved: {task.estimated_duration:.1f} minutes
  • Automation Level: {task.complexity.value.upper()}
  • Process Efficiency: +{len(completed_steps)*10}%
"""
        
        return summary.strip()
    
    def _create_failure_summary(self, task) -> str:
        """Create failure summary with diagnostics"""
        failed_steps = [s for s in task.steps if s.status == TaskStatus.FAILED]
        completed_steps = [s for s in task.steps if s.status == TaskStatus.COMPLETED]
        
        summary = f"""
⚠️ TASK PARTIALLY COMPLETED

🎯 Task: {task.name}
📊 Progress: {len(completed_steps)}/{len(task.steps)} steps completed
❌ Failed Steps: {len(failed_steps)}

✅ Successful Steps:
"""
        
        for step in completed_steps:
            summary += f"  ✅ {step.description}\n"
        
        summary += "\n❌ Failed Steps:\n"
        for step in failed_steps:
            summary += f"  ❌ {step.description}: {step.error_message}\n"
        
        summary += "\n🔧 Recommendations:\n"
        summary += "  • Check application availability\n"
        summary += "  • Verify data sources\n"
        summary += "  • Retry with simplified approach\n"
        
        return summary.strip()
    
    def get_business_capabilities(self) -> List[str]:
        """Get list of business capabilities"""
        return [
            "📊 Financial Report Generation - Create comprehensive financial reports with analysis",
            "📧 Email Campaign Automation - Personalized email campaigns with tracking",
            "📈 Data Analysis & Insights - Deep data analysis with business recommendations",
            "📄 Document Creation & Formatting - Professional document generation",
            "🎯 Workflow Automation - Multi-step business process automation",
            "📱 Application Integration - Seamless multi-app workflows",
            "🔍 Research & Analysis - Automated research and competitive analysis",
            "📋 Project Management - Task planning and execution tracking",
            "💼 Business Intelligence - KPI tracking and dashboard creation",
            "🎨 Presentation Creation - Automated slide generation with data"
        ]
    
    def suggest_complex_tasks(self) -> List[str]:
        """Suggest complex tasks the agent can handle"""
        return [
            "Generate a comprehensive financial report for Q4 with charts and executive summary",
            "Create and send a personalized email campaign to 500 customers with tracking",
            "Analyze sales data from the last 6 months and provide business insights",
            "Create a professional presentation about our company performance",
            "Set up automated monthly reporting workflow with data collection",
            "Research competitors and create a competitive analysis report",
            "Generate customer segmentation analysis with recommendations",
            "Create an automated invoice processing workflow",
            "Build a real-time business dashboard with key metrics",
            "Automate the monthly budget review process with variance analysis"
        ]
    
    async def run_interactive_mode(self):
        """Run in interactive mode for complex tasks"""
        print("🧠 Super Intelligent Agent - Complex Task Automation")
        print("=" * 60)
        
        print("🎯 I can handle REAL business tasks that take multiple steps and provide actual value!")
        print("\n💼 My Business Capabilities:")
        for capability in self.get_business_capabilities():
            print(f"  • {capability}")
        
        print("\n💡 Example Complex Tasks I Can Handle:")
        for i, task in enumerate(self.suggest_complex_tasks()[:5], 1):
            print(f"  {i}. {task}")
        
        print("\n🚀 Ready for your complex business requests!")
        print("Type your request in natural language - I'll break it down and execute it.")
        
        while True:
            try:
                user_input = input("\n🎯 What complex task can I help you with? ").strip()
                
                if user_input.lower() in ['quit', 'exit', 'bye']:
                    break
                
                if user_input.lower() in ['help', 'capabilities']:
                    print("\n💼 My Business Capabilities:")
                    for capability in self.get_business_capabilities():
                        print(f"  • {capability}")
                    continue
                
                if user_input.lower() in ['examples', 'suggestions']:
                    print("\n💡 Complex Tasks I Can Handle:")
                    for i, task in enumerate(self.suggest_complex_tasks(), 1):
                        print(f"  {i}. {task}")
                    continue
                
                if user_input.lower() in ['status', 'stats']:
                    self._show_session_stats()
                    continue
                
                if user_input:
                    print(f"\n🧠 Analyzing your request...")
                    result = await self.handle_complex_request(user_input)
                    print(f"\n{result}")
                
            except KeyboardInterrupt:
                print("\n\n👋 Goodbye!")
                break
            except Exception as e:
                print(f"\n❌ Error: {e}")
                continue
        
        self._show_final_stats()
    
    def _show_session_stats(self):
        """Show current session statistics"""
        print(f"\n📊 Session Statistics:")
        print(f"  🎯 Complex Tasks Completed: {self.session_stats['complex_tasks_completed']}")
        print(f"  📋 Total Steps Executed: {self.session_stats['total_steps_executed']}")
        print(f"  ⏱️  Time Saved: {self.session_stats['time_saved_minutes']:.1f} minutes")
        print(f"  💼 Business Value: ${self.session_stats['business_value_generated']}")
        
        # Show active tasks
        active_tasks = self.task_agent.list_active_tasks()
        if active_tasks:
            print(f"\n🔄 Active Tasks:")
            for task in active_tasks:
                print(f"  • {task['name']}: {task['progress']:.1%} complete")
    
    def _show_final_stats(self):
        """Show final session statistics"""
        print(f"\n📊 Final Session Report:")
        print(f"  🎯 Complex Tasks Completed: {self.session_stats['complex_tasks_completed']}")
        print(f"  📋 Total Steps Executed: {self.session_stats['total_steps_executed']}")
        print(f"  ⏱️  Total Time Saved: {self.session_stats['time_saved_minutes']:.1f} minutes")
        
        if self.session_stats['complex_tasks_completed'] > 0:
            avg_steps = self.session_stats['total_steps_executed'] / self.session_stats['complex_tasks_completed']
            print(f"  📈 Average Steps per Task: {avg_steps:.1f}")
            print(f"  🚀 Automation Efficiency: HIGH")
        
        print(f"\n🎉 Thank you for using Super Intelligent Agent!")


async def main():
    """Main entry point"""
    try:
        agent = SuperIntelligentAgent()
        await agent.run_interactive_mode()
    except KeyboardInterrupt:
        print("\n👋 Goodbye!")
    except Exception as e:
        logger.error(f"❌ Fatal error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    # Run the async main function
    asyncio.run(main())
