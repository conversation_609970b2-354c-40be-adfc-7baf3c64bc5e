#!/usr/bin/env python3
"""
Agent S2 Optimization Script
Makes Agent S2 faster, more accurate, and business-ready
"""

import os
import json
import pyautogui
from pathlib import Path

def optimize_pyautogui():
    """Optimize PyAutoGUI for speed and reliability"""
    # Speed optimizations
    pyautogui.PAUSE = 0.1  # Reduce delay between actions
    pyautogui.FAILSAFE = True  # Keep safety on
    
    # Get screen resolution for better accuracy
    screen_width, screen_height = pyautogui.size()
    print(f"📱 Screen Resolution: {screen_width}x{screen_height}")
    
    return screen_width, screen_height

def create_business_templates():
    """Create templates for common business tasks"""
    templates_dir = Path("business_templates")
    templates_dir.mkdir(exist_ok=True)
    
    # Email templates
    email_templates = {
        "follow_up": "Hi [Name],\n\nI wanted to follow up on our previous conversation about [Topic].\n\nBest regards,\n[Your Name]",
        "meeting_request": "Hi [Name],\n\nI'd like to schedule a meeting to discuss [Topic]. Are you available [Date/Time]?\n\nBest regards,\n[Your Name]",
        "project_update": "Hi Team,\n\nProject Update for [Date]:\n- Completed: [Tasks]\n- In Progress: [Tasks]\n- Next Steps: [Tasks]\n\nBest regards,\n[Your Name]"
    }
    
    with open(templates_dir / "email_templates.json", "w") as f:
        json.dump(email_templates, f, indent=2)
    
    # Excel formulas for business
    excel_formulas = {
        "sales_total": "=SUM(D2:D100)",
        "average_sale": "=AVERAGE(D2:D100)",
        "profit_margin": "=(Revenue-Cost)/Revenue*100",
        "monthly_growth": "=(Current_Month-Previous_Month)/Previous_Month*100"
    }
    
    with open(templates_dir / "excel_formulas.json", "w") as f:
        json.dump(excel_formulas, f, indent=2)
    
    print("✅ Created business templates")

def create_hotkey_shortcuts():
    """Create common business hotkey combinations"""
    shortcuts = {
        "excel": {
            "new_workbook": ["ctrl", "n"],
            "save": ["ctrl", "s"],
            "save_as": ["f12"],
            "insert_chart": ["alt", "n", "c"],
            "format_cells": ["ctrl", "1"],
            "autosum": ["alt", "="]
        },
        "outlook": {
            "new_email": ["ctrl", "n"],
            "send": ["ctrl", "enter"],
            "reply": ["ctrl", "r"],
            "forward": ["ctrl", "f"],
            "calendar": ["ctrl", "2"],
            "contacts": ["ctrl", "3"]
        },
        "word": {
            "new_document": ["ctrl", "n"],
            "save": ["ctrl", "s"],
            "print": ["ctrl", "p"],
            "find_replace": ["ctrl", "h"],
            "spell_check": ["f7"]
        },
        "windows": {
            "task_manager": ["ctrl", "shift", "esc"],
            "run_dialog": ["win", "r"],
            "minimize_all": ["win", "d"],
            "screenshot": ["win", "shift", "s"],
            "switch_apps": ["alt", "tab"]
        }
    }
    
    with open("business_shortcuts.json", "w") as f:
        json.dump(shortcuts, f, indent=2)
    
    print("✅ Created hotkey shortcuts reference")

def optimize_agent_s2_config():
    """Create optimized Agent S2 configuration"""
    screen_width, screen_height = optimize_pyautogui()
    
    # Calculate optimal resize dimensions (maintain aspect ratio)
    optimal_width = min(1024, screen_width)
    optimal_height = int(optimal_width * screen_height / screen_width)
    
    config = {
        "fast_config": {
            "provider": "openai",
            "model": "gpt-4o-mini",
            "grounding_model_provider": "openai", 
            "grounding_model": "gpt-4o-mini",
            "grounding_model_resize_width": optimal_width,
            "grounding_model_resize_height": optimal_height
        },
        "accurate_config": {
            "provider": "openai",
            "model": "gpt-4o",
            "grounding_model_provider": "openai",
            "grounding_model": "gpt-4o",
            "grounding_model_resize_width": 1366,
            "grounding_model_resize_height": 768
        },
        "business_prompts": {
            "excel_task": "You are an Excel automation expert. Focus on creating professional spreadsheets with proper formatting, formulas, and business-appropriate layouts.",
            "email_task": "You are an email management expert. Focus on professional communication, proper formatting, and efficient email organization.",
            "file_management": "You are a file organization expert. Focus on creating logical folder structures and naming conventions for business documents."
        }
    }
    
    with open("agent_s2_config.json", "w") as f:
        json.dump(config, f, indent=2)
    
    print(f"✅ Created optimized config for {screen_width}x{screen_height} screen")
    return config

def create_business_commands():
    """Create ready-to-use business command scripts"""
    
    # Fast commands
    fast_commands = f'''
# FAST BUSINESS COMMANDS (GPT-4o-mini - Fast & Cheap)

# Quick app opening
agent_s2 --provider "openai" --model "gpt-4o-mini" --grounding_model_provider "openai" --grounding_model "gpt-4o-mini" --grounding_model_resize_width 1024

# Example tasks to try:
# "Open Excel and create a sales report template"
# "Open calculator and calculate 15 * 25 + 100"
# "Take a screenshot and save it"
# "Open Notepad and write a to-do list"
'''

    # Accurate commands  
    accurate_commands = f'''
# ACCURATE BUSINESS COMMANDS (GPT-4o - Slower but More Capable)

# Complex business tasks
agent_s2 --provider "openai" --model "gpt-4o" --grounding_model_provider "openai" --grounding_model "gpt-4o" --grounding_model_resize_width 1366

# Example complex tasks:
# "Open Excel, create a financial dashboard with charts and formulas"
# "Open Outlook, organize emails by priority and create calendar events"
# "Navigate to our CRM system and update customer information"
# "Create a comprehensive project report with data from multiple sources"
'''

    with open("fast_commands.txt", "w") as f:
        f.write(fast_commands)
    
    with open("accurate_commands.txt", "w") as f:
        f.write(accurate_commands)
    
    print("✅ Created command reference files")

def main():
    print("🔧 OPTIMIZING AGENT S2 FOR BUSINESS USE")
    print("=" * 50)
    
    # Run all optimizations
    screen_width, screen_height = optimize_pyautogui()
    create_business_templates()
    create_hotkey_shortcuts()
    config = optimize_agent_s2_config()
    create_business_commands()
    
    print("\n🎯 OPTIMIZATION COMPLETE!")
    print("=" * 30)
    print(f"📱 Screen: {screen_width}x{screen_height}")
    print(f"⚡ Fast config: GPT-4o-mini @ {config['fast_config']['grounding_model_resize_width']}px")
    print(f"🎯 Accurate config: GPT-4o @ {config['accurate_config']['grounding_model_resize_width']}px")
    print("\n📁 Created files:")
    print("- business_templates/ (email & Excel templates)")
    print("- business_shortcuts.json (hotkey reference)")
    print("- agent_s2_config.json (optimized settings)")
    print("- fast_commands.txt (quick command reference)")
    print("- accurate_commands.txt (complex task reference)")
    
    print("\n🚀 READY FOR BUSINESS USE!")
    print("Use fast_agent_s2.bat for quick tasks")
    print("Use accurate commands for complex workflows")

if __name__ == "__main__":
    main()
