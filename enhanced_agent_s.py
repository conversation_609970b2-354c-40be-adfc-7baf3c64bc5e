#!/usr/bin/env python3
"""
Enhanced Agent-S System
Integrates all improvements: better app launching, AI vision, error handling, and simplified workflows
"""

import os
import sys
import time
import argparse
from typing import Dict, List, Optional
import logging

# Import enhanced components
try:
    from enhanced_app_launcher import Enhanced<PERSON><PERSON><PERSON>auncher
    from enhanced_ai_vision import EnhancedAIVision, VisionCapability, AIProvider
    from enhanced_error_handler import EnhancedErrorHandler
    from enhanced_workflow_manager import EnhancedWorkflowManager
except ImportError as e:
    print(f"❌ Import error: {e}")
    print("Please ensure all enhanced components are in the same directory")
    sys.exit(1)

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class EnhancedAgentS:
    """
    Enhanced Agent-S System
    
    Combines all improvements into a unified, production-ready system:
    - Enhanced app launching with multiple fallback methods
    - Real AI vision integration (GPT-4<PERSON>, <PERSON>, <PERSON><PERSON><PERSON>)
    - Comprehensive error handling and recovery
    - Simplified workflows with natural language processing
    """
    
    def __init__(self, config: Dict = None):
        """Initialize Enhanced Agent-S with optional configuration"""
        self.config = config or {}
        
        # Initialize components
        logger.info("🔧 Initializing Enhanced Agent-S...")
        
        try:
            self.app_launcher = EnhancedAppLauncher()
            logger.info("✅ App Launcher initialized")
            
            self.ai_vision = EnhancedAIVision()
            logger.info("✅ AI Vision initialized")
            
            self.error_handler = EnhancedErrorHandler()
            logger.info("✅ Error Handler initialized")
            
            self.workflow_manager = EnhancedWorkflowManager()
            logger.info("✅ Workflow Manager initialized")
            
        except Exception as e:
            logger.error(f"❌ Initialization failed: {e}")
            raise
        
        # System statistics
        self.session_stats = {
            'start_time': time.time(),
            'requests_processed': 0,
            'successful_requests': 0,
            'failed_requests': 0,
            'apps_launched': 0,
            'workflows_executed': 0,
            'ai_analyses': 0
        }
        
        logger.info("🎯 Enhanced Agent-S ready!")
    
    def process_request(self, user_input: str) -> str:
        """
        Main entry point for processing user requests
        
        Args:
            user_input: Natural language user request
            
        Returns:
            Response message
        """
        start_time = time.time()
        self.session_stats['requests_processed'] += 1
        
        try:
            logger.info(f"🎯 Processing: {user_input}")
            
            # Use workflow manager for natural language processing
            result = self.workflow_manager.process_user_request(user_input)
            
            # Update statistics based on result
            self._update_stats_from_result(result)
            
            processing_time = time.time() - start_time
            logger.info(f"✅ Processed in {processing_time:.2f}s")
            
            self.session_stats['successful_requests'] += 1
            return result
            
        except Exception as e:
            # Handle errors gracefully
            error_context = self.error_handler.handle_error(e, {
                'user_input': user_input,
                'processing_time': time.time() - start_time
            })
            
            self.session_stats['failed_requests'] += 1
            
            if error_context.recovery_successful:
                return f"⚠️ Encountered an issue but recovered: {error_context.message}"
            else:
                return f"❌ Sorry, I encountered an error: {error_context.message}"
    
    def _update_stats_from_result(self, result: str):
        """Update session statistics based on result content"""
        if "Opened" in result or "launched" in result.lower():
            self.session_stats['apps_launched'] += 1
        
        if "Workflow:" in result:
            self.session_stats['workflows_executed'] += 1
        
        if "AI Analysis:" in result or "Screen Analysis:" in result:
            self.session_stats['ai_analyses'] += 1
    
    def get_system_status(self) -> Dict:
        """Get comprehensive system status"""
        uptime = time.time() - self.session_stats['start_time']
        
        # Get component statistics
        launcher_stats = self.app_launcher.get_stats()
        vision_stats = self.ai_vision.get_stats()
        error_summary = self.error_handler.get_error_summary()
        workflow_stats = self.workflow_manager.get_workflow_stats()
        
        return {
            'system': {
                'uptime_seconds': uptime,
                'uptime_formatted': f"{uptime/60:.1f} minutes",
                'status': 'healthy' if error_summary['recent_errors'] == 0 else 'issues_detected'
            },
            'session': self.session_stats,
            'components': {
                'app_launcher': launcher_stats,
                'ai_vision': vision_stats,
                'error_handler': {
                    'total_errors': error_summary['total_errors'],
                    'recent_errors': error_summary['recent_errors'],
                    'recovery_rate': error_summary['recovery_rate']
                },
                'workflows': workflow_stats
            },
            'recommendations': error_summary['recommendations']
        }
    
    def run_interactive_mode(self):
        """Run in interactive mode with natural language interface"""
        print("🤖 Enhanced Agent-S - Interactive Mode")
        print("=" * 50)
        
        # Show system status
        status = self.get_system_status()
        print(f"🖥️  System Status: {status['system']['status']}")
        print(f"🤖 Available AI Providers: {', '.join(status['components']['ai_vision']['available_providers'])}")
        print(f"📱 Registered Apps: {status['components']['app_launcher']['registered_apps']}")
        print(f"🔄 Available Workflows: {status['components']['workflows']['total_workflows']}")
        
        print("\n💡 What can I help you with today?")
        print("Examples:")
        print("  • 'Open calculator'")
        print("  • 'Start my morning routine'")
        print("  • 'Take a screenshot'")
        print("  • 'What do you see on the screen?'")
        print("  • 'Check system status'")
        print("  • 'Help me with Excel'")
        
        while True:
            try:
                user_input = input("\n🎯 You: ").strip()
                
                if user_input.lower() in ['quit', 'exit', 'bye', 'goodbye']:
                    break
                
                if user_input.lower() in ['status', 'stats', 'system status']:
                    self._show_detailed_status()
                    continue
                
                if user_input.lower() in ['help', '?']:
                    self._show_help()
                    continue
                
                if user_input:
                    print("🤖 Agent-S:", end=" ")
                    result = self.process_request(user_input)
                    print(result)
                
            except KeyboardInterrupt:
                print("\n\n👋 Goodbye!")
                break
            except Exception as e:
                print(f"\n❌ Unexpected error: {e}")
                continue
        
        # Show final statistics
        self._show_session_summary()
    
    def _show_detailed_status(self):
        """Show detailed system status"""
        status = self.get_system_status()
        
        print("\n📊 System Status Report")
        print("-" * 30)
        print(f"⏱️  Uptime: {status['system']['uptime_formatted']}")
        print(f"📈 Requests: {status['session']['successful_requests']}/{status['session']['requests_processed']} successful")
        print(f"🚀 Apps Launched: {status['session']['apps_launched']}")
        print(f"🔄 Workflows Executed: {status['session']['workflows_executed']}")
        print(f"👁️  AI Analyses: {status['session']['ai_analyses']}")
        
        print(f"\n🔧 Component Health:")
        print(f"  App Launcher: {status['components']['app_launcher']['success_rate']} success")
        print(f"  AI Vision: {status['components']['ai_vision']['success_rate']} success")
        print(f"  Error Recovery: {status['components']['error_handler']['recovery_rate']} recovery")
        
        print(f"\n💡 Recommendations:")
        for rec in status['recommendations']:
            print(f"  • {rec}")
    
    def _show_help(self):
        """Show help information"""
        print("\n❓ Enhanced Agent-S Help")
        print("-" * 25)
        print("🎯 Natural Language Commands:")
        print("  • App Control: 'open [app]', 'launch calculator', 'start chrome'")
        print("  • Workflows: 'morning routine', 'productivity setup', 'business apps'")
        print("  • Screenshots: 'take screenshot', 'capture screen', 'snap image'")
        print("  • AI Analysis: 'what do you see?', 'analyze screen', 'help me with...'")
        print("  • System: 'status', 'health check', 'system info'")
        print("\n🔧 Special Commands:")
        print("  • 'status' - Show detailed system status")
        print("  • 'help' - Show this help message")
        print("  • 'quit' - Exit the system")
    
    def _show_session_summary(self):
        """Show session summary"""
        status = self.get_system_status()
        
        print(f"\n📊 Session Summary")
        print("-" * 20)
        print(f"⏱️  Duration: {status['system']['uptime_formatted']}")
        print(f"📈 Total Requests: {status['session']['requests_processed']}")
        print(f"✅ Successful: {status['session']['successful_requests']}")
        print(f"❌ Failed: {status['session']['failed_requests']}")
        print(f"🚀 Apps Launched: {status['session']['apps_launched']}")
        print(f"🔄 Workflows: {status['session']['workflows_executed']}")
        print(f"👁️  AI Analyses: {status['session']['ai_analyses']}")
        
        success_rate = (status['session']['successful_requests'] / max(1, status['session']['requests_processed'])) * 100
        print(f"📊 Success Rate: {success_rate:.1f}%")


def main():
    """Main entry point"""
    parser = argparse.ArgumentParser(description="Enhanced Agent-S System")
    parser.add_argument("--command", "-c", help="Single command to execute")
    parser.add_argument("--config", help="Configuration file path")
    parser.add_argument("--debug", action="store_true", help="Enable debug logging")
    
    args = parser.parse_args()
    
    if args.debug:
        logging.getLogger().setLevel(logging.DEBUG)
    
    try:
        # Initialize Enhanced Agent-S
        config = {}
        if args.config and os.path.exists(args.config):
            import json
            with open(args.config, 'r') as f:
                config = json.load(f)
        
        agent = EnhancedAgentS(config)
        
        if args.command:
            # Single command mode
            result = agent.process_request(args.command)
            print(result)
        else:
            # Interactive mode
            agent.run_interactive_mode()
    
    except KeyboardInterrupt:
        print("\n👋 Goodbye!")
    except Exception as e:
        logger.error(f"❌ Fatal error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
