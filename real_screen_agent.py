#!/usr/bin/env python3
"""
REAL Screen-Controlling Intelligent Agent
Actually controls your screen, uses real AI vision, and performs real actions
"""

import os
import sys
import time
import base64
import io
import json
import requests
import pyautogui
import cv2
import numpy as np
from PIL import Image, ImageDraw
import subprocess
import psutil
from typing import Dict, List, Tuple, Optional
import logging

# Configure pyautogui
pyautogui.FAILSAFE = True
pyautogui.PAUSE = 0.5

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class RealScreenAgent:
    """Agent that actually controls your screen and uses real AI vision"""
    
    def __init__(self):
        # Get OpenAI API key
        self.openai_key = os.getenv('OPENAI_API_KEY')
        if not self.openai_key:
            print("❌ OpenAI API key not found!")
            print("Please set your OpenAI API key:")
            print("export OPENAI_API_KEY='your-key-here'")
            print("Or set it in Windows: setx OPENAI_API_KEY 'your-key-here'")
            sys.exit(1)
        
        print(f"✅ OpenAI API key found: {self.openai_key[:10]}...")
        
        # Screen info
        self.screen_width, self.screen_height = pyautogui.size()
        print(f"🖥️ Screen size: {self.screen_width}x{self.screen_height}")
        
        # Action history
        self.action_history = []
        
        print("🧠 Real Screen Agent initialized - Ready to control your computer!")
    
    def take_screenshot_with_analysis(self) -> Tuple[str, Image.Image]:
        """Take screenshot and prepare for AI analysis"""
        try:
            # Take screenshot
            screenshot = pyautogui.screenshot()
            
            # Resize for AI processing (keep aspect ratio)
            max_size = 1280
            if screenshot.width > max_size:
                ratio = max_size / screenshot.width
                new_height = int(screenshot.height * ratio)
                screenshot = screenshot.resize((max_size, new_height), Image.LANCZOS)
            
            # Convert to base64 for OpenAI
            buffered = io.BytesIO()
            screenshot.save(buffered, format="JPEG", quality=90)
            img_base64 = base64.b64encode(buffered.getvalue()).decode('utf-8')
            
            return img_base64, screenshot
            
        except Exception as e:
            logger.error(f"Screenshot failed: {e}")
            raise
    
    def analyze_screen_with_gpt4v(self, task_description: str) -> Dict:
        """Use GPT-4V to analyze screen and plan actions"""
        try:
            img_base64, screenshot = self.take_screenshot_with_analysis()
            
            prompt = f"""
You are an AI assistant that can see and control a Windows computer screen. 

CURRENT TASK: {task_description}

Please analyze the current screen and provide a detailed action plan. Look for:
1. What applications are currently open
2. What UI elements are visible (buttons, menus, text fields, etc.)
3. What actions need to be taken to complete the task
4. Specific coordinates or elements to click/interact with

Respond in JSON format with:
{{
    "screen_analysis": "What you see on the screen",
    "current_apps": ["list of visible applications"],
    "ui_elements": ["list of clickable elements you can see"],
    "action_plan": [
        {{
            "step": 1,
            "action": "click|type|key|scroll|wait",
            "target": "description of what to click/interact with",
            "coordinates": [x, y] or null,
            "text": "text to type" or null,
            "key": "key to press" or null,
            "description": "what this step accomplishes"
        }}
    ],
    "confidence": "high|medium|low"
}}

Be specific about coordinates when you can see clickable elements. If you need to open an application, look for it in the taskbar, start menu, or desktop.
"""

            headers = {
                'Authorization': f'Bearer {self.openai_key}',
                'Content-Type': 'application/json'
            }
            
            payload = {
                'model': 'gpt-4o',
                'messages': [
                    {
                        'role': 'user',
                        'content': [
                            {
                                'type': 'text',
                                'text': prompt
                            },
                            {
                                'type': 'image_url',
                                'image_url': {
                                    'url': f"data:image/jpeg;base64,{img_base64}",
                                    'detail': 'high'
                                }
                            }
                        ]
                    }
                ],
                'max_tokens': 1500,
                'temperature': 0.1
            }
            
            print("🧠 Analyzing screen with GPT-4V...")
            response = requests.post(
                'https://api.openai.com/v1/chat/completions',
                headers=headers,
                json=payload,
                timeout=30
            )
            
            if response.status_code == 200:
                result = response.json()
                ai_response = result['choices'][0]['message']['content']
                
                # Try to extract JSON from response
                try:
                    # Look for JSON in the response
                    import re
                    json_match = re.search(r'\{.*\}', ai_response, re.DOTALL)
                    if json_match:
                        analysis = json.loads(json_match.group())
                        analysis['raw_response'] = ai_response
                        return analysis
                    else:
                        # Fallback if no JSON found
                        return {
                            'screen_analysis': ai_response,
                            'action_plan': [],
                            'confidence': 'low',
                            'raw_response': ai_response
                        }
                except json.JSONDecodeError:
                    return {
                        'screen_analysis': ai_response,
                        'action_plan': [],
                        'confidence': 'low',
                        'raw_response': ai_response
                    }
            else:
                print(f"❌ OpenAI API error: {response.status_code}")
                print(response.text)
                return None
                
        except Exception as e:
            print(f"❌ Screen analysis failed: {e}")
            return None
    
    def execute_action(self, action: Dict) -> bool:
        """Execute a single action on the screen"""
        try:
            action_type = action.get('action', '').lower()
            
            print(f"🔄 Executing: {action.get('description', 'Unknown action')}")
            
            if action_type == 'click':
                coords = action.get('coordinates')
                if coords and len(coords) == 2:
                    x, y = coords
                    print(f"  🖱️ Clicking at ({x}, {y})")
                    pyautogui.click(x, y)
                    time.sleep(1)
                    return True
                else:
                    print(f"  ❌ Invalid coordinates: {coords}")
                    return False
            
            elif action_type == 'type':
                text = action.get('text', '')
                if text:
                    print(f"  ⌨️ Typing: {text}")
                    pyautogui.typewrite(text, interval=0.05)
                    time.sleep(0.5)
                    return True
                else:
                    print(f"  ❌ No text to type")
                    return False
            
            elif action_type == 'key':
                key = action.get('key', '')
                if key:
                    print(f"  🔑 Pressing key: {key}")
                    pyautogui.press(key)
                    time.sleep(0.5)
                    return True
                else:
                    print(f"  ❌ No key specified")
                    return False
            
            elif action_type == 'hotkey':
                keys = action.get('keys', [])
                if keys:
                    print(f"  🔑 Pressing hotkey: {'+'.join(keys)}")
                    pyautogui.hotkey(*keys)
                    time.sleep(1)
                    return True
                else:
                    print(f"  ❌ No keys specified")
                    return False
            
            elif action_type == 'wait':
                duration = action.get('duration', 1)
                print(f"  ⏱️ Waiting {duration} seconds")
                time.sleep(duration)
                return True
            
            elif action_type == 'scroll':
                direction = action.get('direction', 'down')
                amount = action.get('amount', 3)
                coords = action.get('coordinates', [self.screen_width//2, self.screen_height//2])
                
                print(f"  🖱️ Scrolling {direction} at ({coords[0]}, {coords[1]})")
                if direction == 'down':
                    pyautogui.scroll(-amount, x=coords[0], y=coords[1])
                else:
                    pyautogui.scroll(amount, x=coords[0], y=coords[1])
                time.sleep(0.5)
                return True
            
            else:
                print(f"  ❌ Unknown action type: {action_type}")
                return False
                
        except Exception as e:
            print(f"  ❌ Action failed: {e}")
            return False
    
    def find_and_click_element(self, element_description: str) -> bool:
        """Use AI to find and click a specific element"""
        try:
            analysis = self.analyze_screen_with_gpt4v(f"Find and click: {element_description}")
            
            if not analysis:
                return False
            
            print(f"🔍 Looking for: {element_description}")
            print(f"📊 AI Analysis: {analysis.get('screen_analysis', 'No analysis')[:100]}...")
            
            # Look for click actions in the plan
            action_plan = analysis.get('action_plan', [])
            for action in action_plan:
                if action.get('action') == 'click':
                    return self.execute_action(action)
            
            print(f"❌ Could not find clickable element: {element_description}")
            return False
            
        except Exception as e:
            print(f"❌ Element search failed: {e}")
            return False
    
    def open_application(self, app_name: str) -> bool:
        """Open an application using multiple methods"""
        print(f"🚀 Opening {app_name}...")
        
        # Method 1: Try direct command
        try:
            if app_name.lower() in ['calculator', 'calc']:
                subprocess.Popen(['calc.exe'])
                time.sleep(2)
                return True
            elif app_name.lower() in ['notepad']:
                subprocess.Popen(['notepad.exe'])
                time.sleep(2)
                return True
            elif app_name.lower() in ['chrome', 'browser']:
                subprocess.Popen(['chrome.exe'])
                time.sleep(3)
                return True
        except:
            pass
        
        # Method 2: Use Windows key + search
        try:
            print(f"  🔍 Searching for {app_name} via Start menu...")
            pyautogui.press('win')
            time.sleep(1)
            pyautogui.typewrite(app_name)
            time.sleep(1)
            pyautogui.press('enter')
            time.sleep(3)
            return True
        except Exception as e:
            print(f"  ❌ Start menu search failed: {e}")
            return False
    
    def handle_complex_task(self, task_description: str) -> bool:
        """Handle a complex task using AI vision and screen control"""
        print(f"\n🎯 COMPLEX TASK: {task_description}")
        print("=" * 60)
        
        max_steps = 10
        step_count = 0
        
        while step_count < max_steps:
            step_count += 1
            print(f"\n📋 Step {step_count}/{max_steps}")
            
            # Analyze current screen
            analysis = self.analyze_screen_with_gpt4v(task_description)
            
            if not analysis:
                print("❌ Could not analyze screen")
                return False
            
            print(f"🧠 Screen Analysis: {analysis.get('screen_analysis', 'No analysis')[:150]}...")
            
            action_plan = analysis.get('action_plan', [])
            
            if not action_plan:
                print("✅ Task appears to be complete or no actions needed")
                return True
            
            # Execute the first action in the plan
            first_action = action_plan[0]
            success = self.execute_action(first_action)
            
            if not success:
                print(f"❌ Action failed, trying alternative approach...")
                # Try a simple approach based on task description
                if 'calculator' in task_description.lower():
                    return self.open_application('calculator')
                elif 'notepad' in task_description.lower():
                    return self.open_application('notepad')
                elif 'chrome' in task_description.lower() or 'browser' in task_description.lower():
                    return self.open_application('chrome')
            
            # Record action
            self.action_history.append({
                'step': step_count,
                'action': first_action,
                'success': success,
                'timestamp': time.time()
            })
            
            # Brief pause between actions
            time.sleep(1)
        
        print(f"⚠️ Reached maximum steps ({max_steps})")
        return False
    
    def demonstrate_capabilities(self):
        """Demonstrate the agent's real capabilities"""
        print("\n🎭 REAL SCREEN CONTROL DEMONSTRATION")
        print("=" * 50)
        
        demos = [
            ("Open Calculator", "open calculator application"),
            ("Open Notepad", "open notepad text editor"),
            ("Take Screenshot", "take a screenshot and analyze what's on screen")
        ]
        
        for demo_name, demo_task in demos:
            print(f"\n🎬 Demo: {demo_name}")
            input("Press Enter to continue...")
            
            if demo_task == "take a screenshot and analyze what's on screen":
                analysis = self.analyze_screen_with_gpt4v("Analyze what's currently on the screen")
                if analysis:
                    print(f"📊 AI sees: {analysis.get('screen_analysis', 'No analysis')}")
                    print(f"🎯 Confidence: {analysis.get('confidence', 'unknown')}")
            else:
                success = self.handle_complex_task(demo_task)
                print(f"Result: {'✅ Success' if success else '❌ Failed'}")
    
    def interactive_mode(self):
        """Interactive mode for real-time screen control"""
        print("\n🎮 INTERACTIVE SCREEN CONTROL MODE")
        print("=" * 40)
        print("🎯 I can now ACTUALLY control your screen using AI vision!")
        print("💡 Try commands like:")
        print("  • 'open calculator'")
        print("  • 'open notepad and type hello world'")
        print("  • 'click on the start button'")
        print("  • 'analyze what's on my screen'")
        print("  • 'demo' - run capability demonstration")
        
        while True:
            try:
                task = input("\n🎯 What should I do on your screen? ").strip()
                
                if task.lower() in ['quit', 'exit', 'bye']:
                    break
                
                if task.lower() == 'demo':
                    self.demonstrate_capabilities()
                    continue
                
                if task.lower() in ['analyze', 'analyze screen', 'what do you see']:
                    analysis = self.analyze_screen_with_gpt4v("Analyze what's currently on the screen")
                    if analysis:
                        print(f"\n🧠 AI Analysis:")
                        print(f"📊 Screen: {analysis.get('screen_analysis', 'No analysis')}")
                        print(f"📱 Apps: {', '.join(analysis.get('current_apps', []))}")
                        print(f"🎯 Confidence: {analysis.get('confidence', 'unknown')}")
                    continue
                
                if task:
                    print(f"\n🚀 Executing: {task}")
                    success = self.handle_complex_task(task)
                    print(f"\n{'✅ Task completed!' if success else '❌ Task failed or incomplete'}")
            
            except KeyboardInterrupt:
                print("\n👋 Goodbye!")
                break
            except Exception as e:
                print(f"❌ Error: {e}")
        
        # Show action history
        if self.action_history:
            print(f"\n📊 Session Summary: {len(self.action_history)} actions performed")


def main():
    """Main entry point"""
    try:
        agent = RealScreenAgent()
        agent.interactive_mode()
    except KeyboardInterrupt:
        print("\n👋 Goodbye!")
    except Exception as e:
        print(f"❌ Fatal error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
