#!/usr/bin/env python3
"""
Enhanced Workflow Manager for Agent-S
Provides simplified, reliable workflows with natural language processing
"""

import os
import sys
import time
import json
from typing import Dict, List, Optional, Callable, Any
from dataclasses import dataclass
from enum import Enum
import logging
from enhanced_app_launcher import <PERSON>han<PERSON><PERSON><PERSON><PERSON>auncher
from enhanced_ai_vision import EnhancedAIVision, VisionCapability
from enhanced_error_handler import EnhancedErrorHandler

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class WorkflowType(Enum):
    """Types of workflows"""
    BUSINESS = "business"
    PRODUCTIVITY = "productivity"
    DEVELOPMENT = "development"
    ENTERTAINMENT = "entertainment"
    SYSTEM = "system"

class TaskStatus(Enum):
    """Task execution status"""
    PENDING = "pending"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    FAILED = "failed"
    SKIPPED = "skipped"

@dataclass
class WorkflowTask:
    """Individual task within a workflow"""
    name: str
    description: str
    action: Callable
    args: List[Any]
    kwargs: Dict[str, Any]
    status: TaskStatus = TaskStatus.PENDING
    error_message: str = ""
    execution_time: float = 0.0
    retry_count: int = 0
    max_retries: int = 2

@dataclass
class Workflow:
    """Complete workflow definition"""
    name: str
    description: str
    category: WorkflowType
    tasks: List[WorkflowTask]
    success_rate: float = 0.0
    total_executions: int = 0
    successful_executions: int = 0

class EnhancedWorkflowManager:
    """Enhanced workflow management system"""
    
    def __init__(self):
        self.app_launcher = EnhancedAppLauncher()
        self.ai_vision = EnhancedAIVision()
        self.error_handler = EnhancedErrorHandler()
        
        self.workflows = {}
        self.execution_history = []
        
        # Initialize built-in workflows
        self._create_builtin_workflows()
        
        # Natural language patterns for workflow detection
        self.nl_patterns = {
            'open_app': ['open', 'launch', 'start', 'run'],
            'business_workflow': ['morning routine', 'daily tasks', 'work setup', 'business apps'],
            'productivity_workflow': ['productivity', 'organize', 'manage', 'schedule'],
            'quick_calculation': ['calculate', 'math', 'compute', 'add', 'subtract', 'multiply', 'divide'],
            'take_screenshot': ['screenshot', 'capture', 'snap', 'image'],
            'system_info': ['system', 'status', 'health', 'performance', 'info']
        }
    
    def _create_builtin_workflows(self):
        """Create built-in reliable workflows"""
        
        # Business Morning Routine
        business_morning = Workflow(
            name="business_morning_routine",
            description="Start your business day with essential applications",
            category=WorkflowType.BUSINESS,
            tasks=[
                WorkflowTask(
                    name="Open Email",
                    description="Launch Outlook for email management",
                    action=self.app_launcher.launch_app,
                    args=["outlook"],
                    kwargs={}
                ),
                WorkflowTask(
                    name="Open Browser",
                    description="Launch Chrome for web browsing",
                    action=self.app_launcher.launch_app,
                    args=["chrome"],
                    kwargs={}
                ),
                WorkflowTask(
                    name="Open Excel",
                    description="Launch Excel for spreadsheet work",
                    action=self.app_launcher.launch_app,
                    args=["excel"],
                    kwargs={}
                ),
                WorkflowTask(
                    name="Open Calculator",
                    description="Launch Calculator for quick calculations",
                    action=self.app_launcher.launch_app,
                    args=["calculator"],
                    kwargs={}
                )
            ]
        )
        
        # Quick Productivity Setup
        productivity_setup = Workflow(
            name="productivity_setup",
            description="Set up productivity tools quickly",
            category=WorkflowType.PRODUCTIVITY,
            tasks=[
                WorkflowTask(
                    name="Open Notepad",
                    description="Launch Notepad for quick notes",
                    action=self.app_launcher.launch_app,
                    args=["notepad"],
                    kwargs={}
                ),
                WorkflowTask(
                    name="Open Calculator",
                    description="Launch Calculator",
                    action=self.app_launcher.launch_app,
                    args=["calculator"],
                    kwargs={}
                ),
                WorkflowTask(
                    name="Take Screenshot",
                    description="Capture current screen",
                    action=self._take_screenshot_task,
                    args=[],
                    kwargs={}
                )
            ]
        )
        
        # System Health Check
        system_health = Workflow(
            name="system_health_check",
            description="Check system health and performance",
            category=WorkflowType.SYSTEM,
            tasks=[
                WorkflowTask(
                    name="Check System Stats",
                    description="Gather system performance information",
                    action=self._check_system_health,
                    args=[],
                    kwargs={}
                ),
                WorkflowTask(
                    name="Check App Launcher Stats",
                    description="Review app launcher performance",
                    action=self._check_launcher_stats,
                    args=[],
                    kwargs={}
                ),
                WorkflowTask(
                    name="Check AI Vision Stats",
                    description="Review AI vision system performance",
                    action=self._check_vision_stats,
                    args=[],
                    kwargs={}
                )
            ]
        )
        
        # Add workflows to registry
        self.workflows[business_morning.name] = business_morning
        self.workflows[productivity_setup.name] = productivity_setup
        self.workflows[system_health.name] = system_health
        
        logger.info(f"✅ Initialized {len(self.workflows)} built-in workflows")
    
    def process_natural_language(self, user_input: str) -> Optional[str]:
        """
        Process natural language input and determine appropriate action
        
        Args:
            user_input: User's natural language request
            
        Returns:
            Action to execute or None if not understood
        """
        user_input = user_input.lower().strip()
        
        # Check for app opening requests
        for pattern in self.nl_patterns['open_app']:
            if pattern in user_input:
                # Extract app name
                words = user_input.split()
                for i, word in enumerate(words):
                    if word == pattern and i + 1 < len(words):
                        app_name = words[i + 1]
                        return f"open_app:{app_name}"
        
        # Check for workflow requests
        for pattern in self.nl_patterns['business_workflow']:
            if pattern in user_input:
                return "execute_workflow:business_morning_routine"
        
        for pattern in self.nl_patterns['productivity_workflow']:
            if pattern in user_input:
                return "execute_workflow:productivity_setup"
        
        # Check for specific actions
        if any(pattern in user_input for pattern in self.nl_patterns['take_screenshot']):
            return "take_screenshot"
        
        if any(pattern in user_input for pattern in self.nl_patterns['system_info']):
            return "execute_workflow:system_health_check"
        
        if any(pattern in user_input for pattern in self.nl_patterns['quick_calculation']):
            return "open_app:calculator"
        
        # If no pattern matches, use AI vision for analysis
        return "ai_analysis"
    
    def execute_action(self, action: str, user_input: str = "") -> str:
        """
        Execute the determined action
        
        Args:
            action: Action to execute
            user_input: Original user input for context
            
        Returns:
            Result message
        """
        try:
            if action.startswith("open_app:"):
                app_name = action.split(":", 1)[1]
                success, method, exec_time = self.app_launcher.launch_app(app_name)
                if success:
                    return f"✅ Opened {app_name} in {exec_time:.2f}s via {method}"
                else:
                    return f"❌ Failed to open {app_name}"
            
            elif action.startswith("execute_workflow:"):
                workflow_name = action.split(":", 1)[1]
                return self.execute_workflow(workflow_name)
            
            elif action == "take_screenshot":
                return self._take_screenshot_task()
            
            elif action == "ai_analysis":
                return self.ai_vision.quick_screen_analysis(user_input)
            
            else:
                return f"❌ Unknown action: {action}"
                
        except Exception as e:
            error_context = self.error_handler.handle_error(e, {'action': action, 'user_input': user_input})
            return f"❌ Error executing action: {error_context.message}"
    
    def execute_workflow(self, workflow_name: str) -> str:
        """
        Execute a complete workflow
        
        Args:
            workflow_name: Name of workflow to execute
            
        Returns:
            Execution summary
        """
        if workflow_name not in self.workflows:
            return f"❌ Workflow '{workflow_name}' not found"
        
        workflow = self.workflows[workflow_name]
        workflow.total_executions += 1
        
        logger.info(f"🚀 Executing workflow: {workflow.description}")
        
        start_time = time.time()
        completed_tasks = 0
        failed_tasks = 0
        
        for task in workflow.tasks:
            task.status = TaskStatus.IN_PROGRESS
            task_start = time.time()
            
            logger.info(f"  📋 {task.name}: {task.description}")
            
            try:
                # Execute task with error handling
                success, result, error_ctx = self.error_handler.safe_execute(
                    task.action, *task.args, **task.kwargs
                )
                
                task.execution_time = time.time() - task_start
                
                if success:
                    task.status = TaskStatus.COMPLETED
                    completed_tasks += 1
                    logger.info(f"    ✅ Completed in {task.execution_time:.2f}s")
                else:
                    task.status = TaskStatus.FAILED
                    task.error_message = error_ctx.message if error_ctx else "Unknown error"
                    failed_tasks += 1
                    logger.warning(f"    ❌ Failed: {task.error_message}")
                    
                    # Retry logic
                    if task.retry_count < task.max_retries:
                        task.retry_count += 1
                        logger.info(f"    🔄 Retrying ({task.retry_count}/{task.max_retries})...")
                        time.sleep(1)
                        
                        success, result, error_ctx = self.error_handler.safe_execute(
                            task.action, *task.args, **task.kwargs
                        )
                        
                        if success:
                            task.status = TaskStatus.COMPLETED
                            completed_tasks += 1
                            failed_tasks -= 1
                            logger.info(f"    ✅ Retry successful")
                        else:
                            logger.warning(f"    ❌ Retry failed")
            
            except Exception as e:
                task.status = TaskStatus.FAILED
                task.error_message = str(e)
                failed_tasks += 1
                logger.error(f"    ❌ Task failed: {e}")
            
            # Brief pause between tasks
            time.sleep(0.5)
        
        # Calculate workflow success
        total_time = time.time() - start_time
        success_rate = (completed_tasks / len(workflow.tasks)) * 100
        
        if success_rate >= 80:  # Consider 80%+ as successful
            workflow.successful_executions += 1
        
        # Update workflow success rate
        workflow.success_rate = (workflow.successful_executions / workflow.total_executions) * 100
        
        # Create summary
        summary = f"""
🎯 Workflow: {workflow.description}
⏱️  Total Time: {total_time:.2f}s
✅ Completed: {completed_tasks}/{len(workflow.tasks)} tasks
❌ Failed: {failed_tasks} tasks
📊 Success Rate: {success_rate:.1f}%
📈 Overall Success Rate: {workflow.success_rate:.1f}%
"""
        
        logger.info(summary)
        return summary.strip()
    
    def _take_screenshot_task(self) -> str:
        """Take a screenshot task"""
        try:
            image_data = self.ai_vision.take_screenshot()
            timestamp = time.strftime("%Y%m%d_%H%M%S")
            filename = f"screenshot_{timestamp}.jpg"
            
            # Save screenshot (simplified - in real implementation, decode and save)
            logger.info(f"📸 Screenshot captured: {filename}")
            return f"✅ Screenshot saved as {filename}"
        except Exception as e:
            return f"❌ Screenshot failed: {e}"
    
    def _check_system_health(self) -> str:
        """Check system health"""
        try:
            error_summary = self.error_handler.get_error_summary()
            return f"✅ System Health: {error_summary['recommendations'][0]}"
        except Exception as e:
            return f"❌ Health check failed: {e}"
    
    def _check_launcher_stats(self) -> str:
        """Check app launcher statistics"""
        try:
            stats = self.app_launcher.get_stats()
            return f"✅ App Launcher: {stats['success_rate']} success rate, {stats['average_time']} avg time"
        except Exception as e:
            return f"❌ Launcher stats failed: {e}"
    
    def _check_vision_stats(self) -> str:
        """Check AI vision statistics"""
        try:
            stats = self.ai_vision.get_stats()
            return f"✅ AI Vision: {stats['success_rate']} success rate, {len(stats['available_providers'])} providers"
        except Exception as e:
            return f"❌ Vision stats failed: {e}"

    def get_workflow_stats(self) -> Dict:
        """Get workflow system statistics"""
        total_workflows = len(self.workflows)
        total_executions = sum(w.total_executions for w in self.workflows.values())
        successful_executions = sum(w.successful_executions for w in self.workflows.values())

        overall_success_rate = (successful_executions / total_executions * 100) if total_executions > 0 else 0

        return {
            'total_workflows': total_workflows,
            'total_executions': total_executions,
            'successful_executions': successful_executions,
            'overall_success_rate': f"{overall_success_rate:.1f}%",
            'available_workflows': list(self.workflows.keys())
        }

    def list_workflows(self) -> List[Dict]:
        """List all available workflows"""
        workflow_list = []
        for name, workflow in self.workflows.items():
            workflow_list.append({
                'name': name,
                'description': workflow.description,
                'category': workflow.category.value,
                'task_count': len(workflow.tasks),
                'success_rate': f"{workflow.success_rate:.1f}%",
                'total_executions': workflow.total_executions
            })

        return sorted(workflow_list, key=lambda x: x['success_rate'], reverse=True)

    def process_user_request(self, user_input: str) -> str:
        """
        Main entry point for processing user requests

        Args:
            user_input: Natural language user request

        Returns:
            Response message
        """
        if not user_input.strip():
            return "❓ Please provide a request. Examples: 'open calculator', 'start morning routine', 'take screenshot'"

        logger.info(f"🎯 Processing request: {user_input}")

        # Determine action from natural language
        action = self.process_natural_language(user_input)

        if action:
            # Execute the determined action
            result = self.execute_action(action, user_input)
            return result
        else:
            return "❓ I didn't understand that request. Try: 'open [app]', 'morning routine', 'take screenshot', or 'system status'"


# Example usage and testing
if __name__ == "__main__":
    workflow_manager = EnhancedWorkflowManager()

    print("🔄 Enhanced Workflow Manager - Agent-S")
    print("=" * 50)

    # Show available workflows
    workflows = workflow_manager.list_workflows()
    print(f"📋 Available Workflows ({len(workflows)}):")
    for workflow in workflows:
        print(f"  • {workflow['name']}: {workflow['description']}")
        print(f"    Category: {workflow['category']}, Tasks: {workflow['task_count']}, Success: {workflow['success_rate']}")

    # Show system stats
    stats = workflow_manager.get_workflow_stats()
    print(f"\n📊 System Statistics:")
    for key, value in stats.items():
        if key != 'available_workflows':
            print(f"  {key}: {value}")

    # Interactive mode
    print("\n💡 Natural Language Interface:")
    print("Examples:")
    print("  • 'Start my morning routine'")
    print("  • 'Open calculator'")
    print("  • 'Take a screenshot'")
    print("  • 'Check system status'")
    print("  • 'Set up productivity tools'")

    while True:
        try:
            user_input = input("\n🎯 What would you like me to do? ").strip()
            if user_input.lower() in ['quit', 'exit', 'q']:
                break

            if user_input:
                print("🔄 Processing...")
                result = workflow_manager.process_user_request(user_input)
                print(f"\n{result}")
        except KeyboardInterrupt:
            break

    # Final statistics
    print(f"\n📊 Final Statistics:")
    final_stats = workflow_manager.get_workflow_stats()
    for key, value in final_stats.items():
        if key != 'available_workflows':
            print(f"  {key}: {value}")

    print("\n👋 Goodbye!")
