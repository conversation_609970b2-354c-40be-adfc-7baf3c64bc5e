#!/usr/bin/env python3
"""
Agent-S CLI with Ollama Local Models
Command-line interface for Agent-S using local Ollama models
"""

import argparse
import datetime
import io
import logging
import os
import platform
import pyautogui
import sys
import time
from PIL import Image
from gui_agents.s2.agents.grounding import OSWorldACI
from gui_agents.s2.agents.agent_s import AgentS2

# Configuration
OLLAMA_BASE_URL = "http://localhost:11434/v1/"
OLLAMA_API_KEY = "not-needed"

# Set up logging
current_platform = platform.system().lower()
logger = logging.getLogger()
logger.setLevel(logging.INFO)

datetime_str = datetime.datetime.now().strftime("%Y%m%d@%H%M%S")
log_dir = "logs"
os.makedirs(log_dir, exist_ok=True)

file_handler = logging.FileHandler(
    os.path.join("logs", f"ollama-{datetime_str}.log"), encoding="utf-8"
)
stdout_handler = logging.StreamHandler(sys.stdout)

formatter = logging.Formatter(
    fmt="[%(asctime)s %(levelname)s] %(message)s"
)
file_handler.setFormatter(formatter)
stdout_handler.setFormatter(formatter)

logger.addHandler(file_handler)
logger.addHandler(stdout_handler)

def scale_screen_dimensions(width: int, height: int, max_dim_size: int = 2400):
    """Scale screen dimensions for optimal processing"""
    scale_factor = min(max_dim_size / width, max_dim_size / height, 1)
    safe_width = int(width * scale_factor)
    safe_height = int(height * scale_factor)
    return safe_width, safe_height

def run_agent(agent, instruction: str, scaled_width: int, scaled_height: int):
    """Run agent with given instruction"""
    obs = {}
    traj = f"Task:\n{instruction}"
    subtask_traj = ""
    
    for iteration in range(15):
        logger.info(f"Iteration {iteration}")
        
        # Get screenshot
        screenshot = pyautogui.screenshot()
        screenshot = screenshot.resize((scaled_width, scaled_height), Image.LANCZOS)
        
        # Convert to bytes
        buffered = io.BytesIO()
        screenshot.save(buffered, format="PNG")
        screenshot_bytes = buffered.getvalue()
        obs["screenshot"] = screenshot_bytes
        
        # Get next action
        try:
            info, code = agent.predict(instruction=instruction, observation=obs)
            
            if not code or len(code) == 0:
                logger.warning("No action generated")
                continue
                
            action = code[0].lower()
            
            if "done" in action or "fail" in action:
                logger.info("Task completed")
                # Show completion dialog
                if platform.system() == "Windows":
                    os.system('msg * "Task Completed"')
                agent.update_narrative_memory(traj)
                break
                
            if "next" in action:
                continue
                
            if "wait" in action:
                time.sleep(5)
                continue
                
            # Execute action
            time.sleep(1.0)
            logger.info(f"Executing: {code[0]}")
            
            try:
                exec(code[0])
                time.sleep(1.0)
                
                # Update trajectories
                traj += (
                    f"\n\nReflection:\n{info['reflection']}"
                    f"\n\n----------------------\n\nPlan:\n{info['executor_plan']}"
                )
                subtask_traj = agent.update_episodic_memory(info, subtask_traj)
                
            except Exception as e:
                logger.error(f"Action execution failed: {e}")
                
        except Exception as e:
            logger.error(f"Agent prediction failed: {e}")
            break

def main():
    parser = argparse.ArgumentParser(description="Run Agent-S with Ollama local models")
    parser.add_argument(
        "--model", 
        type=str, 
        default="llava:13b",
        help="Ollama model to use (default: llava:13b)"
    )
    parser.add_argument(
        "--instruction",
        type=str,
        help="Single instruction to execute"
    )
    parser.add_argument(
        "--base_url",
        type=str,
        default=OLLAMA_BASE_URL,
        help=f"Ollama base URL (default: {OLLAMA_BASE_URL})"
    )
    
    args = parser.parse_args()
    
    print("🚀 AGENT-S + OLLAMA LOCAL MODELS")
    print("=" * 50)
    print(f"🤖 Model: {args.model}")
    print(f"🔗 Base URL: {args.base_url}")
    
    # Get screen dimensions
    screen_width, screen_height = pyautogui.size()
    scaled_width, scaled_height = scale_screen_dimensions(screen_width, screen_height)
    
    print(f"🖥️  Screen: {screen_width}x{screen_height}")
    print(f"📏 Scaled: {scaled_width}x{scaled_height}")
    
    # Engine parameters
    engine_params = {
        "engine_type": "openai",
        "model": args.model,
        "base_url": args.base_url,
        "api_key": OLLAMA_API_KEY,
    }
    
    # Grounding engine parameters
    engine_params_for_grounding = {
        "engine_type": "openai",
        "model": args.model,
        "base_url": args.base_url,
        "api_key": OLLAMA_API_KEY,
        "grounding_width": scaled_width,
        "grounding_height": scaled_height,
    }
    
    try:
        print("\n🔧 Initializing Agent-S...")
        
        # Initialize grounding agent
        grounding_agent = OSWorldACI(
            platform=current_platform,
            engine_params_for_generation=engine_params,
            engine_params_for_grounding=engine_params_for_grounding,
            width=screen_width,
            height=screen_height,
        )
        
        # Initialize Agent-S2
        agent = AgentS2(
            engine_params,
            grounding_agent,
            platform=current_platform,
            action_space="pyautogui",
            observation_type="mixed",
            search_engine=None,
            embedding_engine_type=None  # Disable embedding for local setup
        )
        
        print("✅ Agent-S initialized successfully!")
        
        # Single instruction mode
        if args.instruction:
            print(f"\n🎯 Executing: {args.instruction}")
            agent.reset()
            run_agent(agent, args.instruction, scaled_width, scaled_height)
            return
        
        # Interactive mode
        print("\n🎯 Interactive Mode - Enter your commands:")
        print("Type 'quit' to exit")
        
        while True:
            try:
                query = input("\n🤔 Command: ").strip()
                
                if query.lower() in ['quit', 'exit', 'q']:
                    print("👋 Goodbye!")
                    break
                    
                if not query:
                    continue
                    
                print(f"🔄 Processing: {query}")
                agent.reset()
                run_agent(agent, query, scaled_width, scaled_height)
                
            except KeyboardInterrupt:
                print("\n👋 Goodbye!")
                break
                
    except Exception as e:
        logger.error(f"Failed to initialize Agent-S: {e}")
        print("\n🔍 TROUBLESHOOTING:")
        print("1. Make sure Ollama server is running: ollama serve")
        print("2. Check model exists: ollama list")
        print("3. Test connection: python test_ollama_connection.py")

if __name__ == "__main__":
    main() 