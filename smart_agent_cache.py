#!/usr/bin/env python3
"""
Smart Agent with Action Caching
Pre-learns common actions for instant execution
"""

import pyautogui
import time
import json
import os
from pathlib import Path

class SmartActionCache:
    def __init__(self):
        self.cache_file = "action_cache.json"
        self.actions = self.load_cache()
        self.screen_width, self.screen_height = pyautogui.size()
        
        # Speed optimizations
        pyautogui.PAUSE = 0.05  # Super fast
        pyautogui.FAILSAFE = True
        
    def load_cache(self):
        """Load pre-learned actions"""
        if os.path.exists(self.cache_file):
            with open(self.cache_file, 'r') as f:
                return json.load(f)
        return {}
    
    def save_cache(self):
        """Save learned actions"""
        with open(self.cache_file, 'w') as f:
            json.dump(self.actions, f, indent=2)
    
    def learn_screen_positions(self):
        """Learn common UI element positions on this screen"""
        print("🧠 Learning your screen layout...")
        
        # Learn Start button position
        start_positions = [
            (10, self.screen_height - 10),   # Bottom-left corner
            (50, self.screen_height - 50),   # Slightly inward
            (30, self.screen_height - 30),   # Alternative
        ]
        
        self.actions["start_button"] = start_positions[0]
        
        # Learn taskbar positions
        taskbar_y = self.screen_height - 40
        self.actions["taskbar"] = {
            "y": taskbar_y,
            "left": 100,
            "center": self.screen_width // 2,
            "right": self.screen_width - 100
        }
        
        # Common app shortcuts
        self.actions["shortcuts"] = {
            "calculator": ["win", "r", "calc", "enter"],
            "notepad": ["win", "r", "notepad", "enter"],
            "browser": ["win", "r", "msedge", "enter"],
            "excel": ["win", "r", "excel", "enter"],
            "word": ["win", "r", "winword", "enter"]
        }
        
        self.save_cache()
        print("✅ Screen layout learned!")
    
    def instant_open_app(self, app_name):
        """Instantly open apps using cached shortcuts"""
        app_name = app_name.lower()
        
        if app_name in self.actions.get("shortcuts", {}):
            shortcut = self.actions["shortcuts"][app_name]
            
            # Execute shortcut sequence
            if len(shortcut) == 4:  # win+r pattern
                pyautogui.hotkey(shortcut[0], shortcut[1])
                time.sleep(0.2)
                pyautogui.typewrite(shortcut[2])
                time.sleep(0.1)
                pyautogui.press(shortcut[3])
                print(f"⚡ Opened {app_name} in 0.5s")
                return True
        
        # Fallback to Start menu
        return self.fallback_open_app(app_name)
    
    def fallback_open_app(self, app_name):
        """Fallback method using Start menu"""
        start_pos = self.actions.get("start_button", (50, self.screen_height - 50))
        
        pyautogui.click(start_pos[0], start_pos[1])
        time.sleep(0.3)
        pyautogui.typewrite(app_name)
        time.sleep(0.2)
        pyautogui.press("enter")
        print(f"✅ Opened {app_name} via Start menu")
        return True
    
    def smart_click_detection(self, description):
        """Smart clicking based on common UI patterns"""
        desc = description.lower()
        
        # Common button patterns
        if "start" in desc or "windows" in desc:
            pos = self.actions.get("start_button", (50, self.screen_height - 50))
            pyautogui.click(pos[0], pos[1])
            return True
            
        elif "taskbar" in desc:
            taskbar = self.actions.get("taskbar", {})
            y = taskbar.get("y", self.screen_height - 40)
            x = taskbar.get("center", self.screen_width // 2)
            pyautogui.click(x, y)
            return True
            
        elif "close" in desc or "x" in desc:
            # Top-right close button
            pyautogui.click(self.screen_width - 20, 20)
            return True
            
        elif "minimize" in desc:
            # Minimize button
            pyautogui.click(self.screen_width - 60, 20)
            return True
        
        return False

class BusinessWorkflows:
    def __init__(self, cache):
        self.cache = cache
    
    def morning_routine(self):
        """Optimized morning startup"""
        print("🌅 Starting morning routine...")
        
        # Open apps in parallel-ish (fast sequence)
        apps = ["browser", "excel", "notepad"]
        for app in apps:
            self.cache.instant_open_app(app)
            time.sleep(1)  # Brief pause between apps
        
        print("✅ Morning routine complete!")
    
    def quick_calculation(self, expression):
        """Fast calculator with result"""
        self.cache.instant_open_app("calculator")
        time.sleep(1)
        
        # Type expression
        pyautogui.typewrite(str(expression))
        pyautogui.press("enter")
        print(f"🧮 Calculated: {expression}")
    
    def quick_note(self, text):
        """Fast note taking"""
        self.cache.instant_open_app("notepad")
        time.sleep(1)
        pyautogui.typewrite(text)
        print(f"📝 Note created: {text[:30]}...")

def main():
    print("🚀 SMART AGENT WITH CACHING")
    print("=" * 40)
    
    # Initialize smart cache
    cache = SmartActionCache()
    workflows = BusinessWorkflows(cache)
    
    # Learn screen layout if first time
    if not cache.actions:
        cache.learn_screen_positions()
    
    print("\n⚡ INSTANT COMMANDS:")
    print("- 'open [app]' - Instant app opening")
    print("- 'calc [expression]' - Quick calculation")
    print("- 'note [text]' - Quick note")
    print("- 'morning' - Morning routine")
    print("- 'learn' - Re-learn screen layout")
    print("- 'quit' - Exit")
    
    while True:
        try:
            command = input("\n🗣️  Command: ").strip().lower()
            
            if command == 'quit':
                break
            elif command == 'learn':
                cache.learn_screen_positions()
            elif command == 'morning':
                workflows.morning_routine()
            elif command.startswith('open '):
                app = command[5:]
                cache.instant_open_app(app)
            elif command.startswith('calc '):
                expr = command[5:]
                workflows.quick_calculation(expr)
            elif command.startswith('note '):
                text = command[5:]
                workflows.quick_note(text)
            else:
                print("❌ Unknown command")
                
        except KeyboardInterrupt:
            print("\n👋 Goodbye!")
            break
        except Exception as e:
            print(f"❌ Error: {e}")

if __name__ == "__main__":
    main()
