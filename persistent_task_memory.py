#!/usr/bin/env python3
"""
Persistent Task Memory System for Agent-S
Remembers context across long tasks, maintains state, and enables task resumption
"""

import os
import sys
import json
import time
import pickle
import sqlite3
import hashlib
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass, asdict
from enum import Enum
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class TaskState(Enum):
    """Task execution states"""
    PLANNED = "planned"
    IN_PROGRESS = "in_progress"
    PAUSED = "paused"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"

class MemoryType(Enum):
    """Types of memory stored"""
    TASK_CONTEXT = "task_context"
    SCREEN_STATE = "screen_state"
    USER_PREFERENCE = "user_preference"
    EXECUTION_HISTORY = "execution_history"
    ERROR_PATTERN = "error_pattern"
    SUCCESS_PATTERN = "success_pattern"

@dataclass
class TaskMemory:
    """Individual task memory entry"""
    task_id: str
    task_name: str
    user_request: str
    current_state: TaskState
    context_data: Dict[str, Any]
    execution_steps: List[Dict]
    completed_steps: List[str]
    failed_steps: List[str]
    current_step_index: int
    start_time: datetime
    last_update: datetime
    pause_time: Optional[datetime] = None
    resume_count: int = 0
    error_history: List[Dict] = None
    success_metrics: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.error_history is None:
            self.error_history = []
        if self.success_metrics is None:
            self.success_metrics = {}

@dataclass
class ContextSnapshot:
    """Snapshot of execution context at a point in time"""
    timestamp: datetime
    screen_state: Dict[str, Any]
    application_states: Dict[str, Any]
    cursor_position: Tuple[int, int]
    active_windows: List[str]
    clipboard_content: str
    environment_variables: Dict[str, str]
    memory_usage: float
    cpu_usage: float

class PersistentTaskMemory:
    """
    Persistent Task Memory System
    
    Features:
    - Long-term task context storage
    - State persistence across sessions
    - Task resumption capabilities
    - Pattern learning and optimization
    - Context-aware decision making
    """
    
    def __init__(self, memory_dir: str = "agent_memory"):
        self.memory_dir = memory_dir
        self.db_path = os.path.join(memory_dir, "task_memory.db")
        self.context_dir = os.path.join(memory_dir, "contexts")
        self.snapshots_dir = os.path.join(memory_dir, "snapshots")
        
        # Create directories
        os.makedirs(memory_dir, exist_ok=True)
        os.makedirs(self.context_dir, exist_ok=True)
        os.makedirs(self.snapshots_dir, exist_ok=True)
        
        # Initialize database
        self._init_database()
        
        # Active memory cache
        self.active_tasks: Dict[str, TaskMemory] = {}
        self.context_cache: Dict[str, Any] = {}
        self.pattern_cache: Dict[str, List[Dict]] = {}
        
        # Memory management
        self.max_active_tasks = 10
        self.max_context_age_hours = 24
        self.auto_save_interval = 30  # seconds
        
        logger.info(f"🧠 Persistent Task Memory initialized at {memory_dir}")
    
    def _init_database(self):
        """Initialize SQLite database for persistent storage"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # Task memories table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS task_memories (
                    task_id TEXT PRIMARY KEY,
                    task_name TEXT NOT NULL,
                    user_request TEXT NOT NULL,
                    current_state TEXT NOT NULL,
                    context_data TEXT,
                    execution_steps TEXT,
                    completed_steps TEXT,
                    failed_steps TEXT,
                    current_step_index INTEGER,
                    start_time TEXT,
                    last_update TEXT,
                    pause_time TEXT,
                    resume_count INTEGER DEFAULT 0,
                    error_history TEXT,
                    success_metrics TEXT
                )
            ''')
            
            # Context snapshots table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS context_snapshots (
                    snapshot_id TEXT PRIMARY KEY,
                    task_id TEXT,
                    timestamp TEXT,
                    screen_state TEXT,
                    application_states TEXT,
                    cursor_position TEXT,
                    active_windows TEXT,
                    clipboard_content TEXT,
                    environment_variables TEXT,
                    memory_usage REAL,
                    cpu_usage REAL,
                    FOREIGN KEY (task_id) REFERENCES task_memories (task_id)
                )
            ''')
            
            # Memory patterns table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS memory_patterns (
                    pattern_id TEXT PRIMARY KEY,
                    pattern_type TEXT NOT NULL,
                    pattern_data TEXT NOT NULL,
                    frequency INTEGER DEFAULT 1,
                    success_rate REAL DEFAULT 0.0,
                    last_used TEXT,
                    created_time TEXT
                )
            ''')
            
            # User preferences table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS user_preferences (
                    preference_key TEXT PRIMARY KEY,
                    preference_value TEXT NOT NULL,
                    preference_type TEXT NOT NULL,
                    last_updated TEXT
                )
            ''')
            
            conn.commit()
            conn.close()
            
            logger.info("✅ Database initialized successfully")
            
        except Exception as e:
            logger.error(f"❌ Database initialization failed: {e}")
            raise
    
    def create_task_memory(self, task_id: str, task_name: str, user_request: str, 
                          execution_steps: List[Dict]) -> TaskMemory:
        """Create a new task memory"""
        task_memory = TaskMemory(
            task_id=task_id,
            task_name=task_name,
            user_request=user_request,
            current_state=TaskState.PLANNED,
            context_data={},
            execution_steps=execution_steps,
            completed_steps=[],
            failed_steps=[],
            current_step_index=0,
            start_time=datetime.now(),
            last_update=datetime.now()
        )
        
        # Store in active memory
        self.active_tasks[task_id] = task_memory
        
        # Persist to database
        self._save_task_memory(task_memory)
        
        logger.info(f"🧠 Created task memory: {task_name} ({task_id})")
        return task_memory
    
    def update_task_context(self, task_id: str, context_updates: Dict[str, Any]):
        """Update task context with new information"""
        if task_id in self.active_tasks:
            task_memory = self.active_tasks[task_id]
            task_memory.context_data.update(context_updates)
            task_memory.last_update = datetime.now()
            
            # Save to database
            self._save_task_memory(task_memory)
            
            logger.debug(f"🔄 Updated context for task {task_id}")
    
    def mark_step_completed(self, task_id: str, step_id: str, result: Any = None):
        """Mark a step as completed and store result"""
        if task_id in self.active_tasks:
            task_memory = self.active_tasks[task_id]
            
            if step_id not in task_memory.completed_steps:
                task_memory.completed_steps.append(step_id)
                
                # Store step result in context
                if result is not None:
                    task_memory.context_data[f"step_{step_id}_result"] = result
                
                # Update current step index
                task_memory.current_step_index += 1
                task_memory.last_update = datetime.now()
                
                # Save to database
                self._save_task_memory(task_memory)
                
                logger.info(f"✅ Step {step_id} completed for task {task_id}")
    
    def mark_step_failed(self, task_id: str, step_id: str, error: str):
        """Mark a step as failed and store error information"""
        if task_id in self.active_tasks:
            task_memory = self.active_tasks[task_id]
            
            if step_id not in task_memory.failed_steps:
                task_memory.failed_steps.append(step_id)
                
                # Store error information
                error_entry = {
                    'step_id': step_id,
                    'error': error,
                    'timestamp': datetime.now().isoformat(),
                    'retry_count': task_memory.error_history.count(step_id)
                }
                task_memory.error_history.append(error_entry)
                task_memory.last_update = datetime.now()
                
                # Save to database
                self._save_task_memory(task_memory)
                
                logger.warning(f"❌ Step {step_id} failed for task {task_id}: {error}")
    
    def pause_task(self, task_id: str, reason: str = "User requested"):
        """Pause a task and save current state"""
        if task_id in self.active_tasks:
            task_memory = self.active_tasks[task_id]
            task_memory.current_state = TaskState.PAUSED
            task_memory.pause_time = datetime.now()
            task_memory.last_update = datetime.now()
            
            # Store pause reason in context
            task_memory.context_data['pause_reason'] = reason
            task_memory.context_data['pause_timestamp'] = datetime.now().isoformat()
            
            # Take context snapshot
            self.take_context_snapshot(task_id)
            
            # Save to database
            self._save_task_memory(task_memory)
            
            logger.info(f"⏸️ Task {task_id} paused: {reason}")
    
    def resume_task(self, task_id: str) -> Optional[TaskMemory]:
        """Resume a paused task"""
        # Load from database if not in active memory
        if task_id not in self.active_tasks:
            task_memory = self._load_task_memory(task_id)
            if task_memory:
                self.active_tasks[task_id] = task_memory
        
        if task_id in self.active_tasks:
            task_memory = self.active_tasks[task_id]
            
            if task_memory.current_state == TaskState.PAUSED:
                task_memory.current_state = TaskState.IN_PROGRESS
                task_memory.resume_count += 1
                task_memory.last_update = datetime.now()
                
                # Calculate pause duration
                if task_memory.pause_time:
                    pause_duration = datetime.now() - task_memory.pause_time
                    task_memory.context_data['last_pause_duration'] = pause_duration.total_seconds()
                    task_memory.pause_time = None
                
                # Save to database
                self._save_task_memory(task_memory)
                
                logger.info(f"▶️ Task {task_id} resumed (resume count: {task_memory.resume_count})")
                return task_memory
        
        logger.warning(f"❌ Could not resume task {task_id}")
        return None
    
    def get_task_context(self, task_id: str) -> Optional[Dict[str, Any]]:
        """Get current context for a task"""
        if task_id in self.active_tasks:
            return self.active_tasks[task_id].context_data.copy()
        
        # Try loading from database
        task_memory = self._load_task_memory(task_id)
        if task_memory:
            return task_memory.context_data.copy()
        
        return None
    
    def get_next_step(self, task_id: str) -> Optional[Dict]:
        """Get the next step to execute for a task"""
        if task_id in self.active_tasks:
            task_memory = self.active_tasks[task_id]
            
            if task_memory.current_step_index < len(task_memory.execution_steps):
                next_step = task_memory.execution_steps[task_memory.current_step_index]
                
                # Enrich step with context
                next_step['context'] = task_memory.context_data.copy()
                next_step['completed_steps'] = task_memory.completed_steps.copy()
                next_step['failed_steps'] = task_memory.failed_steps.copy()
                
                return next_step
        
        return None
    
    def take_context_snapshot(self, task_id: str) -> str:
        """Take a snapshot of current execution context"""
        try:
            import pyautogui
            import psutil
            
            # Get current system state
            cursor_pos = pyautogui.position()
            
            # Get running applications
            active_windows = []
            try:
                for proc in psutil.process_iter(['name']):
                    if proc.info['name']:
                        active_windows.append(proc.info['name'])
            except:
                pass
            
            # Get system metrics
            memory_usage = psutil.virtual_memory().percent
            cpu_usage = psutil.cpu_percent()
            
            # Create snapshot
            snapshot = ContextSnapshot(
                timestamp=datetime.now(),
                screen_state={'cursor_position': cursor_pos},
                application_states={'active_windows': active_windows[:10]},
                cursor_position=cursor_pos,
                active_windows=active_windows[:10],
                clipboard_content="",  # Could implement clipboard reading
                environment_variables={},
                memory_usage=memory_usage,
                cpu_usage=cpu_usage
            )
            
            # Generate snapshot ID
            snapshot_id = hashlib.md5(f"{task_id}_{datetime.now().isoformat()}".encode()).hexdigest()
            
            # Save to database
            self._save_context_snapshot(snapshot_id, task_id, snapshot)
            
            logger.debug(f"📸 Context snapshot taken: {snapshot_id}")
            return snapshot_id
            
        except Exception as e:
            logger.error(f"❌ Failed to take context snapshot: {e}")
            return ""

    def _save_task_memory(self, task_memory: TaskMemory):
        """Save task memory to database"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            cursor.execute('''
                INSERT OR REPLACE INTO task_memories
                (task_id, task_name, user_request, current_state, context_data,
                 execution_steps, completed_steps, failed_steps, current_step_index,
                 start_time, last_update, pause_time, resume_count, error_history, success_metrics)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                task_memory.task_id,
                task_memory.task_name,
                task_memory.user_request,
                task_memory.current_state.value,
                json.dumps(task_memory.context_data),
                json.dumps(task_memory.execution_steps),
                json.dumps(task_memory.completed_steps),
                json.dumps(task_memory.failed_steps),
                task_memory.current_step_index,
                task_memory.start_time.isoformat(),
                task_memory.last_update.isoformat(),
                task_memory.pause_time.isoformat() if task_memory.pause_time else None,
                task_memory.resume_count,
                json.dumps(task_memory.error_history),
                json.dumps(task_memory.success_metrics)
            ))

            conn.commit()
            conn.close()

        except Exception as e:
            logger.error(f"❌ Failed to save task memory: {e}")

    def _load_task_memory(self, task_id: str) -> Optional[TaskMemory]:
        """Load task memory from database"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            cursor.execute('SELECT * FROM task_memories WHERE task_id = ?', (task_id,))
            row = cursor.fetchone()
            conn.close()

            if row:
                return TaskMemory(
                    task_id=row[0],
                    task_name=row[1],
                    user_request=row[2],
                    current_state=TaskState(row[3]),
                    context_data=json.loads(row[4]) if row[4] else {},
                    execution_steps=json.loads(row[5]) if row[5] else [],
                    completed_steps=json.loads(row[6]) if row[6] else [],
                    failed_steps=json.loads(row[7]) if row[7] else [],
                    current_step_index=row[8],
                    start_time=datetime.fromisoformat(row[9]),
                    last_update=datetime.fromisoformat(row[10]),
                    pause_time=datetime.fromisoformat(row[11]) if row[11] else None,
                    resume_count=row[12],
                    error_history=json.loads(row[13]) if row[13] else [],
                    success_metrics=json.loads(row[14]) if row[14] else {}
                )

            return None

        except Exception as e:
            logger.error(f"❌ Failed to load task memory: {e}")
            return None

    def _save_context_snapshot(self, snapshot_id: str, task_id: str, snapshot: ContextSnapshot):
        """Save context snapshot to database"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            cursor.execute('''
                INSERT OR REPLACE INTO context_snapshots
                (snapshot_id, task_id, timestamp, screen_state, application_states,
                 cursor_position, active_windows, clipboard_content, environment_variables,
                 memory_usage, cpu_usage)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                snapshot_id,
                task_id,
                snapshot.timestamp.isoformat(),
                json.dumps(snapshot.screen_state),
                json.dumps(snapshot.application_states),
                json.dumps(snapshot.cursor_position),
                json.dumps(snapshot.active_windows),
                snapshot.clipboard_content,
                json.dumps(snapshot.environment_variables),
                snapshot.memory_usage,
                snapshot.cpu_usage
            ))

            conn.commit()
            conn.close()

        except Exception as e:
            logger.error(f"❌ Failed to save context snapshot: {e}")

    def learn_pattern(self, pattern_type: MemoryType, pattern_data: Dict, success: bool):
        """Learn from execution patterns"""
        try:
            pattern_id = hashlib.md5(json.dumps(pattern_data, sort_keys=True).encode()).hexdigest()

            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # Check if pattern exists
            cursor.execute('SELECT frequency, success_rate FROM memory_patterns WHERE pattern_id = ?', (pattern_id,))
            row = cursor.fetchone()

            if row:
                # Update existing pattern
                frequency = row[0] + 1
                current_success_rate = row[1]
                new_success_rate = ((current_success_rate * (frequency - 1)) + (1 if success else 0)) / frequency

                cursor.execute('''
                    UPDATE memory_patterns
                    SET frequency = ?, success_rate = ?, last_used = ?
                    WHERE pattern_id = ?
                ''', (frequency, new_success_rate, datetime.now().isoformat(), pattern_id))
            else:
                # Create new pattern
                cursor.execute('''
                    INSERT INTO memory_patterns
                    (pattern_id, pattern_type, pattern_data, frequency, success_rate, last_used, created_time)
                    VALUES (?, ?, ?, ?, ?, ?, ?)
                ''', (
                    pattern_id,
                    pattern_type.value,
                    json.dumps(pattern_data),
                    1,
                    1.0 if success else 0.0,
                    datetime.now().isoformat(),
                    datetime.now().isoformat()
                ))

            conn.commit()
            conn.close()

            logger.debug(f"📚 Pattern learned: {pattern_type.value} ({'success' if success else 'failure'})")

        except Exception as e:
            logger.error(f"❌ Failed to learn pattern: {e}")

    def get_similar_patterns(self, pattern_type: MemoryType, context: Dict, limit: int = 5) -> List[Dict]:
        """Get similar successful patterns for guidance"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            cursor.execute('''
                SELECT pattern_data, success_rate, frequency, last_used
                FROM memory_patterns
                WHERE pattern_type = ? AND success_rate > 0.5
                ORDER BY success_rate DESC, frequency DESC
                LIMIT ?
            ''', (pattern_type.value, limit))

            patterns = []
            for row in cursor.fetchall():
                pattern_data = json.loads(row[0])
                patterns.append({
                    'pattern_data': pattern_data,
                    'success_rate': row[1],
                    'frequency': row[2],
                    'last_used': row[3],
                    'similarity': self._calculate_similarity(context, pattern_data)
                })

            conn.close()

            # Sort by similarity
            patterns.sort(key=lambda x: x['similarity'], reverse=True)

            return patterns

        except Exception as e:
            logger.error(f"❌ Failed to get similar patterns: {e}")
            return []

    def _calculate_similarity(self, context1: Dict, context2: Dict) -> float:
        """Calculate similarity between two contexts"""
        try:
            # Simple similarity based on common keys and values
            keys1 = set(context1.keys())
            keys2 = set(context2.keys())

            common_keys = keys1.intersection(keys2)
            if not common_keys:
                return 0.0

            matching_values = 0
            for key in common_keys:
                if context1[key] == context2[key]:
                    matching_values += 1

            return matching_values / len(common_keys)

        except Exception:
            return 0.0

    def get_task_history(self, limit: int = 20) -> List[Dict]:
        """Get recent task history"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            cursor.execute('''
                SELECT task_id, task_name, user_request, current_state, start_time, last_update
                FROM task_memories
                ORDER BY last_update DESC
                LIMIT ?
            ''', (limit,))

            history = []
            for row in cursor.fetchall():
                history.append({
                    'task_id': row[0],
                    'task_name': row[1],
                    'user_request': row[2],
                    'current_state': row[3],
                    'start_time': row[4],
                    'last_update': row[5]
                })

            conn.close()
            return history

        except Exception as e:
            logger.error(f"❌ Failed to get task history: {e}")
            return []

    def cleanup_old_memories(self, days_old: int = 30):
        """Clean up old memories to save space"""
        try:
            cutoff_date = datetime.now() - timedelta(days=days_old)

            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # Delete old completed tasks
            cursor.execute('''
                DELETE FROM task_memories
                WHERE current_state = 'completed' AND last_update < ?
            ''', (cutoff_date.isoformat(),))

            # Delete old snapshots
            cursor.execute('''
                DELETE FROM context_snapshots
                WHERE timestamp < ?
            ''', (cutoff_date.isoformat(),))

            # Delete unused patterns
            cursor.execute('''
                DELETE FROM memory_patterns
                WHERE frequency = 1 AND last_used < ?
            ''', (cutoff_date.isoformat(),))

            deleted_tasks = cursor.rowcount
            conn.commit()
            conn.close()

            logger.info(f"🧹 Cleaned up {deleted_tasks} old memories")

        except Exception as e:
            logger.error(f"❌ Memory cleanup failed: {e}")

    def get_memory_stats(self) -> Dict:
        """Get memory system statistics"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # Count tasks by state
            cursor.execute('SELECT current_state, COUNT(*) FROM task_memories GROUP BY current_state')
            task_counts = dict(cursor.fetchall())

            # Count patterns by type
            cursor.execute('SELECT pattern_type, COUNT(*) FROM memory_patterns GROUP BY pattern_type')
            pattern_counts = dict(cursor.fetchall())

            # Count snapshots
            cursor.execute('SELECT COUNT(*) FROM context_snapshots')
            snapshot_count = cursor.fetchone()[0]

            # Average success rate
            cursor.execute('SELECT AVG(success_rate) FROM memory_patterns')
            avg_success_rate = cursor.fetchone()[0] or 0.0

            conn.close()

            return {
                'active_tasks': len(self.active_tasks),
                'total_tasks': sum(task_counts.values()),
                'task_counts': task_counts,
                'pattern_counts': pattern_counts,
                'snapshot_count': snapshot_count,
                'average_success_rate': avg_success_rate,
                'memory_cache_size': len(self.context_cache)
            }

        except Exception as e:
            logger.error(f"❌ Failed to get memory stats: {e}")
            return {}
