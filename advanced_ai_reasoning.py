#!/usr/bin/env python3
"""
Advanced AI Reasoning System for Agent-S
Provides sophisticated cognitive capabilities for task planning, decision making, and adaptive execution
"""

import os
import sys
import json
import time
import base64
import requests
from typing import Dict, List, Any, Optional, Tuple, Union
from dataclasses import dataclass, asdict
from enum import Enum
import logging
from datetime import datetime
import threading
import queue

# Set OpenAI API key
os.environ['OPENAI_API_KEY'] = '********************************************************************************************************************************************************************'

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class ReasoningType(Enum):
    """Types of AI reasoning"""
    STRATEGIC_PLANNING = "strategic_planning"
    TACTICAL_EXECUTION = "tactical_execution"
    PROBLEM_SOLVING = "problem_solving"
    DECISION_MAKING = "decision_making"
    ADAPTIVE_LEARNING = "adaptive_learning"
    CAUSAL_ANALYSIS = "causal_analysis"
    PREDICTIVE_MODELING = "predictive_modeling"
    CREATIVE_SYNTHESIS = "creative_synthesis"

class ConfidenceLevel(Enum):
    """Confidence levels for AI decisions"""
    VERY_HIGH = "very_high"  # 90-100%
    HIGH = "high"           # 75-89%
    MEDIUM = "medium"       # 50-74%
    LOW = "low"            # 25-49%
    VERY_LOW = "very_low"  # 0-24%

@dataclass
class ReasoningContext:
    """Context for AI reasoning operations"""
    task_description: str
    current_state: Dict[str, Any]
    available_tools: List[str]
    constraints: List[str]
    objectives: List[str]
    time_limit: Optional[int] = None
    complexity_level: str = "medium"
    user_preferences: Dict[str, Any] = None
    historical_data: List[Dict] = None
    
    def __post_init__(self):
        if self.user_preferences is None:
            self.user_preferences = {}
        if self.historical_data is None:
            self.historical_data = []

@dataclass
class ReasoningResult:
    """Result of AI reasoning operation"""
    reasoning_type: ReasoningType
    decision: Dict[str, Any]
    confidence: ConfidenceLevel
    rationale: str
    alternatives: List[Dict[str, Any]]
    risk_assessment: Dict[str, Any]
    success_probability: float
    execution_plan: List[Dict[str, Any]]
    monitoring_points: List[str]
    fallback_strategies: List[Dict[str, Any]]
    timestamp: datetime
    processing_time: float

class AdvancedAIReasoning:
    """
    Advanced AI Reasoning System
    
    Provides sophisticated cognitive capabilities:
    - Strategic task planning and decomposition
    - Real-time decision making under uncertainty
    - Adaptive execution based on feedback
    - Causal analysis and predictive modeling
    - Creative problem solving
    - Multi-objective optimization
    """
    
    def __init__(self):
        self.openai_key = os.getenv('OPENAI_API_KEY')
        if not self.openai_key:
            raise ValueError("OpenAI API key not found")
        
        # Reasoning capabilities
        self.reasoning_engines = {
            ReasoningType.STRATEGIC_PLANNING: self._strategic_planning,
            ReasoningType.TACTICAL_EXECUTION: self._tactical_execution,
            ReasoningType.PROBLEM_SOLVING: self._problem_solving,
            ReasoningType.DECISION_MAKING: self._decision_making,
            ReasoningType.ADAPTIVE_LEARNING: self._adaptive_learning,
            ReasoningType.CAUSAL_ANALYSIS: self._causal_analysis,
            ReasoningType.PREDICTIVE_MODELING: self._predictive_modeling,
            ReasoningType.CREATIVE_SYNTHESIS: self._creative_synthesis
        }
        
        # Reasoning cache and history
        self.reasoning_cache = {}
        self.reasoning_history = []
        self.performance_metrics = {
            'total_reasonings': 0,
            'successful_reasonings': 0,
            'average_confidence': 0.0,
            'average_processing_time': 0.0
        }
        
        # Advanced reasoning parameters
        self.max_reasoning_depth = 5
        self.confidence_threshold = 0.7
        self.parallel_reasoning = True
        self.learning_enabled = True
        
        logger.info("🧠 Advanced AI Reasoning System initialized")
    
    def reason(self, reasoning_type: ReasoningType, context: ReasoningContext, 
              screen_data: Optional[str] = None) -> ReasoningResult:
        """
        Perform advanced AI reasoning
        
        Args:
            reasoning_type: Type of reasoning to perform
            context: Context and constraints for reasoning
            screen_data: Optional base64 encoded screen data
            
        Returns:
            ReasoningResult with decision, rationale, and execution plan
        """
        start_time = time.time()
        
        logger.info(f"🧠 Starting {reasoning_type.value} reasoning...")
        
        try:
            # Check cache first
            cache_key = self._generate_cache_key(reasoning_type, context)
            if cache_key in self.reasoning_cache:
                cached_result = self.reasoning_cache[cache_key]
                logger.info("💾 Using cached reasoning result")
                return cached_result
            
            # Perform reasoning
            reasoning_engine = self.reasoning_engines[reasoning_type]
            result = reasoning_engine(context, screen_data)
            
            # Calculate processing time
            result.processing_time = time.time() - start_time
            result.timestamp = datetime.now()
            
            # Cache result
            self.reasoning_cache[cache_key] = result
            
            # Update metrics
            self._update_metrics(result)
            
            # Add to history
            self.reasoning_history.append(result)
            
            logger.info(f"✅ Reasoning completed in {result.processing_time:.2f}s")
            logger.info(f"🎯 Confidence: {result.confidence.value}")
            
            return result
            
        except Exception as e:
            logger.error(f"❌ Reasoning failed: {e}")
            # Return fallback result
            return self._create_fallback_result(reasoning_type, context, str(e))
    
    def _strategic_planning(self, context: ReasoningContext, screen_data: Optional[str]) -> ReasoningResult:
        """Strategic planning and high-level task decomposition"""
        
        prompt = f"""
You are an advanced AI strategic planner. Analyze this complex task and create a comprehensive strategic plan.

TASK: {context.task_description}
CURRENT STATE: {json.dumps(context.current_state, indent=2)}
AVAILABLE TOOLS: {', '.join(context.available_tools)}
CONSTRAINTS: {', '.join(context.constraints)}
OBJECTIVES: {', '.join(context.objectives)}
TIME LIMIT: {context.time_limit} minutes
COMPLEXITY: {context.complexity_level}

Perform strategic analysis and planning:

1. SITUATION ANALYSIS
   - Analyze the current state and requirements
   - Identify key challenges and opportunities
   - Assess resource availability and constraints

2. STRATEGIC DECOMPOSITION
   - Break down the task into major phases
   - Identify critical dependencies
   - Determine optimal sequencing

3. RISK ASSESSMENT
   - Identify potential failure points
   - Assess probability and impact of risks
   - Develop mitigation strategies

4. SUCCESS METRICS
   - Define measurable success criteria
   - Identify key performance indicators
   - Set monitoring checkpoints

5. RESOURCE OPTIMIZATION
   - Optimize tool usage and sequencing
   - Identify parallel execution opportunities
   - Plan for resource constraints

Respond in JSON format:
{{
    "strategic_analysis": {{
        "situation_assessment": "detailed analysis",
        "key_challenges": ["challenge1", "challenge2"],
        "opportunities": ["opportunity1", "opportunity2"],
        "critical_success_factors": ["factor1", "factor2"]
    }},
    "execution_strategy": {{
        "phases": [
            {{
                "phase_name": "Phase 1",
                "objectives": ["obj1", "obj2"],
                "duration_estimate": 10,
                "dependencies": [],
                "tools_required": ["tool1", "tool2"],
                "success_criteria": ["criteria1"]
            }}
        ],
        "parallel_opportunities": ["task1", "task2"],
        "critical_path": ["phase1", "phase2"]
    }},
    "risk_management": {{
        "high_risks": [
            {{
                "risk": "description",
                "probability": 0.3,
                "impact": "high",
                "mitigation": "strategy"
            }}
        ],
        "contingency_plans": ["plan1", "plan2"]
    }},
    "success_probability": 0.85,
    "confidence_level": "high",
    "rationale": "detailed explanation of the strategic approach"
}}
"""
        
        response = self._call_openai_reasoning(prompt, screen_data)
        return self._parse_reasoning_response(ReasoningType.STRATEGIC_PLANNING, response, context)
    
    def _tactical_execution(self, context: ReasoningContext, screen_data: Optional[str]) -> ReasoningResult:
        """Tactical execution planning and real-time adaptation"""
        
        prompt = f"""
You are an advanced AI tactical execution planner. Focus on immediate, actionable steps.

TASK: {context.task_description}
CURRENT STATE: {json.dumps(context.current_state, indent=2)}
AVAILABLE TOOLS: {', '.join(context.available_tools)}

Analyze the current situation and provide tactical execution guidance:

1. IMMEDIATE SITUATION ASSESSMENT
   - What is the current state of execution?
   - What tools/applications are currently available?
   - What is the next logical step?

2. TACTICAL DECISION MAKING
   - What is the best immediate action to take?
   - Which tool should be used and how?
   - What parameters should be used?

3. EXECUTION OPTIMIZATION
   - How can this step be executed most efficiently?
   - What are the potential issues to watch for?
   - How should success be measured?

4. ADAPTIVE PLANNING
   - What should happen after this step?
   - How should the plan adapt based on results?
   - What are the alternative approaches?

Respond in JSON format:
{{
    "immediate_action": {{
        "tool": "tool_name",
        "parameters": {{"param": "value"}},
        "purpose": "why this action",
        "expected_outcome": "what should happen"
    }},
    "execution_details": {{
        "step_by_step": ["step1", "step2", "step3"],
        "timing": "how long this should take",
        "success_indicators": ["indicator1", "indicator2"],
        "failure_indicators": ["indicator1", "indicator2"]
    }},
    "adaptation_strategy": {{
        "success_next_step": "what to do if successful",
        "failure_recovery": "what to do if failed",
        "alternative_approaches": ["approach1", "approach2"]
    }},
    "confidence_level": "high",
    "success_probability": 0.9,
    "rationale": "why this is the best tactical approach"
}}
"""
        
        response = self._call_openai_reasoning(prompt, screen_data)
        return self._parse_reasoning_response(ReasoningType.TACTICAL_EXECUTION, response, context)
    
    def _problem_solving(self, context: ReasoningContext, screen_data: Optional[str]) -> ReasoningResult:
        """Advanced problem solving and creative solutions"""
        
        prompt = f"""
You are an advanced AI problem solver. Analyze this problem and generate creative solutions.

PROBLEM: {context.task_description}
CURRENT STATE: {json.dumps(context.current_state, indent=2)}
CONSTRAINTS: {', '.join(context.constraints)}
AVAILABLE RESOURCES: {', '.join(context.available_tools)}

Apply advanced problem-solving methodologies:

1. PROBLEM ANALYSIS
   - Root cause analysis
   - Problem decomposition
   - Constraint identification
   - Stakeholder impact assessment

2. SOLUTION GENERATION
   - Brainstorm multiple approaches
   - Apply creative thinking techniques
   - Consider unconventional solutions
   - Evaluate feasibility of each option

3. SOLUTION EVALUATION
   - Assess pros and cons
   - Consider implementation complexity
   - Evaluate resource requirements
   - Predict success likelihood

4. IMPLEMENTATION STRATEGY
   - Select optimal solution
   - Plan implementation steps
   - Identify potential obstacles
   - Design monitoring approach

Respond in JSON format:
{{
    "problem_analysis": {{
        "root_causes": ["cause1", "cause2"],
        "key_constraints": ["constraint1", "constraint2"],
        "success_criteria": ["criteria1", "criteria2"]
    }},
    "solution_options": [
        {{
            "solution_name": "Solution 1",
            "approach": "detailed description",
            "pros": ["pro1", "pro2"],
            "cons": ["con1", "con2"],
            "feasibility": 0.8,
            "complexity": "medium",
            "resource_requirements": ["resource1", "resource2"]
        }}
    ],
    "recommended_solution": {{
        "solution": "chosen solution",
        "implementation_steps": ["step1", "step2", "step3"],
        "success_probability": 0.85,
        "risk_factors": ["risk1", "risk2"],
        "monitoring_points": ["checkpoint1", "checkpoint2"]
    }},
    "confidence_level": "high",
    "rationale": "why this solution is optimal"
}}
"""
        
        response = self._call_openai_reasoning(prompt, screen_data)
        return self._parse_reasoning_response(ReasoningType.PROBLEM_SOLVING, response, context)
    
    def _decision_making(self, context: ReasoningContext, screen_data: Optional[str]) -> ReasoningResult:
        """Multi-criteria decision making under uncertainty"""
        
        prompt = f"""
You are an advanced AI decision maker. Analyze this decision scenario and provide optimal choices.

DECISION SCENARIO: {context.task_description}
CURRENT CONTEXT: {json.dumps(context.current_state, indent=2)}
AVAILABLE OPTIONS: {', '.join(context.available_tools)}
CONSTRAINTS: {', '.join(context.constraints)}
OBJECTIVES: {', '.join(context.objectives)}

Apply advanced decision-making frameworks:

1. DECISION ANALYSIS
   - Identify all viable options
   - Define evaluation criteria
   - Assess uncertainty factors
   - Consider stakeholder impacts

2. MULTI-CRITERIA EVALUATION
   - Weight different criteria by importance
   - Score each option against criteria
   - Consider trade-offs and synergies
   - Account for uncertainty and risk

3. SCENARIO PLANNING
   - Consider best-case scenarios
   - Analyze worst-case scenarios
   - Evaluate most likely outcomes
   - Plan for contingencies

4. OPTIMAL CHOICE
   - Select best option based on analysis
   - Justify the decision rationale
   - Plan implementation approach
   - Design monitoring strategy

Respond in JSON format:
{{
    "decision_analysis": {{
        "available_options": ["option1", "option2", "option3"],
        "evaluation_criteria": [
            {{
                "criterion": "effectiveness",
                "weight": 0.4,
                "description": "how well it achieves objectives"
            }}
        ],
        "uncertainty_factors": ["factor1", "factor2"]
    }},
    "option_evaluation": [
        {{
            "option": "Option 1",
            "scores": {{"effectiveness": 0.8, "efficiency": 0.7}},
            "total_score": 0.75,
            "pros": ["pro1", "pro2"],
            "cons": ["con1", "con2"],
            "risk_level": "medium"
        }}
    ],
    "recommended_decision": {{
        "chosen_option": "Option 1",
        "implementation_plan": ["step1", "step2"],
        "success_probability": 0.8,
        "monitoring_metrics": ["metric1", "metric2"],
        "contingency_triggers": ["trigger1", "trigger2"]
    }},
    "confidence_level": "high",
    "rationale": "comprehensive justification for the decision"
}}
"""
        
        response = self._call_openai_reasoning(prompt, screen_data)
        return self._parse_reasoning_response(ReasoningType.DECISION_MAKING, response, context)

    def _adaptive_learning(self, context: ReasoningContext, screen_data: Optional[str]) -> ReasoningResult:
        """Adaptive learning from experience and feedback"""

        prompt = f"""
You are an advanced AI learning system. Analyze patterns and adapt strategies based on experience.

LEARNING CONTEXT: {context.task_description}
HISTORICAL DATA: {json.dumps(context.historical_data[-10:], indent=2) if context.historical_data else "No historical data"}
CURRENT STATE: {json.dumps(context.current_state, indent=2)}

Apply machine learning and adaptive reasoning:

1. PATTERN ANALYSIS
   - Identify recurring patterns in historical data
   - Analyze success and failure factors
   - Detect performance trends
   - Find optimization opportunities

2. STRATEGY ADAPTATION
   - Adapt strategies based on learned patterns
   - Optimize parameters for better performance
   - Eliminate ineffective approaches
   - Enhance successful methodologies

3. PREDICTIVE INSIGHTS
   - Predict likely outcomes based on patterns
   - Identify potential issues before they occur
   - Suggest proactive improvements
   - Recommend timing optimizations

4. CONTINUOUS IMPROVEMENT
   - Define learning objectives
   - Set up feedback loops
   - Plan experimentation approach
   - Design performance monitoring

Respond in JSON format:
{{
    "pattern_analysis": {{
        "success_patterns": ["pattern1", "pattern2"],
        "failure_patterns": ["pattern1", "pattern2"],
        "performance_trends": "improving/declining/stable",
        "key_insights": ["insight1", "insight2"]
    }},
    "adaptive_strategy": {{
        "strategy_adjustments": ["adjustment1", "adjustment2"],
        "parameter_optimizations": {{"param1": "new_value"}},
        "approach_improvements": ["improvement1", "improvement2"],
        "elimination_candidates": ["approach1", "approach2"]
    }},
    "predictive_insights": {{
        "likely_outcomes": ["outcome1", "outcome2"],
        "potential_issues": ["issue1", "issue2"],
        "optimization_opportunities": ["opportunity1", "opportunity2"],
        "timing_recommendations": ["recommendation1", "recommendation2"]
    }},
    "learning_plan": {{
        "learning_objectives": ["objective1", "objective2"],
        "feedback_mechanisms": ["mechanism1", "mechanism2"],
        "experimentation_areas": ["area1", "area2"],
        "success_metrics": ["metric1", "metric2"]
    }},
    "confidence_level": "medium",
    "success_probability": 0.75,
    "rationale": "how learning will improve performance"
}}
"""

        response = self._call_openai_reasoning(prompt, screen_data)
        return self._parse_reasoning_response(ReasoningType.ADAPTIVE_LEARNING, response, context)

    def _causal_analysis(self, context: ReasoningContext, screen_data: Optional[str]) -> ReasoningResult:
        """Causal analysis and root cause identification"""

        prompt = f"""
You are an advanced AI causal analyst. Identify cause-effect relationships and root causes.

ANALYSIS TARGET: {context.task_description}
CURRENT SITUATION: {json.dumps(context.current_state, indent=2)}
HISTORICAL CONTEXT: {json.dumps(context.historical_data[-5:], indent=2) if context.historical_data else "Limited historical data"}

Apply causal reasoning methodologies:

1. CAUSAL MAPPING
   - Identify all relevant variables
   - Map cause-effect relationships
   - Determine causal chains
   - Find feedback loops

2. ROOT CAUSE ANALYSIS
   - Trace problems to their origins
   - Distinguish symptoms from causes
   - Identify contributing factors
   - Assess causal strength

3. INTERVENTION ANALYSIS
   - Identify leverage points for change
   - Predict intervention outcomes
   - Assess intervention feasibility
   - Plan intervention strategies

4. SYSTEMIC UNDERSTANDING
   - Understand system dynamics
   - Identify emergent properties
   - Predict system behavior
   - Design system improvements

Respond in JSON format:
{{
    "causal_mapping": {{
        "primary_causes": ["cause1", "cause2"],
        "secondary_causes": ["cause1", "cause2"],
        "causal_chains": [["cause1", "effect1", "effect2"]],
        "feedback_loops": ["loop1", "loop2"]
    }},
    "root_cause_analysis": {{
        "root_causes": ["root1", "root2"],
        "contributing_factors": ["factor1", "factor2"],
        "causal_strength": {{"cause1": 0.8, "cause2": 0.6}},
        "evidence_quality": "high/medium/low"
    }},
    "intervention_strategy": {{
        "leverage_points": ["point1", "point2"],
        "intervention_options": ["option1", "option2"],
        "expected_outcomes": ["outcome1", "outcome2"],
        "implementation_difficulty": "low/medium/high"
    }},
    "systemic_insights": {{
        "system_dynamics": "description of how system behaves",
        "emergent_properties": ["property1", "property2"],
        "system_vulnerabilities": ["vulnerability1", "vulnerability2"],
        "improvement_opportunities": ["opportunity1", "opportunity2"]
    }},
    "confidence_level": "high",
    "success_probability": 0.85,
    "rationale": "explanation of causal analysis and recommendations"
}}
"""

        response = self._call_openai_reasoning(prompt, screen_data)
        return self._parse_reasoning_response(ReasoningType.CAUSAL_ANALYSIS, response, context)

    def _predictive_modeling(self, context: ReasoningContext, screen_data: Optional[str]) -> ReasoningResult:
        """Predictive modeling and forecasting"""

        prompt = f"""
You are an advanced AI predictive modeler. Create forecasts and predict future outcomes.

PREDICTION TARGET: {context.task_description}
CURRENT DATA: {json.dumps(context.current_state, indent=2)}
HISTORICAL TRENDS: {json.dumps(context.historical_data[-20:], indent=2) if context.historical_data else "Limited trend data"}
TIME HORIZON: {context.time_limit} minutes

Apply predictive modeling techniques:

1. TREND ANALYSIS
   - Identify historical trends and patterns
   - Analyze trend stability and volatility
   - Detect seasonal or cyclical patterns
   - Assess trend continuation probability

2. SCENARIO MODELING
   - Develop multiple future scenarios
   - Assign probability to each scenario
   - Identify key uncertainty factors
   - Model scenario dependencies

3. OUTCOME PREDICTION
   - Predict most likely outcomes
   - Estimate confidence intervals
   - Identify leading indicators
   - Assess prediction reliability

4. RISK FORECASTING
   - Predict potential risks and opportunities
   - Estimate probability and impact
   - Identify early warning signals
   - Plan risk mitigation strategies

Respond in JSON format:
{{
    "trend_analysis": {{
        "identified_trends": ["trend1", "trend2"],
        "trend_strength": {{"trend1": 0.8, "trend2": 0.6}},
        "pattern_stability": "high/medium/low",
        "continuation_probability": 0.75
    }},
    "scenario_modeling": {{
        "scenarios": [
            {{
                "scenario_name": "Optimistic",
                "probability": 0.3,
                "key_assumptions": ["assumption1", "assumption2"],
                "predicted_outcomes": ["outcome1", "outcome2"]
            }}
        ],
        "uncertainty_factors": ["factor1", "factor2"],
        "scenario_dependencies": ["dependency1", "dependency2"]
    }},
    "predictions": {{
        "most_likely_outcome": "detailed prediction",
        "confidence_interval": {{"lower": 0.6, "upper": 0.9}},
        "leading_indicators": ["indicator1", "indicator2"],
        "prediction_accuracy": "high/medium/low"
    }},
    "risk_forecast": {{
        "potential_risks": ["risk1", "risk2"],
        "risk_probabilities": {{"risk1": 0.2, "risk2": 0.1}},
        "early_warnings": ["warning1", "warning2"],
        "mitigation_strategies": ["strategy1", "strategy2"]
    }},
    "confidence_level": "medium",
    "success_probability": 0.7,
    "rationale": "basis for predictions and modeling approach"
}}
"""

        response = self._call_openai_reasoning(prompt, screen_data)
        return self._parse_reasoning_response(ReasoningType.PREDICTIVE_MODELING, response, context)

    def _creative_synthesis(self, context: ReasoningContext, screen_data: Optional[str]) -> ReasoningResult:
        """Creative synthesis and innovative solutions"""

        prompt = f"""
You are an advanced AI creative synthesizer. Generate innovative solutions through creative thinking.

CREATIVE CHALLENGE: {context.task_description}
CURRENT CONSTRAINTS: {', '.join(context.constraints)}
AVAILABLE RESOURCES: {', '.join(context.available_tools)}
OBJECTIVES: {', '.join(context.objectives)}

Apply creative thinking methodologies:

1. DIVERGENT THINKING
   - Generate multiple creative ideas
   - Explore unconventional approaches
   - Challenge existing assumptions
   - Combine disparate concepts

2. INNOVATIVE SYNTHESIS
   - Synthesize ideas into novel solutions
   - Create hybrid approaches
   - Apply cross-domain insights
   - Design breakthrough strategies

3. CREATIVE EVALUATION
   - Assess novelty and originality
   - Evaluate practical feasibility
   - Consider implementation challenges
   - Predict adoption likelihood

4. INNOVATION STRATEGY
   - Plan creative implementation
   - Design experimentation approach
   - Manage innovation risks
   - Scale successful innovations

Respond in JSON format:
{{
    "creative_ideas": [
        {{
            "idea_name": "Innovative Idea 1",
            "description": "detailed description",
            "novelty_score": 0.8,
            "feasibility_score": 0.7,
            "potential_impact": "high/medium/low"
        }}
    ],
    "synthesized_solution": {{
        "solution_name": "Creative Solution",
        "approach": "detailed innovative approach",
        "key_innovations": ["innovation1", "innovation2"],
        "differentiation_factors": ["factor1", "factor2"],
        "implementation_strategy": ["step1", "step2", "step3"]
    }},
    "creative_evaluation": {{
        "originality_assessment": "high/medium/low",
        "practical_feasibility": 0.8,
        "implementation_complexity": "low/medium/high",
        "adoption_barriers": ["barrier1", "barrier2"]
    }},
    "innovation_plan": {{
        "experimentation_approach": "how to test the innovation",
        "success_metrics": ["metric1", "metric2"],
        "risk_management": ["risk1", "risk2"],
        "scaling_strategy": "how to scale if successful"
    }},
    "confidence_level": "medium",
    "success_probability": 0.65,
    "rationale": "creative reasoning and innovation justification"
}}
"""

        response = self._call_openai_reasoning(prompt, screen_data)
        return self._parse_reasoning_response(ReasoningType.CREATIVE_SYNTHESIS, response, context)

    def _call_openai_reasoning(self, prompt: str, screen_data: Optional[str] = None) -> Optional[str]:
        """Call OpenAI API for advanced reasoning"""
        try:
            headers = {
                'Authorization': f'Bearer {self.openai_key}',
                'Content-Type': 'application/json'
            }

            # Prepare messages
            messages = []

            if screen_data:
                # Include visual context for reasoning
                messages.append({
                    'role': 'user',
                    'content': [
                        {'type': 'text', 'text': prompt},
                        {
                            'type': 'image_url',
                            'image_url': {
                                'url': f"data:image/jpeg;base64,{screen_data}",
                                'detail': 'high'
                            }
                        }
                    ]
                })
            else:
                messages.append({
                    'role': 'user',
                    'content': prompt
                })

            payload = {
                'model': 'gpt-4o',
                'messages': messages,
                'max_tokens': 2000,
                'temperature': 0.1,  # Low temperature for consistent reasoning
                'top_p': 0.9
            }

            response = requests.post(
                'https://api.openai.com/v1/chat/completions',
                headers=headers,
                json=payload,
                timeout=60
            )

            if response.status_code == 200:
                result = response.json()
                return result['choices'][0]['message']['content']
            else:
                logger.error(f"❌ OpenAI API error: {response.status_code}")
                return None

        except Exception as e:
            logger.error(f"❌ OpenAI reasoning call failed: {e}")
            return None

    def _parse_reasoning_response(self, reasoning_type: ReasoningType, response: str,
                                context: ReasoningContext) -> ReasoningResult:
        """Parse OpenAI response into structured reasoning result"""
        try:
            # Extract JSON from response
            import re
            json_match = re.search(r'\{.*\}', response, re.DOTALL)

            if json_match:
                data = json.loads(json_match.group())

                # Extract key components
                confidence_str = data.get('confidence_level', 'medium').lower()
                confidence = self._parse_confidence(confidence_str)

                success_prob = data.get('success_probability', 0.5)
                rationale = data.get('rationale', 'No rationale provided')

                # Create execution plan from the reasoning
                execution_plan = self._extract_execution_plan(data, reasoning_type)

                # Extract alternatives and risk assessment
                alternatives = self._extract_alternatives(data)
                risk_assessment = self._extract_risk_assessment(data)

                # Create monitoring points
                monitoring_points = self._extract_monitoring_points(data)

                # Create fallback strategies
                fallback_strategies = self._extract_fallback_strategies(data)

                return ReasoningResult(
                    reasoning_type=reasoning_type,
                    decision=data,
                    confidence=confidence,
                    rationale=rationale,
                    alternatives=alternatives,
                    risk_assessment=risk_assessment,
                    success_probability=success_prob,
                    execution_plan=execution_plan,
                    monitoring_points=monitoring_points,
                    fallback_strategies=fallback_strategies,
                    timestamp=datetime.now(),
                    processing_time=0.0  # Will be set by caller
                )
            else:
                # Fallback parsing
                return self._create_fallback_result(reasoning_type, context, "Could not parse JSON response")

        except Exception as e:
            logger.error(f"❌ Failed to parse reasoning response: {e}")
            return self._create_fallback_result(reasoning_type, context, str(e))

    def _parse_confidence(self, confidence_str: str) -> ConfidenceLevel:
        """Parse confidence string to enum"""
        confidence_map = {
            'very_high': ConfidenceLevel.VERY_HIGH,
            'high': ConfidenceLevel.HIGH,
            'medium': ConfidenceLevel.MEDIUM,
            'low': ConfidenceLevel.LOW,
            'very_low': ConfidenceLevel.VERY_LOW
        }
        return confidence_map.get(confidence_str, ConfidenceLevel.MEDIUM)

    def _extract_execution_plan(self, data: Dict, reasoning_type: ReasoningType) -> List[Dict[str, Any]]:
        """Extract execution plan from reasoning data"""
        plan = []

        if reasoning_type == ReasoningType.STRATEGIC_PLANNING:
            phases = data.get('execution_strategy', {}).get('phases', [])
            for phase in phases:
                plan.append({
                    'type': 'phase',
                    'name': phase.get('phase_name', 'Unknown Phase'),
                    'objectives': phase.get('objectives', []),
                    'duration': phase.get('duration_estimate', 10),
                    'tools': phase.get('tools_required', []),
                    'dependencies': phase.get('dependencies', [])
                })

        elif reasoning_type == ReasoningType.TACTICAL_EXECUTION:
            immediate_action = data.get('immediate_action', {})
            if immediate_action:
                plan.append({
                    'type': 'immediate_action',
                    'tool': immediate_action.get('tool'),
                    'parameters': immediate_action.get('parameters', {}),
                    'purpose': immediate_action.get('purpose'),
                    'expected_outcome': immediate_action.get('expected_outcome')
                })

        elif reasoning_type == ReasoningType.PROBLEM_SOLVING:
            solution = data.get('recommended_solution', {})
            steps = solution.get('implementation_steps', [])
            for i, step in enumerate(steps):
                plan.append({
                    'type': 'solution_step',
                    'step_number': i + 1,
                    'description': step,
                    'solution_name': solution.get('solution', 'Unknown Solution')
                })

        return plan

    def _extract_alternatives(self, data: Dict) -> List[Dict[str, Any]]:
        """Extract alternative options from reasoning data"""
        alternatives = []

        # Look for alternatives in different structures
        if 'solution_options' in data:
            for option in data['solution_options']:
                alternatives.append({
                    'name': option.get('solution_name', 'Alternative'),
                    'description': option.get('approach', ''),
                    'feasibility': option.get('feasibility', 0.5),
                    'pros': option.get('pros', []),
                    'cons': option.get('cons', [])
                })

        elif 'option_evaluation' in data:
            for option in data['option_evaluation']:
                alternatives.append({
                    'name': option.get('option', 'Alternative'),
                    'score': option.get('total_score', 0.5),
                    'pros': option.get('pros', []),
                    'cons': option.get('cons', []),
                    'risk_level': option.get('risk_level', 'medium')
                })

        return alternatives

    def _extract_risk_assessment(self, data: Dict) -> Dict[str, Any]:
        """Extract risk assessment from reasoning data"""
        risk_assessment = {
            'overall_risk': 'medium',
            'risk_factors': [],
            'mitigation_strategies': [],
            'contingency_plans': []
        }

        # Look for risk information in different structures
        if 'risk_management' in data:
            risk_mgmt = data['risk_management']
            risk_assessment['risk_factors'] = risk_mgmt.get('high_risks', [])
            risk_assessment['contingency_plans'] = risk_mgmt.get('contingency_plans', [])

        if 'risk_forecast' in data:
            risk_forecast = data['risk_forecast']
            risk_assessment['risk_factors'].extend(risk_forecast.get('potential_risks', []))
            risk_assessment['mitigation_strategies'] = risk_forecast.get('mitigation_strategies', [])

        return risk_assessment

    def _extract_monitoring_points(self, data: Dict) -> List[str]:
        """Extract monitoring points from reasoning data"""
        monitoring = []

        # Look for monitoring information
        if 'monitoring_metrics' in data:
            monitoring.extend(data['monitoring_metrics'])

        if 'success_criteria' in data:
            monitoring.extend(data['success_criteria'])

        if 'monitoring_points' in data:
            monitoring.extend(data['monitoring_points'])

        return monitoring

    def _extract_fallback_strategies(self, data: Dict) -> List[Dict[str, Any]]:
        """Extract fallback strategies from reasoning data"""
        fallbacks = []

        # Look for fallback information
        if 'adaptation_strategy' in data:
            adapt = data['adaptation_strategy']
            if 'failure_recovery' in adapt:
                fallbacks.append({
                    'trigger': 'failure',
                    'strategy': adapt['failure_recovery']
                })
            if 'alternative_approaches' in adapt:
                for approach in adapt['alternative_approaches']:
                    fallbacks.append({
                        'trigger': 'primary_failure',
                        'strategy': approach
                    })

        return fallbacks

    def _create_fallback_result(self, reasoning_type: ReasoningType, context: ReasoningContext,
                               error_msg: str) -> ReasoningResult:
        """Create a fallback reasoning result when AI reasoning fails"""
        return ReasoningResult(
            reasoning_type=reasoning_type,
            decision={'error': error_msg, 'fallback': True},
            confidence=ConfidenceLevel.LOW,
            rationale=f"Fallback result due to reasoning failure: {error_msg}",
            alternatives=[],
            risk_assessment={'overall_risk': 'high', 'risk_factors': ['reasoning_failure']},
            success_probability=0.3,
            execution_plan=[{
                'type': 'fallback',
                'description': 'Execute basic approach due to reasoning failure'
            }],
            monitoring_points=['execution_success', 'error_recovery'],
            fallback_strategies=[{
                'trigger': 'continued_failure',
                'strategy': 'Request human intervention'
            }],
            timestamp=datetime.now(),
            processing_time=0.0
        )

    def _generate_cache_key(self, reasoning_type: ReasoningType, context: ReasoningContext) -> str:
        """Generate cache key for reasoning results"""
        import hashlib

        # Create a hash of the key context elements
        key_data = {
            'type': reasoning_type.value,
            'task': context.task_description,
            'tools': sorted(context.available_tools),
            'constraints': sorted(context.constraints),
            'objectives': sorted(context.objectives)
        }

        key_str = json.dumps(key_data, sort_keys=True)
        return hashlib.md5(key_str.encode()).hexdigest()

    def _update_metrics(self, result: ReasoningResult):
        """Update performance metrics"""
        self.performance_metrics['total_reasonings'] += 1

        if result.confidence != ConfidenceLevel.VERY_LOW:
            self.performance_metrics['successful_reasonings'] += 1

        # Update average confidence
        confidence_values = {
            ConfidenceLevel.VERY_HIGH: 0.95,
            ConfidenceLevel.HIGH: 0.82,
            ConfidenceLevel.MEDIUM: 0.62,
            ConfidenceLevel.LOW: 0.37,
            ConfidenceLevel.VERY_LOW: 0.12
        }

        current_confidence = confidence_values[result.confidence]
        total = self.performance_metrics['total_reasonings']
        prev_avg = self.performance_metrics['average_confidence']

        self.performance_metrics['average_confidence'] = (
            (prev_avg * (total - 1) + current_confidence) / total
        )

        # Update average processing time
        prev_time_avg = self.performance_metrics['average_processing_time']
        self.performance_metrics['average_processing_time'] = (
            (prev_time_avg * (total - 1) + result.processing_time) / total
        )

    def multi_perspective_reasoning(self, context: ReasoningContext,
                                  perspectives: List[ReasoningType],
                                  screen_data: Optional[str] = None) -> Dict[ReasoningType, ReasoningResult]:
        """Perform reasoning from multiple perspectives and synthesize results"""
        results = {}

        logger.info(f"🧠 Multi-perspective reasoning with {len(perspectives)} perspectives")

        # Perform reasoning from each perspective
        for perspective in perspectives:
            try:
                result = self.reason(perspective, context, screen_data)
                results[perspective] = result
                logger.info(f"✅ {perspective.value} reasoning completed")
            except Exception as e:
                logger.error(f"❌ {perspective.value} reasoning failed: {e}")
                results[perspective] = self._create_fallback_result(perspective, context, str(e))

        return results

    def synthesize_reasoning_results(self, results: Dict[ReasoningType, ReasoningResult]) -> ReasoningResult:
        """Synthesize multiple reasoning results into a unified decision"""
        if not results:
            return self._create_fallback_result(
                ReasoningType.DECISION_MAKING,
                ReasoningContext("No results to synthesize", {}, [], [], []),
                "No reasoning results provided"
            )

        # Weight different reasoning types
        weights = {
            ReasoningType.STRATEGIC_PLANNING: 0.25,
            ReasoningType.TACTICAL_EXECUTION: 0.20,
            ReasoningType.PROBLEM_SOLVING: 0.20,
            ReasoningType.DECISION_MAKING: 0.15,
            ReasoningType.ADAPTIVE_LEARNING: 0.10,
            ReasoningType.CAUSAL_ANALYSIS: 0.05,
            ReasoningType.PREDICTIVE_MODELING: 0.03,
            ReasoningType.CREATIVE_SYNTHESIS: 0.02
        }

        # Calculate weighted confidence and success probability
        total_weight = 0
        weighted_confidence = 0
        weighted_success_prob = 0

        confidence_values = {
            ConfidenceLevel.VERY_HIGH: 0.95,
            ConfidenceLevel.HIGH: 0.82,
            ConfidenceLevel.MEDIUM: 0.62,
            ConfidenceLevel.LOW: 0.37,
            ConfidenceLevel.VERY_LOW: 0.12
        }

        for reasoning_type, result in results.items():
            weight = weights.get(reasoning_type, 0.1)
            total_weight += weight

            confidence_val = confidence_values[result.confidence]
            weighted_confidence += confidence_val * weight
            weighted_success_prob += result.success_probability * weight

        # Normalize
        if total_weight > 0:
            weighted_confidence /= total_weight
            weighted_success_prob /= total_weight

        # Determine overall confidence level
        if weighted_confidence >= 0.9:
            overall_confidence = ConfidenceLevel.VERY_HIGH
        elif weighted_confidence >= 0.75:
            overall_confidence = ConfidenceLevel.HIGH
        elif weighted_confidence >= 0.5:
            overall_confidence = ConfidenceLevel.MEDIUM
        elif weighted_confidence >= 0.25:
            overall_confidence = ConfidenceLevel.LOW
        else:
            overall_confidence = ConfidenceLevel.VERY_LOW

        # Synthesize execution plan
        synthesized_plan = []
        for reasoning_type, result in results.items():
            if result.execution_plan:
                for step in result.execution_plan:
                    step['source_reasoning'] = reasoning_type.value
                    synthesized_plan.append(step)

        # Synthesize alternatives
        all_alternatives = []
        for result in results.values():
            all_alternatives.extend(result.alternatives)

        # Synthesize risk assessment
        all_risks = []
        all_mitigations = []
        for result in results.values():
            all_risks.extend(result.risk_assessment.get('risk_factors', []))
            all_mitigations.extend(result.risk_assessment.get('mitigation_strategies', []))

        synthesized_risk = {
            'overall_risk': 'medium',
            'risk_factors': list(set(all_risks)),
            'mitigation_strategies': list(set(all_mitigations))
        }

        # Create synthesized rationale
        rationale = f"Synthesized from {len(results)} reasoning perspectives: "
        rationale += ", ".join([r.value for r in results.keys()])

        return ReasoningResult(
            reasoning_type=ReasoningType.DECISION_MAKING,
            decision={'synthesis': True, 'perspectives': len(results)},
            confidence=overall_confidence,
            rationale=rationale,
            alternatives=all_alternatives[:5],  # Limit to top 5
            risk_assessment=synthesized_risk,
            success_probability=weighted_success_prob,
            execution_plan=synthesized_plan,
            monitoring_points=list(set([mp for r in results.values() for mp in r.monitoring_points])),
            fallback_strategies=list(set([str(fs) for r in results.values() for fs in r.fallback_strategies]))[:3],
            timestamp=datetime.now(),
            processing_time=sum(r.processing_time for r in results.values())
        )

    def get_reasoning_stats(self) -> Dict[str, Any]:
        """Get reasoning system statistics"""
        return {
            'performance_metrics': self.performance_metrics.copy(),
            'cache_size': len(self.reasoning_cache),
            'history_size': len(self.reasoning_history),
            'reasoning_types_used': list(set(r.reasoning_type.value for r in self.reasoning_history)),
            'average_confidence': self.performance_metrics['average_confidence'],
            'success_rate': (
                self.performance_metrics['successful_reasonings'] /
                max(1, self.performance_metrics['total_reasonings'])
            )
        }

    def clear_cache(self):
        """Clear reasoning cache"""
        self.reasoning_cache.clear()
        logger.info("🧹 Reasoning cache cleared")

    def get_recent_reasoning_history(self, limit: int = 10) -> List[Dict[str, Any]]:
        """Get recent reasoning history"""
        recent = self.reasoning_history[-limit:] if self.reasoning_history else []

        return [{
            'reasoning_type': r.reasoning_type.value,
            'confidence': r.confidence.value,
            'success_probability': r.success_probability,
            'processing_time': r.processing_time,
            'timestamp': r.timestamp.isoformat()
        } for r in recent]
