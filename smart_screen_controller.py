#!/usr/bin/env python3
"""
Smart Screen Controller - Actually controls your computer with AI guidance
Uses real OpenAI API and performs real screen actions
"""

import os
import sys
import time
import base64
import io
import json
import requests
import pyautogui
from PIL import Image
import subprocess
import logging

# Configure pyautogui for safety
pyautogui.FAILSAFE = True
pyautogui.PAUSE = 0.3

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class SmartScreenController:
    """AI-powered screen controller that actually performs actions"""
    
    def __init__(self):
        # Check for OpenAI API key
        self.openai_key = os.getenv('OPENAI_API_KEY')
        if not self.openai_key:
            print("❌ OpenAI API key not found!")
            print("\n🔧 To set your OpenAI API key:")
            print("Windows: setx OPENAI_API_KEY 'your-key-here'")
            print("Or add to your environment variables")
            print("\n💡 Get your key from: https://platform.openai.com/api-keys")
            sys.exit(1)
        
        print(f"✅ OpenAI API key loaded: {self.openai_key[:10]}...")
        
        # Get screen dimensions
        self.screen_width, self.screen_height = pyautogui.size()
        print(f"🖥️ Screen: {self.screen_width}x{self.screen_height}")
        
        self.actions_performed = 0
        
    def capture_screen(self) -> str:
        """Capture screen and convert to base64 for AI analysis"""
        try:
            # Take screenshot
            screenshot = pyautogui.screenshot()
            
            # Resize for faster AI processing
            if screenshot.width > 1280:
                ratio = 1280 / screenshot.width
                new_height = int(screenshot.height * ratio)
                screenshot = screenshot.resize((1280, new_height), Image.LANCZOS)
            
            # Convert to base64
            buffer = io.BytesIO()
            screenshot.save(buffer, format='JPEG', quality=85)
            img_base64 = base64.b64encode(buffer.getvalue()).decode('utf-8')
            
            return img_base64
            
        except Exception as e:
            print(f"❌ Screenshot failed: {e}")
            return None
    
    def ask_ai_what_to_do(self, user_request: str) -> dict:
        """Ask GPT-4V what to do based on current screen"""
        try:
            screen_data = self.capture_screen()
            if not screen_data:
                return None
            
            prompt = f"""
You are controlling a Windows computer. The user wants: "{user_request}"

Look at the current screen and tell me EXACTLY what to do next. Be very specific about:
1. What you can see on the screen
2. What action to take (click, type, press key, etc.)
3. Exact coordinates if clicking something
4. What application to open if needed

Respond in JSON format:
{{
    "what_i_see": "description of current screen",
    "next_action": "click|type|key|open_app|scroll",
    "coordinates": [x, y] or null,
    "text_to_type": "text" or null,
    "key_to_press": "key_name" or null,
    "app_to_open": "app_name" or null,
    "explanation": "why this action will help complete the user's request"
}}

Be precise with coordinates. Look for buttons, text fields, menus, and other UI elements.
"""

            headers = {
                'Authorization': f'Bearer {self.openai_key}',
                'Content-Type': 'application/json'
            }
            
            payload = {
                'model': 'gpt-4o',
                'messages': [{
                    'role': 'user',
                    'content': [
                        {'type': 'text', 'text': prompt},
                        {
                            'type': 'image_url',
                            'image_url': {
                                'url': f"data:image/jpeg;base64,{screen_data}",
                                'detail': 'high'
                            }
                        }
                    ]
                }],
                'max_tokens': 800,
                'temperature': 0.1
            }
            
            print("🧠 Asking AI what to do...")
            response = requests.post(
                'https://api.openai.com/v1/chat/completions',
                headers=headers,
                json=payload,
                timeout=30
            )
            
            if response.status_code == 200:
                result = response.json()
                ai_text = result['choices'][0]['message']['content']
                
                # Extract JSON from response
                try:
                    import re
                    json_match = re.search(r'\{.*\}', ai_text, re.DOTALL)
                    if json_match:
                        return json.loads(json_match.group())
                    else:
                        return {'what_i_see': ai_text, 'next_action': 'none'}
                except:
                    return {'what_i_see': ai_text, 'next_action': 'none'}
            else:
                print(f"❌ OpenAI API error: {response.status_code}")
                return None
                
        except Exception as e:
            print(f"❌ AI analysis failed: {e}")
            return None
    
    def perform_action(self, action_data: dict) -> bool:
        """Perform the action suggested by AI"""
        try:
            action = action_data.get('next_action', '').lower()
            
            print(f"🎯 AI sees: {action_data.get('what_i_see', 'Unknown')[:100]}...")
            print(f"🔄 Action: {action_data.get('explanation', 'No explanation')}")
            
            if action == 'click':
                coords = action_data.get('coordinates')
                if coords and len(coords) == 2:
                    x, y = coords
                    print(f"🖱️ Clicking at ({x}, {y})")
                    pyautogui.click(x, y)
                    time.sleep(1)
                    self.actions_performed += 1
                    return True
                else:
                    print("❌ No valid coordinates for click")
                    return False
            
            elif action == 'type':
                text = action_data.get('text_to_type', '')
                if text:
                    print(f"⌨️ Typing: {text}")
                    pyautogui.typewrite(text, interval=0.05)
                    time.sleep(0.5)
                    self.actions_performed += 1
                    return True
                else:
                    print("❌ No text to type")
                    return False
            
            elif action == 'key':
                key = action_data.get('key_to_press', '')
                if key:
                    print(f"🔑 Pressing: {key}")
                    pyautogui.press(key)
                    time.sleep(0.5)
                    self.actions_performed += 1
                    return True
                else:
                    print("❌ No key specified")
                    return False
            
            elif action == 'open_app':
                app = action_data.get('app_to_open', '')
                if app:
                    return self.open_application(app)
                else:
                    print("❌ No app specified")
                    return False
            
            elif action == 'scroll':
                coords = action_data.get('coordinates', [self.screen_width//2, self.screen_height//2])
                print(f"🖱️ Scrolling at ({coords[0]}, {coords[1]})")
                pyautogui.scroll(-3, x=coords[0], y=coords[1])
                time.sleep(0.5)
                self.actions_performed += 1
                return True
            
            else:
                print(f"❓ Unknown action: {action}")
                return False
                
        except Exception as e:
            print(f"❌ Action failed: {e}")
            return False
    
    def open_application(self, app_name: str) -> bool:
        """Open an application using the most reliable method"""
        print(f"🚀 Opening {app_name}...")
        
        # Common applications
        app_commands = {
            'calculator': 'calc',
            'calc': 'calc',
            'notepad': 'notepad',
            'chrome': 'chrome',
            'browser': 'chrome',
            'excel': 'excel',
            'word': 'winword',
            'powerpoint': 'powerpnt',
            'outlook': 'outlook',
            'cmd': 'cmd',
            'command prompt': 'cmd',
            'powershell': 'powershell'
        }
        
        app_lower = app_name.lower()
        
        # Try direct command first
        if app_lower in app_commands:
            try:
                subprocess.Popen(app_commands[app_lower], shell=True)
                time.sleep(2)
                self.actions_performed += 1
                print(f"✅ Opened {app_name} via command")
                return True
            except:
                pass
        
        # Fallback: Use Windows search
        try:
            print(f"🔍 Searching for {app_name}...")
            pyautogui.press('win')
            time.sleep(0.8)
            pyautogui.typewrite(app_name)
            time.sleep(1)
            pyautogui.press('enter')
            time.sleep(2)
            self.actions_performed += 1
            print(f"✅ Opened {app_name} via search")
            return True
        except Exception as e:
            print(f"❌ Failed to open {app_name}: {e}")
            return False
    
    def execute_smart_task(self, user_request: str, max_steps: int = 5) -> bool:
        """Execute a task using AI guidance"""
        print(f"\n🎯 SMART TASK: {user_request}")
        print("=" * 50)
        
        for step in range(1, max_steps + 1):
            print(f"\n📋 Step {step}/{max_steps}")
            
            # Get AI guidance
            action_data = self.ask_ai_what_to_do(user_request)
            
            if not action_data:
                print("❌ Could not get AI guidance")
                return False
            
            # Check if task is complete
            if action_data.get('next_action') == 'none' or 'complete' in action_data.get('what_i_see', '').lower():
                print("✅ Task appears to be complete!")
                return True
            
            # Perform the suggested action
            success = self.perform_action(action_data)
            
            if not success:
                print(f"❌ Step {step} failed")
                # Try a simple fallback for common requests
                if any(word in user_request.lower() for word in ['open', 'launch', 'start']):
                    app_words = user_request.lower().split()
                    for word in app_words:
                        if word in ['calculator', 'notepad', 'chrome', 'excel', 'word']:
                            return self.open_application(word)
                return False
            
            # Brief pause between steps
            time.sleep(1.5)
        
        print(f"⚠️ Reached maximum steps ({max_steps})")
        return False
    
    def run_interactive_mode(self):
        """Interactive mode for real-time control"""
        print("\n🤖 SMART SCREEN CONTROLLER")
        print("=" * 40)
        print("🎯 I can actually control your screen using AI vision!")
        print("✨ Your OpenAI API key is working!")
        print("\n💡 Try these commands:")
        print("  • 'open calculator'")
        print("  • 'open notepad and type hello'")
        print("  • 'take a screenshot'")
        print("  • 'what do you see on my screen'")
        print("  • 'click on the start button'")
        
        while True:
            try:
                request = input("\n🎯 What should I do? ").strip()
                
                if request.lower() in ['quit', 'exit', 'bye', 'stop']:
                    break
                
                if request.lower() in ['screenshot', 'take screenshot', 'capture']:
                    screen_data = self.capture_screen()
                    if screen_data:
                        print("📸 Screenshot captured and ready for AI analysis")
                    continue
                
                if request.lower() in ['analyze', 'what do you see', 'describe screen']:
                    action_data = self.ask_ai_what_to_do("describe what you see on the screen")
                    if action_data:
                        print(f"🧠 AI sees: {action_data.get('what_i_see', 'Could not analyze')}")
                    continue
                
                if request:
                    print(f"\n🚀 Executing: {request}")
                    success = self.execute_smart_task(request)
                    if success:
                        print("✅ Task completed successfully!")
                    else:
                        print("❌ Task failed or incomplete")
            
            except KeyboardInterrupt:
                print("\n👋 Goodbye!")
                break
            except Exception as e:
                print(f"❌ Error: {e}")
        
        print(f"\n📊 Session complete! Performed {self.actions_performed} actions.")


def main():
    """Main entry point"""
    print("🧠 Smart Screen Controller - Real AI Computer Control")
    print("Requires OpenAI API key for GPT-4V vision capabilities")
    
    try:
        controller = SmartScreenController()
        controller.run_interactive_mode()
    except KeyboardInterrupt:
        print("\n👋 Goodbye!")
    except Exception as e:
        print(f"❌ Error: {e}")


if __name__ == "__main__":
    main()
