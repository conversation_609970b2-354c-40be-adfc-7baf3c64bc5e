#!/usr/bin/env python3
"""
Ultimate AI System
Combines speed, knowledge, and complex task handling
"""

import json
import os
import pyautogui
import time
import subprocess
import threading
import queue
from pathlib import Path

class UltimateAISystem:
    def __init__(self):
        self.knowledge = self.load_system_knowledge()
        self.task_queue = queue.Queue()
        self.is_processing = False
        
        # Adaptive speed settings
        pyautogui.PAUSE = 0.05
        pyautogui.FAILSAFE = True
        
        # Task categories for intelligent routing
        self.simple_patterns = [
            'open', 'close', 'click', 'type', 'calc', 'minimize', 'maximize'
        ]
        
        self.complex_patterns = [
            'create', 'organize', 'backup', 'format', 'analyze', 'generate',
            'multiple', 'several', 'workflow', 'report', 'spreadsheet'
        ]
        
    def load_system_knowledge(self):
        """Load comprehensive system knowledge"""
        try:
            with open("system_knowledge.json", 'r', encoding='utf-8') as f:
                knowledge = json.load(f)
                print(f"🧠 AI knows about {len(knowledge.get('installed_apps', {}))} applications")
                return knowledge
        except FileNotFoundError:
            print("⚠️ Building system knowledge...")
            self.build_knowledge()
            return self.load_system_knowledge()
    
    def build_knowledge(self):
        """Build system knowledge if not available"""
        try:
            subprocess.run(["python", "system_knowledge_builder.py"], check=True)
        except:
            print("❌ Could not build system knowledge")
            return {}
    
    def is_simple_task(self, command):
        """Determine if task is simple or complex"""
        command_lower = command.lower()
        
        # Check for simple patterns
        for pattern in self.simple_patterns:
            if pattern in command_lower:
                return True
        
        # Check for complex patterns
        for pattern in self.complex_patterns:
            if pattern in command_lower:
                return False
        
        # Default to simple for short commands
        return len(command.split()) <= 3
    
    def execute_simple_task(self, command):
        """Execute simple tasks ultra-fast"""
        print(f"⚡ FAST EXECUTION: {command}")
        
        command_lower = command.lower().strip()
        
        # Parse command
        if command_lower.startswith('open '):
            app_name = command_lower[5:]
            return self.ultra_fast_open(app_name)
        
        elif command_lower.startswith('calc '):
            expression = command[5:]
            return self.quick_calculate(expression)
        
        elif command_lower.startswith('type '):
            text = command[5:]
            pyautogui.typewrite(text, interval=0.01)
            return True
        
        elif 'close' in command_lower:
            pyautogui.hotkey('alt', 'f4')
            return True
        
        elif 'minimize' in command_lower:
            pyautogui.hotkey('win', 'down')
            return True
        
        elif 'maximize' in command_lower:
            pyautogui.hotkey('win', 'up')
            return True
        
        return False
    
    def ultra_fast_open(self, app_name):
        """Ultra-fast app opening with knowledge"""
        # Use system knowledge to find best launch method
        apps = self.knowledge.get('installed_apps', {})
        
        # Find app in knowledge base
        app_info = None
        for key, info in apps.items():
            if app_name in key or app_name in info.get('name', '').lower():
                app_info = info
                break
        
        # Try multiple launch methods
        methods = [
            lambda: subprocess.Popen(app_name, shell=True),
            lambda: self.launch_via_run(app_name),
            lambda: self.launch_via_start(app_name)
        ]
        
        for method in methods:
            try:
                method()
                print(f"⚡ Opened {app_name}")
                return True
            except:
                continue
        
        return False
    
    def launch_via_run(self, app_name):
        """Launch via Win+R"""
        pyautogui.hotkey('win', 'r')
        time.sleep(0.1)
        pyautogui.typewrite(app_name)
        pyautogui.press('enter')
    
    def launch_via_start(self, app_name):
        """Launch via Start menu"""
        pyautogui.press('win')
        time.sleep(0.2)
        pyautogui.typewrite(app_name)
        time.sleep(0.3)
        pyautogui.press('enter')
    
    def quick_calculate(self, expression):
        """Quick calculation"""
        if self.ultra_fast_open('calc'):
            time.sleep(1)
            pyautogui.typewrite(expression.replace('x', '*'))
            pyautogui.press('enter')
            return True
        return False
    
    def execute_complex_task(self, command):
        """Execute complex multi-step tasks"""
        print(f"🧠 COMPLEX EXECUTION: {command}")
        
        # Break down into steps
        steps = self.analyze_complex_task(command)
        
        success_count = 0
        for i, step in enumerate(steps, 1):
            print(f"📋 Step {i}: {step['description']}")
            
            try:
                if self.execute_step(step):
                    print(f"✅ Step {i} completed")
                    success_count += 1
                else:
                    print(f"❌ Step {i} failed")
                
                time.sleep(1)  # Pause between steps
                
            except Exception as e:
                print(f"❌ Step {i} error: {e}")
        
        print(f"🎉 Complex task complete: {success_count}/{len(steps)} steps")
        return success_count == len(steps)
    
    def analyze_complex_task(self, command):
        """Analyze and break down complex tasks"""
        command_lower = command.lower()
        steps = []
        
        # Sales report workflow
        if 'sales report' in command_lower and 'excel' in command_lower:
            steps = [
                {'type': 'open_app', 'description': 'Open Excel', 'app': 'excel'},
                {'type': 'create_new', 'description': 'Create new workbook'},
                {'type': 'add_headers', 'description': 'Add report headers'},
                {'type': 'add_data', 'description': 'Add sample data'},
                {'type': 'create_chart', 'description': 'Create chart'},
                {'type': 'save_file', 'description': 'Save report'}
            ]
        
        # File organization workflow
        elif 'organize' in command_lower and ('desktop' in command_lower or 'files' in command_lower):
            steps = [
                {'type': 'open_explorer', 'description': 'Open File Explorer'},
                {'type': 'navigate_desktop', 'description': 'Navigate to Desktop'},
                {'type': 'create_folders', 'description': 'Create organization folders'},
                {'type': 'sort_files', 'description': 'Sort and organize files'}
            ]
        
        # Morning routine workflow
        elif 'morning' in command_lower or ('open' in command_lower and 'and' in command_lower):
            apps = ['outlook', 'teams', 'excel', 'browser']
            for app in apps:
                if app in command_lower:
                    steps.append({
                        'type': 'open_app',
                        'description': f'Open {app}',
                        'app': app
                    })
        
        # Generic workflow
        else:
            steps = self.create_generic_workflow(command)
        
        return steps
    
    def create_generic_workflow(self, command):
        """Create generic workflow from command"""
        steps = []
        command_lower = command.lower()
        
        # Identify mentioned apps
        apps = self.knowledge.get('installed_apps', {})
        for app_name in apps:
            if app_name in command_lower:
                steps.append({
                    'type': 'open_app',
                    'description': f'Open {app_name}',
                    'app': app_name
                })
        
        # Add action steps based on keywords
        if 'create' in command_lower:
            steps.append({'type': 'create_new', 'description': 'Create new document'})
        
        if 'save' in command_lower:
            steps.append({'type': 'save_file', 'description': 'Save work'})
        
        return steps
    
    def execute_step(self, step):
        """Execute individual workflow step"""
        step_type = step.get('type', '')
        
        if step_type == 'open_app':
            return self.ultra_fast_open(step.get('app', ''))
        
        elif step_type == 'create_new':
            pyautogui.hotkey('ctrl', 'n')
            time.sleep(1)
            return True
        
        elif step_type == 'add_headers':
            headers = ["Date", "Product", "Quantity", "Price", "Total"]
            for i, header in enumerate(headers):
                pyautogui.typewrite(header)
                if i < len(headers) - 1:
                    pyautogui.press('tab')
            pyautogui.press('enter')
            return True
        
        elif step_type == 'add_data':
            # Sample data
            data_rows = [
                ["2024-01-01", "Laptop", "5", "1000", "5000"],
                ["2024-01-02", "Mouse", "20", "25", "500"],
                ["2024-01-03", "Keyboard", "15", "50", "750"]
            ]
            
            for row in data_rows:
                for i, cell in enumerate(row):
                    pyautogui.typewrite(cell)
                    if i < len(row) - 1:
                        pyautogui.press('tab')
                pyautogui.press('enter')
            return True
        
        elif step_type == 'save_file':
            pyautogui.hotkey('ctrl', 's')
            time.sleep(0.5)
            pyautogui.typewrite("Report_" + str(int(time.time())))
            pyautogui.press('enter')
            return True
        
        else:
            print(f"⚠️ Unknown step: {step_type}")
            return False
    
    def process_command(self, command):
        """Main command processor with intelligent routing"""
        start_time = time.time()
        
        if self.is_simple_task(command):
            print("⚡ Routing to FAST processor...")
            success = self.execute_simple_task(command)
        else:
            print("🧠 Routing to COMPLEX processor...")
            success = self.execute_complex_task(command)
        
        end_time = time.time()
        execution_time = end_time - start_time
        
        if success:
            print(f"✅ Command completed in {execution_time:.2f}s")
        else:
            print(f"❌ Command failed after {execution_time:.2f}s")
        
        return success

def main():
    print("🚀 ULTIMATE AI SYSTEM")
    print("=" * 40)
    print("🧠 Deep system knowledge + ⚡ Ultra-fast execution")
    print("🎯 Handles simple commands in <1s, complex tasks in minutes")
    print()
    
    ai = UltimateAISystem()
    
    print("💡 Example commands:")
    print("Simple: 'open calculator', 'calc 15*25', 'type hello'")
    print("Complex: 'create sales report in excel', 'organize my desktop files'")
    print()
    
    while True:
        try:
            command = input("🎯 Command: ").strip()
            
            if command.lower() in ['quit', 'exit']:
                break
            
            if command:
                ai.process_command(command)
            
        except KeyboardInterrupt:
            print("\n👋 Goodbye!")
            break
        except Exception as e:
            print(f"❌ Error: {e}")

if __name__ == "__main__":
    main()
