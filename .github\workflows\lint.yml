name: lint
on:
  pull_request:
    types: [opened, reopened, synchronize]
    paths:
      - "gui_agents/**"
      - "tests/**"
      - ".github/workflows/lint.yml"
  push:
    branches:
      - main
    paths:
      - "gui_agents/**"
      - "tests/**"
      - ".github/workflows/lint.yml"

env:
  SUPPORTED_PYTHON_VERSIONS: "3.11"

jobs:
  build:
    runs-on: ubuntu-latest
    strategy:
      fail-fast: false
      matrix:
        python-version: ["3.10", "3.11"]
    steps:
    - uses: actions/checkout@v3

    - name: Set up Python ${{ matrix.python-version }}
      uses: actions/setup-python@v4
      with:
        python-version: ${{ matrix.python-version }}

    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -e .[dev]

    - name: Run Linter
      run: |
        black --check gui_agents tests
