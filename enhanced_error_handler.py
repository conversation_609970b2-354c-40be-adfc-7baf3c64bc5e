#!/usr/bin/env python3
"""
Enhanced Error Handler for Agent-S
Provides comprehensive error detection, recovery, and user-friendly reporting
"""

import os
import sys
import time
import traceback
import logging
import json
from typing import Dict, List, Optional, Callable, Any, Tuple
from enum import Enum
from dataclasses import dataclass
from datetime import datetime, timedelta
import pyautogui
import psutil

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class ErrorSeverity(Enum):
    """Error severity levels"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"

class ErrorCategory(Enum):
    """Error categories for better classification"""
    SYSTEM = "system"
    APPLICATION = "application"
    NETWORK = "network"
    USER_INPUT = "user_input"
    AUTOMATION = "automation"
    AI_MODEL = "ai_model"
    UNKNOWN = "unknown"

@dataclass
class ErrorContext:
    """Context information for errors"""
    timestamp: datetime
    error_type: str
    message: str
    severity: ErrorSeverity
    category: ErrorCategory
    stack_trace: str
    system_info: Dict
    recovery_attempted: bool = False
    recovery_successful: bool = False
    user_action_required: bool = False

class EnhancedErrorHandler:
    """Enhanced error handling system with recovery mechanisms"""
    
    def __init__(self):
        self.error_history = []
        self.recovery_strategies = {}
        self.error_patterns = {}
        self.max_retry_attempts = 3
        self.retry_delay = 1.0
        
        # Initialize recovery strategies
        self._setup_recovery_strategies()
        
        # Initialize error patterns
        self._setup_error_patterns()
        
        # System monitoring
        self.system_health = {
            'cpu_usage': 0.0,
            'memory_usage': 0.0,
            'disk_usage': 0.0,
            'last_check': datetime.now()
        }
    
    def _setup_recovery_strategies(self):
        """Setup automated recovery strategies for common errors"""
        self.recovery_strategies = {
            'app_launch_failed': self._recover_app_launch,
            'screenshot_failed': self._recover_screenshot,
            'ai_model_timeout': self._recover_ai_timeout,
            'automation_stuck': self._recover_automation_stuck,
            'memory_error': self._recover_memory_error,
            'network_error': self._recover_network_error,
            'permission_denied': self._recover_permission_denied
        }
    
    def _setup_error_patterns(self):
        """Setup patterns to identify common error types"""
        self.error_patterns = {
            'app_launch_failed': [
                'FileNotFoundError',
                'PermissionError',
                'OSError',
                'application not found',
                'failed to launch'
            ],
            'screenshot_failed': [
                'screenshot',
                'PIL',
                'image capture',
                'display error'
            ],
            'ai_model_timeout': [
                'timeout',
                'connection timeout',
                'model timeout',
                'request timeout'
            ],
            'automation_stuck': [
                'pyautogui',
                'automation failed',
                'element not found',
                'click failed'
            ],
            'memory_error': [
                'MemoryError',
                'out of memory',
                'insufficient memory'
            ],
            'network_error': [
                'ConnectionError',
                'NetworkError',
                'requests.exceptions',
                'network unreachable'
            ],
            'permission_denied': [
                'PermissionError',
                'Access denied',
                'permission denied',
                'unauthorized'
            ]
        }
    
    def handle_error(self, error: Exception, context: Dict = None) -> ErrorContext:
        """
        Handle an error with automatic recovery attempts
        
        Args:
            error: The exception that occurred
            context: Additional context information
            
        Returns:
            ErrorContext with error details and recovery status
        """
        # Create error context
        error_context = self._create_error_context(error, context)
        
        # Log the error
        self._log_error(error_context)
        
        # Add to history
        self.error_history.append(error_context)
        
        # Attempt recovery
        recovery_success = self._attempt_recovery(error_context)
        error_context.recovery_attempted = True
        error_context.recovery_successful = recovery_success
        
        # Update system health
        self._update_system_health()
        
        return error_context
    
    def _create_error_context(self, error: Exception, context: Dict = None) -> ErrorContext:
        """Create comprehensive error context"""
        error_type = type(error).__name__
        message = str(error)
        stack_trace = traceback.format_exc()
        
        # Determine severity and category
        severity = self._determine_severity(error_type, message)
        category = self._determine_category(error_type, message)
        
        # Gather system information
        system_info = self._gather_system_info()
        
        # Add user context if provided
        if context:
            system_info.update(context)
        
        return ErrorContext(
            timestamp=datetime.now(),
            error_type=error_type,
            message=message,
            severity=severity,
            category=category,
            stack_trace=stack_trace,
            system_info=system_info
        )
    
    def _determine_severity(self, error_type: str, message: str) -> ErrorSeverity:
        """Determine error severity based on type and message"""
        critical_patterns = ['MemoryError', 'SystemExit', 'KeyboardInterrupt']
        high_patterns = ['PermissionError', 'FileNotFoundError', 'ConnectionError']
        medium_patterns = ['ValueError', 'TypeError', 'AttributeError']
        
        if any(pattern in error_type for pattern in critical_patterns):
            return ErrorSeverity.CRITICAL
        elif any(pattern in error_type for pattern in high_patterns):
            return ErrorSeverity.HIGH
        elif any(pattern in error_type for pattern in medium_patterns):
            return ErrorSeverity.MEDIUM
        else:
            return ErrorSeverity.LOW
    
    def _determine_category(self, error_type: str, message: str) -> ErrorCategory:
        """Determine error category based on type and message"""
        for category, patterns in self.error_patterns.items():
            if any(pattern.lower() in message.lower() or pattern in error_type for pattern in patterns):
                if 'app_launch' in category or 'automation' in category:
                    return ErrorCategory.AUTOMATION
                elif 'ai_model' in category:
                    return ErrorCategory.AI_MODEL
                elif 'network' in category:
                    return ErrorCategory.NETWORK
                elif 'permission' in category:
                    return ErrorCategory.SYSTEM
                else:
                    return ErrorCategory.APPLICATION
        
        return ErrorCategory.UNKNOWN
    
    def _gather_system_info(self) -> Dict:
        """Gather current system information"""
        try:
            return {
                'cpu_percent': psutil.cpu_percent(),
                'memory_percent': psutil.virtual_memory().percent,
                'disk_percent': psutil.disk_usage('/').percent if os.name != 'nt' else psutil.disk_usage('C:').percent,
                'running_processes': len(psutil.pids()),
                'screen_size': pyautogui.size(),
                'python_version': sys.version,
                'platform': sys.platform
            }
        except Exception as e:
            logger.warning(f"Failed to gather system info: {e}")
            return {'error': 'Failed to gather system info'}
    
    def _log_error(self, error_context: ErrorContext):
        """Log error with appropriate level"""
        severity_map = {
            ErrorSeverity.LOW: logger.info,
            ErrorSeverity.MEDIUM: logger.warning,
            ErrorSeverity.HIGH: logger.error,
            ErrorSeverity.CRITICAL: logger.critical
        }
        
        log_func = severity_map.get(error_context.severity, logger.error)
        log_func(f"🚨 {error_context.severity.value.upper()} ERROR: {error_context.error_type} - {error_context.message}")
    
    def _attempt_recovery(self, error_context: ErrorContext) -> bool:
        """Attempt to recover from the error"""
        # Identify error pattern
        error_pattern = self._identify_error_pattern(error_context.message, error_context.error_type)
        
        if error_pattern and error_pattern in self.recovery_strategies:
            logger.info(f"🔧 Attempting recovery for {error_pattern}...")
            try:
                recovery_func = self.recovery_strategies[error_pattern]
                return recovery_func(error_context)
            except Exception as e:
                logger.error(f"Recovery attempt failed: {e}")
                return False
        
        logger.warning(f"No recovery strategy available for {error_context.error_type}")
        return False
    
    def _identify_error_pattern(self, message: str, error_type: str) -> Optional[str]:
        """Identify which error pattern matches the current error"""
        for pattern_name, patterns in self.error_patterns.items():
            if any(pattern.lower() in message.lower() or pattern in error_type for pattern in patterns):
                return pattern_name
        return None
    
    def _update_system_health(self):
        """Update system health metrics"""
        try:
            self.system_health = {
                'cpu_usage': psutil.cpu_percent(),
                'memory_usage': psutil.virtual_memory().percent,
                'disk_usage': psutil.disk_usage('C:').percent,
                'last_check': datetime.now()
            }
        except Exception as e:
            logger.warning(f"Failed to update system health: {e}")
    
    # Recovery strategy implementations
    def _recover_app_launch(self, error_context: ErrorContext) -> bool:
        """Recover from application launch failures"""
        logger.info("🔧 Attempting app launch recovery...")
        
        # Try alternative launch methods
        try:
            # Method 1: Try Windows key + search
            pyautogui.press('win')
            time.sleep(0.5)
            pyautogui.typewrite('notepad')  # Safe fallback app
            time.sleep(0.5)
            pyautogui.press('enter')
            time.sleep(1)
            
            logger.info("✅ App launch recovery successful")
            return True
        except Exception as e:
            logger.error(f"App launch recovery failed: {e}")
            return False
    
    def _recover_screenshot(self, error_context: ErrorContext) -> bool:
        """Recover from screenshot failures"""
        logger.info("🔧 Attempting screenshot recovery...")
        
        try:
            # Wait a moment and retry
            time.sleep(1)
            screenshot = pyautogui.screenshot()
            logger.info("✅ Screenshot recovery successful")
            return True
        except Exception as e:
            logger.error(f"Screenshot recovery failed: {e}")
            return False
    
    def _recover_ai_timeout(self, error_context: ErrorContext) -> bool:
        """Recover from AI model timeouts"""
        logger.info("🔧 Attempting AI timeout recovery...")
        
        # Implement retry with exponential backoff
        for attempt in range(self.max_retry_attempts):
            try:
                delay = self.retry_delay * (2 ** attempt)
                logger.info(f"Retrying in {delay}s (attempt {attempt + 1}/{self.max_retry_attempts})")
                time.sleep(delay)
                
                # This would be where you retry the AI call
                # For now, just simulate success
                logger.info("✅ AI timeout recovery successful")
                return True
            except Exception as e:
                logger.warning(f"AI recovery attempt {attempt + 1} failed: {e}")
                continue
        
        return False
    
    def _recover_automation_stuck(self, error_context: ErrorContext) -> bool:
        """Recover from stuck automation"""
        logger.info("🔧 Attempting automation recovery...")
        
        try:
            # Press Escape to cancel any ongoing operations
            pyautogui.press('escape')
            time.sleep(0.5)
            
            # Move mouse to center of screen
            screen_width, screen_height = pyautogui.size()
            pyautogui.moveTo(screen_width // 2, screen_height // 2)
            
            logger.info("✅ Automation recovery successful")
            return True
        except Exception as e:
            logger.error(f"Automation recovery failed: {e}")
            return False
    
    def _recover_memory_error(self, error_context: ErrorContext) -> bool:
        """Recover from memory errors"""
        logger.info("🔧 Attempting memory recovery...")
        
        try:
            # Force garbage collection
            import gc
            gc.collect()
            
            # Check if memory usage improved
            current_memory = psutil.virtual_memory().percent
            if current_memory < 90:  # If memory usage is below 90%
                logger.info("✅ Memory recovery successful")
                return True
            else:
                logger.warning("Memory usage still high after recovery attempt")
                return False
        except Exception as e:
            logger.error(f"Memory recovery failed: {e}")
            return False
    
    def _recover_network_error(self, error_context: ErrorContext) -> bool:
        """Recover from network errors"""
        logger.info("🔧 Attempting network recovery...")
        
        try:
            # Wait and retry
            time.sleep(2)
            
            # Test basic connectivity (simplified)
            import socket
            socket.create_connection(("8.8.8.8", 53), timeout=3)
            
            logger.info("✅ Network recovery successful")
            return True
        except Exception as e:
            logger.error(f"Network recovery failed: {e}")
            return False
    
    def _recover_permission_denied(self, error_context: ErrorContext) -> bool:
        """Recover from permission errors"""
        logger.info("🔧 Attempting permission recovery...")
        
        # For permission errors, usually need user intervention
        error_context.user_action_required = True
        logger.warning("⚠️ Permission error requires user intervention")
        return False

    def get_error_summary(self) -> Dict:
        """Get summary of error history and system health"""
        if not self.error_history:
            return {
                'total_errors': 0,
                'recent_errors': 0,
                'recovery_rate': 0.0,
                'system_health': self.system_health,
                'recommendations': ['System running smoothly']
            }

        total_errors = len(self.error_history)
        recent_errors = len([e for e in self.error_history if (datetime.now() - e.timestamp).seconds < 300])  # Last 5 minutes

        recovery_attempts = len([e for e in self.error_history if e.recovery_attempted])
        successful_recoveries = len([e for e in self.error_history if e.recovery_successful])
        recovery_rate = (successful_recoveries / recovery_attempts * 100) if recovery_attempts > 0 else 0

        # Generate recommendations
        recommendations = self._generate_recommendations()

        return {
            'total_errors': total_errors,
            'recent_errors': recent_errors,
            'recovery_rate': f"{recovery_rate:.1f}%",
            'system_health': self.system_health,
            'recommendations': recommendations,
            'error_categories': self._get_error_category_stats(),
            'severity_distribution': self._get_severity_stats()
        }

    def _generate_recommendations(self) -> List[str]:
        """Generate recommendations based on error patterns"""
        recommendations = []

        # Check system health
        if self.system_health['memory_usage'] > 80:
            recommendations.append("🔧 High memory usage detected - consider closing unused applications")

        if self.system_health['cpu_usage'] > 90:
            recommendations.append("🔧 High CPU usage detected - system may be overloaded")

        # Check error patterns
        recent_errors = [e for e in self.error_history if (datetime.now() - e.timestamp).seconds < 600]  # Last 10 minutes

        if len(recent_errors) > 5:
            recommendations.append("⚠️ Multiple recent errors - consider restarting the system")

        # Check for specific error patterns
        app_launch_errors = [e for e in recent_errors if 'app_launch' in str(e.message).lower()]
        if len(app_launch_errors) > 2:
            recommendations.append("🚀 Multiple app launch failures - check application installations")

        network_errors = [e for e in recent_errors if e.category == ErrorCategory.NETWORK]
        if len(network_errors) > 1:
            recommendations.append("🌐 Network issues detected - check internet connection")

        if not recommendations:
            recommendations.append("✅ System appears to be running well")

        return recommendations

    def _get_error_category_stats(self) -> Dict:
        """Get statistics by error category"""
        category_counts = {}
        for error in self.error_history:
            category = error.category.value
            category_counts[category] = category_counts.get(category, 0) + 1

        return category_counts

    def _get_severity_stats(self) -> Dict:
        """Get statistics by error severity"""
        severity_counts = {}
        for error in self.error_history:
            severity = error.severity.value
            severity_counts[severity] = severity_counts.get(severity, 0) + 1

        return severity_counts

    def export_error_log(self, filename: str = None) -> str:
        """Export error history to JSON file"""
        if filename is None:
            filename = f"agent_s_error_log_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"

        export_data = {
            'export_timestamp': datetime.now().isoformat(),
            'summary': self.get_error_summary(),
            'errors': []
        }

        for error in self.error_history:
            export_data['errors'].append({
                'timestamp': error.timestamp.isoformat(),
                'error_type': error.error_type,
                'message': error.message,
                'severity': error.severity.value,
                'category': error.category.value,
                'recovery_attempted': error.recovery_attempted,
                'recovery_successful': error.recovery_successful,
                'user_action_required': error.user_action_required,
                'system_info': error.system_info
            })

        try:
            with open(filename, 'w') as f:
                json.dump(export_data, f, indent=2)
            logger.info(f"✅ Error log exported to {filename}")
            return filename
        except Exception as e:
            logger.error(f"Failed to export error log: {e}")
            return ""

    def clear_error_history(self, older_than_hours: int = 24):
        """Clear error history older than specified hours"""
        cutoff_time = datetime.now() - timedelta(hours=older_than_hours)
        original_count = len(self.error_history)

        self.error_history = [e for e in self.error_history if e.timestamp > cutoff_time]

        cleared_count = original_count - len(self.error_history)
        logger.info(f"🧹 Cleared {cleared_count} old error records")

    def safe_execute(self, func: Callable, *args, **kwargs) -> Tuple[bool, Any, Optional[ErrorContext]]:
        """
        Safely execute a function with automatic error handling

        Args:
            func: Function to execute
            *args: Function arguments
            **kwargs: Function keyword arguments

        Returns:
            Tuple of (success, result, error_context)
        """
        try:
            result = func(*args, **kwargs)
            return True, result, None
        except Exception as e:
            error_context = self.handle_error(e, {'function': func.__name__, 'args': str(args)})
            return False, None, error_context


# Example usage and testing
if __name__ == "__main__":
    from datetime import timedelta

    error_handler = EnhancedErrorHandler()

    print("🛡️ Enhanced Error Handler - Agent-S")
    print("=" * 40)

    # Test error handling
    def test_function_that_fails():
        raise ValueError("This is a test error")

    def test_function_that_works():
        return "Success!"

    # Test safe execution
    print("\n🧪 Testing safe execution...")

    # Test successful function
    success, result, error_ctx = error_handler.safe_execute(test_function_that_works)
    print(f"✅ Success: {success}, Result: {result}")

    # Test failing function
    success, result, error_ctx = error_handler.safe_execute(test_function_that_fails)
    print(f"❌ Success: {success}, Error: {error_ctx.message if error_ctx else 'None'}")

    # Show error summary
    print("\n📊 Error Summary:")
    summary = error_handler.get_error_summary()
    for key, value in summary.items():
        if key != 'system_health':
            print(f"  {key}: {value}")

    print("\n🖥️ System Health:")
    for key, value in summary['system_health'].items():
        print(f"  {key}: {value}")

    # Export error log
    log_file = error_handler.export_error_log()
    if log_file:
        print(f"\n📄 Error log exported to: {log_file}")

    print("\n👋 Error handler test complete!")
