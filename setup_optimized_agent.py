#!/usr/bin/env python3
"""
Setup Script for Optimized Agent-S
Automated installation and configuration
"""

import os
import sys
import subprocess
import json
from pathlib import Path

def check_python_version():
    """Check if Python version is compatible"""
    if sys.version_info < (3, 7):
        print("❌ Python 3.7 or higher is required")
        print(f"Current version: {sys.version}")
        return False
    
    print(f"✅ Python version: {sys.version.split()[0]}")
    return True

def install_dependencies():
    """Install required dependencies"""
    print("\n📦 Installing dependencies...")
    
    required_packages = [
        'pyautogui>=0.9.54',
        'pillow>=8.0.0',
        'psutil>=5.8.0',
        'opencv-python>=4.5.0',
        'numpy>=1.20.0'
    ]
    
    optional_packages = [
        'openai>=1.0.0',
        'anthropic>=0.3.0'
    ]
    
    # Install required packages
    for package in required_packages:
        try:
            print(f"Installing {package}...")
            subprocess.check_call([sys.executable, '-m', 'pip', 'install', package])
            print(f"✅ {package} installed successfully")
        except subprocess.CalledProcessError as e:
            print(f"❌ Failed to install {package}: {e}")
            return False
    
    # Install optional packages (don't fail if these don't install)
    print("\n🔧 Installing optional AI packages...")
    for package in optional_packages:
        try:
            print(f"Installing {package}...")
            subprocess.check_call([sys.executable, '-m', 'pip', 'install', package])
            print(f"✅ {package} installed successfully")
        except subprocess.CalledProcessError:
            print(f"⚠️ {package} installation failed (optional)")
    
    return True

def setup_directories():
    """Create necessary directories"""
    print("\n📁 Setting up directories...")
    
    directories = [
        'agent_memory',
        'screenshots',
        'logs',
        'workflows',
        'config'
    ]
    
    for directory in directories:
        Path(directory).mkdir(exist_ok=True)
        print(f"✅ Created directory: {directory}")

def create_config_files():
    """Create default configuration files"""
    print("\n⚙️ Creating configuration files...")
    
    # Default agent configuration
    default_config = {
        "performance_optimization": True,
        "error_recovery": True,
        "context_memory": True,
        "ai_vision": True,
        "automation_speed": "fast",
        "retry_attempts": 3,
        "screenshot_quality": "high",
        "logging_level": "INFO"
    }
    
    config_file = Path("config/agent_config.json")
    with open(config_file, 'w') as f:
        json.dump(default_config, f, indent=2)
    
    print(f"✅ Created: {config_file}")
    
    # Environment template
    env_template = """# Optimized Agent-S Environment Variables
# Copy this to .env and fill in your API keys

# OpenAI API Key (for GPT-4V vision capabilities)
OPENAI_API_KEY=your_openai_api_key_here

# Anthropic API Key (for Claude vision capabilities)
ANTHROPIC_API_KEY=your_anthropic_api_key_here

# Optional: Custom settings
AGENT_LOG_LEVEL=INFO
AGENT_MEMORY_DIR=agent_memory
AGENT_SCREENSHOT_DIR=screenshots
"""
    
    env_file = Path(".env.template")
    with open(env_file, 'w') as f:
        f.write(env_template)
    
    print(f"✅ Created: {env_file}")

def create_launcher_scripts():
    """Create convenient launcher scripts"""
    print("\n🚀 Creating launcher scripts...")
    
    # Windows batch file
    windows_launcher = """@echo off
echo Starting Optimized Agent-S...
python unified_agent_interface.py --interactive
pause
"""
    
    with open("start_agent.bat", 'w') as f:
        f.write(windows_launcher)
    
    print("✅ Created: start_agent.bat")
    
    # Unix shell script
    unix_launcher = """#!/bin/bash
echo "Starting Optimized Agent-S..."
python3 unified_agent_interface.py --interactive
"""
    
    with open("start_agent.sh", 'w') as f:
        f.write(unix_launcher)
    
    # Make shell script executable
    try:
        os.chmod("start_agent.sh", 0o755)
        print("✅ Created: start_agent.sh")
    except:
        print("⚠️ Created start_agent.sh (may need chmod +x)")

def run_initial_test():
    """Run initial system test"""
    print("\n🧪 Running initial system test...")
    
    try:
        # Test imports
        import pyautogui
        import psutil
        from PIL import Image
        import cv2
        import numpy as np
        
        print("✅ All required packages imported successfully")
        
        # Test screen access
        screen_size = pyautogui.size()
        print(f"✅ Screen access working: {screen_size[0]}x{screen_size[1]}")
        
        # Test screenshot
        screenshot = pyautogui.screenshot()
        print("✅ Screenshot capability working")
        
        return True
        
    except Exception as e:
        print(f"❌ System test failed: {e}")
        return False

def print_setup_complete():
    """Print setup completion message"""
    print("\n" + "="*60)
    print("🎉 OPTIMIZED AGENT-S SETUP COMPLETE!")
    print("="*60)
    
    print("\n🚀 Quick Start:")
    print("1. Set up your API keys (optional but recommended):")
    print("   - Copy .env.template to .env")
    print("   - Add your OpenAI and/or Anthropic API keys")
    
    print("\n2. Start the system:")
    print("   Windows: Double-click start_agent.bat")
    print("   Linux/Mac: ./start_agent.sh")
    print("   Manual: python unified_agent_interface.py --interactive")
    
    print("\n3. Try these commands:")
    print("   • 'Open calculator'")
    print("   • 'Take a screenshot'")
    print("   • 'Create Excel sales report'")
    print("   • 'Show performance report'")
    
    print("\n📚 Documentation:")
    print("   • README: OPTIMIZED_AGENT_S_README.md")
    print("   • Test system: python test_optimized_agent.py")
    print("   • Configuration: config/agent_config.json")
    
    print("\n🎯 What's Optimized:")
    print("   ✅ Smart context management")
    print("   ✅ Intelligent tool selection")
    print("   ✅ Comprehensive error recovery")
    print("   ✅ Real AI vision integration")
    print("   ✅ Natural language interface")
    print("   ✅ Business-ready workflows")

def main():
    """Main setup function"""
    print("🚀 Optimized Agent-S Setup")
    print("="*40)
    
    # Check Python version
    if not check_python_version():
        return False
    
    # Install dependencies
    if not install_dependencies():
        print("❌ Dependency installation failed")
        return False
    
    # Setup directories
    setup_directories()
    
    # Create configuration files
    create_config_files()
    
    # Create launcher scripts
    create_launcher_scripts()
    
    # Run initial test
    if not run_initial_test():
        print("⚠️ Initial test failed - system may not work properly")
    
    # Print completion message
    print_setup_complete()
    
    return True

if __name__ == "__main__":
    try:
        success = main()
        if success:
            print("\n✅ Setup completed successfully!")
        else:
            print("\n❌ Setup failed!")
            sys.exit(1)
    except KeyboardInterrupt:
        print("\n❌ Setup interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Setup failed with error: {e}")
        sys.exit(1)
