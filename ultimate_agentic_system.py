#!/usr/bin/env python3
"""
ULTIMATE AGENTIC SYSTEM
Fully autonomous agent with proper tools, task understanding, and real capabilities
"""

import os
import sys
import time
import json
import base64
import io
import requests
import pyautogui
import subprocess
import psutil
import threading
from PIL import Image
from typing import Dict, List, Any, Optional
import logging

# Set the OpenAI API key
os.environ['OPENAI_API_KEY'] = '********************************************************************************************************************************************************************'

# Configure for safety and speed
pyautogui.FAILSAFE = True
pyautogui.PAUSE = 0.2

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class UltimateAgenticSystem:
    """
    FULLY AGENTIC SYSTEM with:
    - Real AI vision and understanding
    - Proper tool system
    - Task decomposition and planning
    - Autonomous execution
    - Error handling and recovery
    """
    
    def __init__(self):
        self.openai_key = os.getenv('OPENAI_API_KEY')
        self.screen_width, self.screen_height = pyautogui.size()
        
        # Tool registry - REAL tools the agent can use
        self.tools = {
            'take_screenshot': self.take_screenshot,
            'click_coordinates': self.click_coordinates,
            'type_text': self.type_text,
            'press_key': self.press_key,
            'open_application': self.open_application,
            'find_element': self.find_element,
            'scroll_screen': self.scroll_screen,
            'get_running_apps': self.get_running_apps,
            'execute_command': self.execute_command,
            'wait_seconds': self.wait_seconds,
            'analyze_screen': self.analyze_screen,
            'move_mouse': self.move_mouse,
            'right_click': self.right_click,
            'double_click': self.double_click,
            'drag_and_drop': self.drag_and_drop
        }
        
        # Task execution state
        self.current_task = None
        self.task_history = []
        self.execution_context = {}
        
        print("🧠 ULTIMATE AGENTIC SYSTEM INITIALIZED")
        print(f"🖥️ Screen: {self.screen_width}x{self.screen_height}")
        print(f"🔧 Available Tools: {len(self.tools)}")
        print("✅ Ready for fully autonomous operation!")
    
    # ==================== CORE AI REASONING ====================
    
    def understand_and_plan_task(self, user_request: str) -> Dict:
        """Use AI to understand task and create detailed execution plan"""
        try:
            # Take screenshot for context
            screenshot_b64 = self.take_screenshot()
            
            prompt = f"""
You are an advanced AI agent controlling a Windows computer. You have access to these tools:
{list(self.tools.keys())}

USER REQUEST: "{user_request}"

Analyze the current screen and create a detailed execution plan. You must:

1. UNDERSTAND the task completely
2. ANALYZE what's currently on screen
3. PLAN the exact sequence of actions needed
4. CHOOSE the right tools for each step

Respond in JSON format:
{{
    "task_understanding": "What the user wants accomplished",
    "current_screen_analysis": "What you see on the screen right now",
    "execution_plan": [
        {{
            "step": 1,
            "tool": "tool_name",
            "parameters": {{"param": "value"}},
            "purpose": "why this step is needed",
            "expected_result": "what should happen"
        }}
    ],
    "success_criteria": "How to know the task is complete",
    "estimated_steps": 5,
    "complexity": "simple|moderate|complex"
}}

Be specific about coordinates, text, and parameters. Plan for real execution.
"""

            response = self.call_openai_vision(prompt, screenshot_b64)
            
            if response:
                try:
                    # Extract JSON from response
                    import re
                    json_match = re.search(r'\{.*\}', response, re.DOTALL)
                    if json_match:
                        plan = json.loads(json_match.group())
                        print(f"🎯 Task Understanding: {plan.get('task_understanding', 'Unknown')}")
                        print(f"📊 Screen Analysis: {plan.get('current_screen_analysis', 'Unknown')[:100]}...")
                        print(f"📋 Execution Plan: {plan.get('estimated_steps', 0)} steps")
                        return plan
                except json.JSONDecodeError:
                    pass
            
            # Fallback plan
            return self.create_fallback_plan(user_request)
            
        except Exception as e:
            print(f"❌ Planning failed: {e}")
            return self.create_fallback_plan(user_request)
    
    def create_fallback_plan(self, user_request: str) -> Dict:
        """Create a simple fallback plan for common requests"""
        request_lower = user_request.lower()
        
        if 'calculator' in request_lower or 'calc' in request_lower:
            return {
                "task_understanding": "Open calculator application",
                "execution_plan": [
                    {"step": 1, "tool": "open_application", "parameters": {"app_name": "calculator"}, "purpose": "Open calculator"}
                ],
                "complexity": "simple"
            }
        elif 'notepad' in request_lower:
            return {
                "task_understanding": "Open notepad text editor",
                "execution_plan": [
                    {"step": 1, "tool": "open_application", "parameters": {"app_name": "notepad"}, "purpose": "Open notepad"}
                ],
                "complexity": "simple"
            }
        else:
            return {
                "task_understanding": f"Execute user request: {user_request}",
                "execution_plan": [
                    {"step": 1, "tool": "analyze_screen", "parameters": {}, "purpose": "Understand current state"}
                ],
                "complexity": "moderate"
            }
    
    def execute_autonomous_task(self, user_request: str) -> bool:
        """Autonomously execute a task with full AI reasoning"""
        print(f"\n🚀 AUTONOMOUS TASK EXECUTION")
        print(f"📝 Request: {user_request}")
        print("=" * 60)
        
        # Step 1: Understand and plan
        print("🧠 Phase 1: Understanding and Planning...")
        plan = self.understand_and_plan_task(user_request)
        
        if not plan or not plan.get('execution_plan'):
            print("❌ Could not create execution plan")
            return False
        
        self.current_task = {
            'request': user_request,
            'plan': plan,
            'start_time': time.time(),
            'steps_completed': 0
        }
        
        # Step 2: Execute plan autonomously
        print("🔄 Phase 2: Autonomous Execution...")
        execution_plan = plan['execution_plan']
        
        for i, step in enumerate(execution_plan, 1):
            print(f"\n📋 Step {i}/{len(execution_plan)}: {step.get('purpose', 'Unknown purpose')}")
            
            tool_name = step.get('tool')
            parameters = step.get('parameters', {})
            
            if tool_name not in self.tools:
                print(f"❌ Unknown tool: {tool_name}")
                continue
            
            try:
                # Execute the tool
                tool_function = self.tools[tool_name]

                # Handle parameter mapping for common issues
                if tool_name == 'open_application' and 'application_name' in parameters:
                    parameters['app_name'] = parameters.pop('application_name')

                result = tool_function(**parameters)
                
                if result:
                    print(f"✅ Step {i} completed successfully")
                    self.current_task['steps_completed'] += 1
                else:
                    print(f"⚠️ Step {i} had issues but continuing...")
                
                # Brief pause between steps
                time.sleep(0.5)
                
            except Exception as e:
                print(f"❌ Step {i} failed: {e}")
                
                # Try to recover with AI guidance
                if i < len(execution_plan):
                    print("🔧 Attempting AI-guided recovery...")
                    recovery_success = self.attempt_recovery(user_request, step)
                    if not recovery_success:
                        print("❌ Recovery failed, stopping execution")
                        return False
        
        # Step 3: Verify completion
        print("\n🔍 Phase 3: Verification...")
        completion_verified = self.verify_task_completion(user_request, plan)
        
        total_time = time.time() - self.current_task['start_time']
        
        if completion_verified:
            print(f"🎉 TASK COMPLETED SUCCESSFULLY!")
            print(f"⏱️ Total time: {total_time:.1f} seconds")
            print(f"📊 Steps completed: {self.current_task['steps_completed']}/{len(execution_plan)}")
            return True
        else:
            print(f"⚠️ Task may be incomplete")
            return False
    
    def attempt_recovery(self, original_request: str, failed_step: Dict) -> bool:
        """Use AI to attempt recovery from failed step"""
        try:
            screenshot_b64 = self.take_screenshot()
            
            prompt = f"""
RECOVERY MODE: A step failed during task execution.

Original request: "{original_request}"
Failed step: {failed_step}

Look at the current screen and suggest a recovery action using these tools:
{list(self.tools.keys())}

Respond with JSON:
{{
    "recovery_action": "tool_name",
    "parameters": {{"param": "value"}},
    "explanation": "why this should work"
}}
"""
            
            response = self.call_openai_vision(prompt, screenshot_b64)
            
            if response:
                import re
                json_match = re.search(r'\{.*\}', response, re.DOTALL)
                if json_match:
                    recovery = json.loads(json_match.group())
                    tool_name = recovery.get('recovery_action')
                    parameters = recovery.get('parameters', {})
                    
                    if tool_name in self.tools:
                        print(f"🔧 Recovery: {recovery.get('explanation', 'Attempting fix')}")
                        result = self.tools[tool_name](**parameters)
                        return result
            
            return False
            
        except Exception as e:
            print(f"❌ Recovery attempt failed: {e}")
            return False
    
    def verify_task_completion(self, original_request: str, plan: Dict) -> bool:
        """Use AI to verify if the task was completed successfully"""
        try:
            screenshot_b64 = self.take_screenshot()
            success_criteria = plan.get('success_criteria', 'Task appears complete')
            
            prompt = f"""
VERIFICATION: Check if this task was completed successfully.

Original request: "{original_request}"
Success criteria: "{success_criteria}"

Look at the current screen and determine if the task was completed.

Respond with JSON:
{{
    "task_completed": true/false,
    "evidence": "what on screen shows completion or failure",
    "confidence": "high|medium|low"
}}
"""
            
            response = self.call_openai_vision(prompt, screenshot_b64)
            
            if response:
                import re
                json_match = re.search(r'\{.*\}', response, re.DOTALL)
                if json_match:
                    verification = json.loads(json_match.group())
                    completed = verification.get('task_completed', False)
                    evidence = verification.get('evidence', 'Unknown')
                    confidence = verification.get('confidence', 'low')
                    
                    print(f"🔍 Verification: {evidence}")
                    print(f"🎯 Confidence: {confidence}")
                    
                    return completed
            
            return True  # Assume success if verification fails
            
        except Exception as e:
            print(f"❌ Verification failed: {e}")
            return True
    
    # ==================== TOOL IMPLEMENTATIONS ====================
    
    def take_screenshot(self) -> str:
        """Take screenshot and return base64 encoded image"""
        try:
            screenshot = pyautogui.screenshot()
            
            # Optimize for AI processing
            if screenshot.width > 1280:
                ratio = 1280 / screenshot.width
                new_height = int(screenshot.height * ratio)
                screenshot = screenshot.resize((1280, new_height), Image.LANCZOS)
            
            buffer = io.BytesIO()
            screenshot.save(buffer, format='JPEG', quality=85)
            return base64.b64encode(buffer.getvalue()).decode('utf-8')
            
        except Exception as e:
            print(f"❌ Screenshot failed: {e}")
            return ""
    
    def click_coordinates(self, x: int, y: int) -> bool:
        """Click at specific coordinates"""
        try:
            print(f"🖱️ Clicking at ({x}, {y})")
            pyautogui.click(x, y)
            time.sleep(0.3)
            return True
        except Exception as e:
            print(f"❌ Click failed: {e}")
            return False
    
    def type_text(self, text: str) -> bool:
        """Type text"""
        try:
            print(f"⌨️ Typing: {text}")
            pyautogui.typewrite(text, interval=0.03)
            time.sleep(0.2)
            return True
        except Exception as e:
            print(f"❌ Typing failed: {e}")
            return False
    
    def press_key(self, key: str) -> bool:
        """Press a key"""
        try:
            print(f"🔑 Pressing: {key}")
            pyautogui.press(key)
            time.sleep(0.2)
            return True
        except Exception as e:
            print(f"❌ Key press failed: {e}")
            return False
    
    def open_application(self, app_name: str) -> bool:
        """Open an application"""
        try:
            print(f"🚀 Opening {app_name}")
            
            # Common app mappings
            app_commands = {
                'calculator': 'calc',
                'calc': 'calc',
                'notepad': 'notepad',
                'chrome': 'chrome',
                'browser': 'chrome',
                'excel': 'excel',
                'word': 'winword',
                'cmd': 'cmd',
                'powershell': 'powershell'
            }
            
            app_lower = app_name.lower()
            
            # Try direct command
            if app_lower in app_commands:
                try:
                    subprocess.Popen(app_commands[app_lower], shell=True)
                    time.sleep(2)
                    return True
                except:
                    pass
            
            # Fallback: Windows search
            pyautogui.press('win')
            time.sleep(0.8)
            pyautogui.typewrite(app_name)
            time.sleep(1)
            pyautogui.press('enter')
            time.sleep(2)
            return True
            
        except Exception as e:
            print(f"❌ App opening failed: {e}")
            return False
    
    def find_element(self, description: str) -> Optional[Dict]:
        """Use AI to find an element on screen"""
        try:
            screenshot_b64 = self.take_screenshot()
            
            prompt = f"""
Find this element on the screen: "{description}"

Look for buttons, text, icons, or UI elements matching this description.

Respond with JSON:
{{
    "found": true/false,
    "coordinates": [x, y] or null,
    "description": "what you found"
}}
"""
            
            response = self.call_openai_vision(prompt, screenshot_b64)
            
            if response:
                import re
                json_match = re.search(r'\{.*\}', response, re.DOTALL)
                if json_match:
                    result = json.loads(json_match.group())
                    if result.get('found') and result.get('coordinates'):
                        print(f"🔍 Found: {result.get('description', 'Element')}")
                        return result
            
            return None
            
        except Exception as e:
            print(f"❌ Element search failed: {e}")
            return None
    
    def scroll_screen(self, direction: str = "down", amount: int = 3) -> bool:
        """Scroll the screen"""
        try:
            print(f"🖱️ Scrolling {direction}")
            x, y = self.screen_width // 2, self.screen_height // 2
            
            if direction.lower() == "down":
                pyautogui.scroll(-amount, x=x, y=y)
            else:
                pyautogui.scroll(amount, x=x, y=y)
            
            time.sleep(0.3)
            return True
        except Exception as e:
            print(f"❌ Scroll failed: {e}")
            return False
    
    def get_running_apps(self) -> List[str]:
        """Get list of running applications"""
        try:
            apps = []
            for proc in psutil.process_iter(['name']):
                try:
                    app_name = proc.info['name']
                    if app_name and not app_name.startswith('System'):
                        apps.append(app_name)
                except:
                    continue
            
            unique_apps = list(set(apps))[:20]  # Limit to 20 most common
            print(f"📱 Found {len(unique_apps)} running applications")
            return unique_apps
        except Exception as e:
            print(f"❌ App listing failed: {e}")
            return []
    
    def execute_command(self, command: str) -> bool:
        """Execute a system command"""
        try:
            print(f"💻 Executing: {command}")
            result = subprocess.run(command, shell=True, capture_output=True, text=True, timeout=10)
            return result.returncode == 0
        except Exception as e:
            print(f"❌ Command failed: {e}")
            return False
    
    def wait_seconds(self, seconds: float) -> bool:
        """Wait for specified seconds"""
        print(f"⏱️ Waiting {seconds} seconds")
        time.sleep(seconds)
        return True
    
    def analyze_screen(self) -> Dict:
        """Analyze current screen with AI"""
        try:
            screenshot_b64 = self.take_screenshot()
            
            prompt = """
Analyze the current screen and describe:
1. What applications are open
2. What UI elements are visible
3. What the user might want to do next

Respond with JSON:
{
    "applications": ["list of open apps"],
    "ui_elements": ["list of visible elements"],
    "suggestions": ["possible next actions"]
}
"""
            
            response = self.call_openai_vision(prompt, screenshot_b64)
            
            if response:
                import re
                json_match = re.search(r'\{.*\}', response, re.DOTALL)
                if json_match:
                    analysis = json.loads(json_match.group())
                    print(f"📊 Screen Analysis: {len(analysis.get('ui_elements', []))} elements found")
                    return analysis
            
            return {}
            
        except Exception as e:
            print(f"❌ Screen analysis failed: {e}")
            return {}
    
    def move_mouse(self, x: int, y: int) -> bool:
        """Move mouse to coordinates"""
        try:
            pyautogui.moveTo(x, y)
            time.sleep(0.1)
            return True
        except Exception as e:
            print(f"❌ Mouse move failed: {e}")
            return False
    
    def right_click(self, x: int, y: int) -> bool:
        """Right click at coordinates"""
        try:
            pyautogui.rightClick(x, y)
            time.sleep(0.3)
            return True
        except Exception as e:
            print(f"❌ Right click failed: {e}")
            return False
    
    def double_click(self, x: int, y: int) -> bool:
        """Double click at coordinates"""
        try:
            pyautogui.doubleClick(x, y)
            time.sleep(0.3)
            return True
        except Exception as e:
            print(f"❌ Double click failed: {e}")
            return False
    
    def drag_and_drop(self, start_x: int, start_y: int, end_x: int, end_y: int) -> bool:
        """Drag from start to end coordinates"""
        try:
            pyautogui.drag(end_x - start_x, end_y - start_y, duration=0.5, button='left')
            time.sleep(0.3)
            return True
        except Exception as e:
            print(f"❌ Drag and drop failed: {e}")
            return False
    
    # ==================== AI COMMUNICATION ====================
    
    def call_openai_vision(self, prompt: str, image_b64: str) -> Optional[str]:
        """Call OpenAI GPT-4V API"""
        try:
            headers = {
                'Authorization': f'Bearer {self.openai_key}',
                'Content-Type': 'application/json'
            }
            
            payload = {
                'model': 'gpt-4o',
                'messages': [{
                    'role': 'user',
                    'content': [
                        {'type': 'text', 'text': prompt},
                        {
                            'type': 'image_url',
                            'image_url': {
                                'url': f"data:image/jpeg;base64,{image_b64}",
                                'detail': 'high'
                            }
                        }
                    ]
                }],
                'max_tokens': 1500,
                'temperature': 0.1
            }
            
            response = requests.post(
                'https://api.openai.com/v1/chat/completions',
                headers=headers,
                json=payload,
                timeout=30
            )
            
            if response.status_code == 200:
                result = response.json()
                return result['choices'][0]['message']['content']
            else:
                print(f"❌ OpenAI API error: {response.status_code}")
                return None
                
        except Exception as e:
            print(f"❌ AI call failed: {e}")
            return None

    # ==================== INTERACTIVE INTERFACE ====================

    def run_agentic_mode(self):
        """Run the ultimate agentic system"""
        print("\n🧠 ULTIMATE AGENTIC SYSTEM - FULLY AUTONOMOUS")
        print("=" * 60)
        print("🎯 I am a FULLY AGENTIC AI that can:")
        print("  • Understand complex tasks completely")
        print("  • Plan detailed execution strategies")
        print("  • Use real tools to control your computer")
        print("  • Adapt and recover from errors")
        print("  • Verify task completion autonomously")
        print()
        print("💡 Try complex requests like:")
        print("  • 'Open calculator and compute 25 * 37 + 15'")
        print("  • 'Open notepad and write a shopping list'")
        print("  • 'Find and open Chrome browser'")
        print("  • 'Take a screenshot and analyze what you see'")
        print("  • 'Open Excel and create a simple spreadsheet'")
        print()
        print("🔧 Available Tools:")
        for tool in sorted(self.tools.keys()):
            print(f"  • {tool}")
        print()

        while True:
            try:
                request = input("🎯 What task should I autonomously execute? ").strip()

                if request.lower() in ['quit', 'exit', 'bye', 'stop']:
                    break

                if request.lower() in ['help', 'tools']:
                    print("\n🔧 Available Tools:")
                    for tool in sorted(self.tools.keys()):
                        print(f"  • {tool}")
                    continue

                if request.lower() in ['analyze', 'screen', 'what do you see']:
                    print("\n🔍 Analyzing current screen...")
                    analysis = self.analyze_screen()
                    if analysis:
                        print(f"📱 Applications: {', '.join(analysis.get('applications', []))}")
                        print(f"🎯 UI Elements: {len(analysis.get('ui_elements', []))} found")
                        print(f"💡 Suggestions: {', '.join(analysis.get('suggestions', []))}")
                    continue

                if request.lower() == 'demo':
                    self.run_demo_tasks()
                    continue

                if request:
                    print(f"\n🚀 AUTONOMOUS EXECUTION STARTING...")
                    success = self.execute_autonomous_task(request)

                    if success:
                        print("\n🎉 TASK COMPLETED SUCCESSFULLY!")
                    else:
                        print("\n⚠️ Task completed with issues or failed")

                    # Add to history
                    self.task_history.append({
                        'request': request,
                        'success': success,
                        'timestamp': time.time()
                    })

            except KeyboardInterrupt:
                print("\n👋 Goodbye!")
                break
            except Exception as e:
                print(f"❌ Error: {e}")

        # Show session summary
        if self.task_history:
            successful = len([t for t in self.task_history if t['success']])
            total = len(self.task_history)
            print(f"\n📊 Session Summary: {successful}/{total} tasks completed successfully")

    def run_demo_tasks(self):
        """Run demonstration of agentic capabilities"""
        print("\n🎭 AGENTIC SYSTEM DEMONSTRATION")
        print("=" * 40)

        demo_tasks = [
            "Take a screenshot and analyze what's on screen",
            "Open calculator application",
            "Open notepad text editor"
        ]

        for i, task in enumerate(demo_tasks, 1):
            print(f"\n🎬 Demo {i}/{len(demo_tasks)}: {task}")
            proceed = input("Press Enter to continue (or 'skip' to skip): ").strip()

            if proceed.lower() == 'skip':
                continue

            success = self.execute_autonomous_task(task)
            print(f"Result: {'✅ Success' if success else '❌ Failed'}")

            if i < len(demo_tasks):
                time.sleep(2)  # Brief pause between demos


def main():
    """Main entry point for the Ultimate Agentic System"""
    print("🧠 ULTIMATE AGENTIC SYSTEM")
    print("Fully autonomous AI agent with real tools and understanding")
    print()

    try:
        # Initialize the system
        agent = UltimateAgenticSystem()

        # Run in agentic mode
        agent.run_agentic_mode()

    except KeyboardInterrupt:
        print("\n👋 Goodbye!")
    except Exception as e:
        print(f"❌ Fatal error: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
