
# ACCURATE BUSINESS COMMANDS (GPT-4o - Slower but More Capable)

# Complex business tasks
agent_s2 --provider "openai" --model "gpt-4o" --grounding_model_provider "openai" --grounding_model "gpt-4o" --grounding_model_resize_width 1366

# Example complex tasks:
# "Open Excel, create a financial dashboard with charts and formulas"
# "Open Outlook, organize emails by priority and create calendar events"
# "Navigate to our CRM system and update customer information"
# "Create a comprehensive project report with data from multiple sources"
